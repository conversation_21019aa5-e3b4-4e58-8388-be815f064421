const Applicant = require('../models/Applicant');

// @desc    Submit a new job application
// @route   POST /api/applicants
// @access  Public
exports.submitApplication = async (req, res) => {
  try {
    const application = await Applicant.create(req.body);
    
    res.status(201).json({
      success: true,
      data: application
    });
  } catch (error) {
    console.error('Error submitting application:', error);
    
    // Handle validation errors
    if (error.name === 'ValidationError') {
      const messages = Object.values(error.errors).map(val => val.message);
      return res.status(400).json({
        success: false,
        error: messages
      });
    }
    
    res.status(500).json({
      success: false,
      error: 'Server Error'
    });
  }
};

// @desc    Get all applications
// @route   GET /api/applicants
// @access  Private (Admin only)
exports.getApplications = async (req, res) => {
  try {
    const applications = await Applicant.find();
    
    res.status(200).json({
      success: true,
      count: applications.length,
      data: applications
    });
  } catch (error) {
    console.error('Error fetching applications:', error);
    res.status(500).json({
      success: false,
      error: 'Server Error'
    });
  }
};

// @desc    Get single application
// @route   GET /api/applicants/:id
// @access  Private (Admin only)
exports.getApplication = async (req, res) => {
  try {
    const application = await Applicant.findById(req.params.id);
    
    if (!application) {
      return res.status(404).json({
        success: false,
        error: 'Application not found'
      });
    }
    
    res.status(200).json({
      success: true,
      data: application
    });
  } catch (error) {
    console.error('Error fetching application:', error);
    res.status(500).json({
      success: false,
      error: 'Server Error'
    });
  }
};

// @desc    Update application status
// @route   PUT /api/applicants/:id
// @access  Private (Admin only)
exports.updateApplicationStatus = async (req, res) => {
  try {
    const { status } = req.body;
    
    if (!status) {
      return res.status(400).json({
        success: false,
        error: 'Please provide a status'
      });
    }
    
    let application = await Applicant.findById(req.params.id);
    
    if (!application) {
      return res.status(404).json({
        success: false,
        error: 'Application not found'
      });
    }
    
    application = await Applicant.findByIdAndUpdate(
      req.params.id,
      { status },
      { new: true, runValidators: true }
    );
    
    res.status(200).json({
      success: true,
      data: application
    });
  } catch (error) {
    console.error('Error updating application status:', error);
    res.status(500).json({
      success: false,
      error: 'Server Error'
    });
  }
};
