{"name": "gettwistedlocs-frontend", "version": "1.0.0", "description": "Frontend for GetTwisted Hair Studios", "main": "index.js", "scripts": {"start": "http-server ./public -p 3000", "dev": "http-server ./public -p 3000", "build": "echo \"No build step required for static site\" && exit 0", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["hair", "salon", "frontend"], "author": "GetTwisted Hair Studios", "license": "ISC", "dependencies": {"http-server": "^14.1.1"}, "devDependencies": {"tailwindcss": "^3.3.3"}}