// Main JavaScript for GetTwisted Hair Studios

// Initialize AOS (Animate on Scroll) and setup page
document.addEventListener('DOMContentLoaded', () => {
  // Initialize AOS
  if (typeof AOS !== 'undefined') {
    AOS.init();
  }

  // Theme initialization
  const savedTheme = localStorage.getItem('selectedTheme') || 'vibrant';

  // Remove any existing theme classes first
  document.body.classList.remove('theme-vibrant', 'theme-black-white', 'theme-matrix');

  // Add the saved theme
  document.body.classList.add(`theme-${savedTheme}`);

  // Set theme toggle button text if it exists
  const themeToggleBtn = document.getElementById('themeToggleBtn');
  if (themeToggleBtn) {
    themeToggleBtn.innerText = '🎨';
  }

  // Mobile menu functionality is now handled by inline onclick attribute
});

// Theme toggle functionality
const themes = ['vibrant', 'black-white', 'matrix'];

function toggleTheme() {
  // Find current theme
  let currentTheme = -1;

  for (let i = 0; i < themes.length; i++) {
    if (document.body.classList.contains(`theme-${themes[i]}`)) {
      currentTheme = i;
      break;
    }
  }

  // If no theme is found, default to vibrant
  if (currentTheme === -1) {
    currentTheme = 0; // vibrant
  }

  // Remove all theme classes
  document.body.classList.remove('theme-vibrant', 'theme-black-white', 'theme-matrix');

  // Move to next theme
  currentTheme = (currentTheme + 1) % themes.length;

  // Add new theme class
  document.body.classList.add(`theme-${themes[currentTheme]}`);

  // Save to localStorage
  localStorage.setItem('selectedTheme', themes[currentTheme]);

  console.log(`Theme changed to: ${themes[currentTheme]}`);
}

// Chatbot functionality
function toggleChat() {
  const chat = document.getElementById('chatbot');
  if (chat) {
    chat.classList.toggle('hidden');
    chat.classList.toggle('animate-slide-in');
  }
}

// Booking modal functionality is now handled in booking.js

// Mobile menu toggle functionality is now handled by inline onclick attribute
