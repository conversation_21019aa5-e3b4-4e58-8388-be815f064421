// Main JavaScript for GetTwisted Hair Studios

// Global variables
const themes = ['vibrant', 'black-white', 'matrix'];

// Initialize AOS (Animate on Scroll) and setup page
document.addEventListener('DOMContentLoaded', () => {
  // Initialize AOS
  if (typeof AOS !== 'undefined') {
    AOS.init();
  }

  // Theme initialization
  initializeTheme();

  // Mobile menu initialization
  initializeMobileMenu();

  // Set theme toggle button text if it exists
  const themeToggleBtn = document.getElementById('themeToggleBtn');
  if (themeToggleBtn) {
    themeToggleBtn.innerText = '🎨';
  }
});

// Theme initialization function
function initializeTheme() {
  const savedTheme = localStorage.getItem('selectedTheme') || 'vibrant';

  // Remove any existing theme classes first
  document.body.classList.remove('theme-vibrant', 'theme-black-white', 'theme-matrix');

  // Add the saved theme
  document.body.classList.add(`theme-${savedTheme}`);
}

// Mobile menu initialization function
function initializeMobileMenu() {
  const mobileMenuBtn = document.getElementById('mobileMenuBtn');
  const mobileMenuToggle = document.getElementById('mobileMenuToggle');
  const mobileMenu = document.getElementById('mobileMenu');
  const closeMenu = document.getElementById('closeMenu');

  // Handle mobile menu button (dropdown style)
  if (mobileMenuBtn && mobileMenu) {
    mobileMenuBtn.addEventListener('click', function(e) {
      e.preventDefault();
      e.stopPropagation();
      mobileMenu.classList.toggle('hidden');
    });
  }

  // Handle mobile menu toggle (full-screen style)
  if (mobileMenuToggle && mobileMenu) {
    mobileMenuToggle.addEventListener('click', function(e) {
      e.preventDefault();
      e.stopPropagation();
      mobileMenu.classList.remove('hidden');
    });
  }

  // Handle close menu button
  if (closeMenu && mobileMenu) {
    closeMenu.addEventListener('click', function(e) {
      e.preventDefault();
      e.stopPropagation();
      mobileMenu.classList.add('hidden');
    });
  }

  // Close mobile menu when clicking outside (for dropdown style)
  document.addEventListener('click', function(e) {
    if (mobileMenu && !mobileMenu.contains(e.target) && !mobileMenuBtn?.contains(e.target) && !mobileMenuToggle?.contains(e.target)) {
      mobileMenu.classList.add('hidden');
    }
  });

  // Close mobile menu on escape key
  document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape' && mobileMenu && !mobileMenu.classList.contains('hidden')) {
      mobileMenu.classList.add('hidden');
    }
  });
}

// Theme toggle functionality
function toggleTheme() {
  // Find current theme
  let currentTheme = -1;

  for (let i = 0; i < themes.length; i++) {
    if (document.body.classList.contains(`theme-${themes[i]}`)) {
      currentTheme = i;
      break;
    }
  }

  // If no theme is found, default to vibrant
  if (currentTheme === -1) {
    currentTheme = 0; // vibrant
  }

  // Remove all theme classes
  document.body.classList.remove('theme-vibrant', 'theme-black-white', 'theme-matrix');

  // Move to next theme
  currentTheme = (currentTheme + 1) % themes.length;

  // Add new theme class
  document.body.classList.add(`theme-${themes[currentTheme]}`);

  // Save to localStorage
  localStorage.setItem('selectedTheme', themes[currentTheme]);

  console.log(`Theme changed to: ${themes[currentTheme]}`);
}

// Chat toggle functionality
function toggleChat() {
  const chat = document.getElementById('chatbot');
  if (chat) {
    chat.classList.toggle('hidden');
    chat.classList.toggle('animate-slide-in');
  }
}

// Duplicate function removed - using the one above

// Booking modal functionality is now handled in booking.js

// Mobile menu toggle functionality is now handled by inline onclick attribute
