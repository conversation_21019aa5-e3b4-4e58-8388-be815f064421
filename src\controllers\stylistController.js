const Stylist = require('../models/Stylist');

// @desc    Get all stylists
// @route   GET /api/stylists
// @access  Public
exports.getStylists = async (req, res, next) => {
  try {
    const stylists = await Stylist.find({ isActive: true });
    
    res.status(200).json({
      success: true,
      count: stylists.length,
      data: stylists
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Get single stylist
// @route   GET /api/stylists/:id
// @access  Public
exports.getStylist = async (req, res, next) => {
  try {
    const stylist = await Stylist.findById(req.params.id);
    
    if (!stylist) {
      return res.status(404).json({
        success: false,
        error: 'Stylist not found'
      });
    }

    res.status(200).json({
      success: true,
      data: stylist
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Create new stylist
// @route   POST /api/stylists
// @access  Private
exports.createStylist = async (req, res, next) => {
  try {
    const stylist = await Stylist.create(req.body);

    res.status(201).json({
      success: true,
      data: stylist
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Update stylist
// @route   PUT /api/stylists/:id
// @access  Private
exports.updateStylist = async (req, res, next) => {
  try {
    const stylist = await Stylist.findByIdAndUpdate(
      req.params.id, 
      req.body, 
      { 
        new: true,
        runValidators: true 
      }
    );

    if (!stylist) {
      return res.status(404).json({
        success: false,
        error: 'Stylist not found'
      });
    }

    res.status(200).json({
      success: true,
      data: stylist
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Delete stylist
// @route   DELETE /api/stylists/:id
// @access  Private
exports.deleteStylist = async (req, res, next) => {
  try {
    const stylist = await Stylist.findById(req.params.id);

    if (!stylist) {
      return res.status(404).json({
        success: false,
        error: 'Stylist not found'
      });
    }

    // Instead of deleting, set isActive to false
    stylist.isActive = false;
    await stylist.save();

    res.status(200).json({
      success: true,
      data: {}
    });
  } catch (error) {
    next(error);
  }
};
