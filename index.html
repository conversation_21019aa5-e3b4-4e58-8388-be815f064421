<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>GetTwisted Hair Studios</title>

  <!-- SEO & Social Meta -->
  <meta name="description" content="GetTwisted Hair Studios – Locs, Braids & Natural Hair Specialists in Philly.">
  <meta property="og:title" content="GetTwisted Hair Studios" />
  <meta property="og:description" content="Book your natural hair appointment today." />
  <meta property="og:image" content="https://www.gettwistedloc.com/assets/images/hero.jpg" />
  <meta property="og:url" content="https://www.gettwistedloc.com" />

  <!-- Security Headers -->
  <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline' https://cdn.tailwindcss.com https://unpkg.com https://www.googletagmanager.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://unpkg.com; font-src 'self' https://fonts.gstatic.com; frame-src https://get-twisted-hair-studio.square.site/ https://*.square.site/ https://*.squareup.com/; connect-src https://*.square.site/ https://*.squareup.com/; img-src 'self' https://*.square.site/ https://*.squareup.com/ data:; object-src 'none';">
  <meta http-equiv="X-Content-Type-Options" content="nosniff">
  <meta http-equiv="Referrer-Policy" content="no-referrer-when-downgrade">
  <!-- Removed X-Frame-Options to allow iframe embedding -->
  <meta http-equiv="Permissions-Policy" content="geolocation=(), microphone=(), camera=()">

  <!-- Tailwind CSS -->
  <script src="https://cdn.tailwindcss.com"></script>
  <!-- Fonts + AOS -->
  <link href="https://fonts.googleapis.com/css2?family=Fredoka+One&display=swap" rel="stylesheet">
  <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">

  <!-- Google tag (gtag.js) -->
  <script async src="https://www.googletagmanager.com/gtag/js?id=G-QMXCEDX3LX"></script>
  <script>
    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);}
    gtag('js', new Date());
    gtag('config', 'G-QMXCEDX3LX');
  </script>

  <!-- Custom CSS -->
  <link href="css/main.css" rel="stylesheet">

  <style>
    /* Gallery Slider Styles */
    #gallery-slider {
      width: 100%;
      padding: 0 40px;
    }

    #slider-track {
      width: 100%;
    }

    .slider-item {
      transition: transform 0.3s ease;
    }

    .slider-item:hover {
      transform: translateY(-5px);
    }

    /* TikTok Slider Styles */
    #tiktok-slider-track {
      width: 100%;
    }

    .tiktok-slider-item {
      transition: transform 0.3s ease;
    }

    .tiktok-slider-item:hover {
      transform: translateY(-5px);
    }

    /* Animation for TikTok-style logo */
    @keyframes ping {
      0% {
        transform: scale(0.8);
        opacity: 0.8;
      }
      70%, 100% {
        transform: scale(1.7);
        opacity: 0;
      }
    }

    .animate-ping {
      animation: ping 2s cubic-bezier(0, 0, 0.2, 1) infinite;
    }

    @media (max-width: 640px) {
      #gallery-slider, #tiktok-slider-track {
        padding: 0 30px;
      }
    }
  </style>
</head>

<!-- Navbar -->
<header class="sticky top-0 z-50 p-6 shadow-md flex flex-wrap items-center bg-pink-600">
  <!-- Logo -->
  <a href="index.html" class="text-3xl font-extrabold tracking-wide flex-shrink-0 mr-auto text-yellow-300">
    GETTWISTED
    <span class="block text-sm md:text-lg text-cyan-300">HAIR STUDIOS</span>
  </a>

  <!-- Desktop Nav -->
  <nav id="navMenu" class="hidden md:flex space-x-4 text-sm md:text-base items-center">
    <a href="index.html" class="hover:underline">Home</a>
    <a href="stylists.html" class="hover:underline">Stylists</a>
    <a href="services.html" class="hover:underline">Services</a>
    <a href="gallery.html" class="hover:underline">Gallery</a>
    <a href="careers.html" class="hover:underline">Careers</a>
    <a href="help.html" class="hover:underline">Help</a>
    <button onclick="toggleTheme()" id="themeToggleBtn" class="action-btn theme-btn" title="Change Theme">🎨</button>
    <button onclick="toggleChat()" class="action-btn chat-btn" title="Chat with Us">💬</button>
    <a href="sms:+16102880343" class="action-btn sms-btn" title="Text Us">📱</a>
  </nav>

  <!-- Mobile Toggle -->
  <button id="mobileMenuBtn" class="md:hidden bg-white text-black px-4 py-2 rounded-full shadow-lg font-bold ml-4 text-xl">
   ☰
  </button>

  <!-- Mobile Dropdown -->
  <div id="mobileMenu" class="w-full mt-4 hidden md:hidden bg-pink-600 py-3 px-2 rounded-lg shadow-lg">
    <nav class="flex flex-wrap justify-center items-center gap-4 text-sm text-white">
      <a href="index.html" class="hover:underline">Home</a>
      <a href="stylists.html" class="hover:underline">Stylists</a>
      <a href="services.html" class="hover:underline">Services</a>
      <a href="gallery.html" class="hover:underline">Gallery</a>
      <a href="careers.html" class="hover:underline">Careers</a>
      <a href="help.html" class="hover:underline">Help</a>
      <div class="flex gap-3 mt-3 sm:mt-0">
        <button onclick="toggleTheme()" class="action-btn mobile-action-btn theme-btn" title="Change Theme">🎨</button>
        <button onclick="toggleChat()" class="action-btn mobile-action-btn chat-btn" title="Chat with Us">💬</button>
        <a href="sms:+16102880343" class="action-btn mobile-action-btn sms-btn" title="Text Us">📱</a>
      </div>
    </nav>
  </div>

</header>

  <!-- Hero -->
  <section class="hero-section flex flex-col md:flex-row items-center justify-center md:justify-between px-6 md:px-20 py-12 md:py-20 gap-10 text-center md:text-left">
    <div class="flex-1" data-aos="fade-right">
      <h2 class="text-4xl md:text-6xl font-bold mb-6">Embrace<br>Your Natural<br>Hair</h2>
      <button onclick="toggleBookingModal()" class="bg-yellow-300 text-pink-700 px-6 py-3 text-lg rounded-full font-semibold shadow hover:bg-yellow-400 transition">
        Book Appointment
      </button>
    </div>
    <div class="flex-1 max-w-md md:max-w-lg" data-aos="fade-left">
      <img src="assets/images/hero.jpg" alt="Hero Image" class="rounded-xl shadow-lg w-full object-cover max-h-[500px]" />
    </div>
  </section>

  <!-- Flyer Gallery Section -->
  <section class="bg-white py-16 px-6 text-center" data-aos="fade-up">
    <h2 class="text-3xl md:text-4xl font-bold uppercase mb-8 theme-text">
      What's Poppin' at GetTwisted?
    </h2>
    <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6 max-w-6xl mx-auto">
      <!-- Flyer 1 -->
      <div class="rounded-xl overflow-hidden shadow-lg" data-aos="zoom-in">
        <img src="assets/images/gettwisted_briads_flyer.png" alt="Braiding Flyer" class="w-full h-auto object-cover" />
      </div>

      <!-- Flyer 2 -->
      <div class="rounded-xl overflow-hidden shadow-lg" data-aos="zoom-in" data-aos-delay="100">
        <img src="assets/images/gettwisted_barber_flyer.png" alt="Barber Flyer" class="w-full h-auto object-cover" />
      </div>

      <!-- Flyer 3 -->
      <div class="rounded-xl overflow-hidden shadow-lg" data-aos="zoom-in" data-aos-delay="200">
        <img src="assets/images/gettwisted_locs_flyer.png" alt="Locs Flyer" class="w-full h-auto object-cover" />
      </div>

    </div>
  </section>

  <!-- Recent Styles Gallery with Horizontal Slider -->
  <section class="bg-white py-16 px-6 text-center" data-aos="fade-up">
    <h2 class="text-3xl md:text-4xl font-bold uppercase mb-4 theme-text">
      Our Recent Styles
    </h2>
    <p class="mb-8"><a href="gallery.html" class="text-pink-600 hover:text-pink-700 underline">View our full gallery →</a></p>

    <!-- Gallery Slider -->
    <div class="max-w-6xl mx-auto mb-8">
      <div class="relative">
        <div id="gallery-slider" class="overflow-hidden">
          <div id="slider-track" class="flex transition-transform duration-500 ease-in-out">
            <!-- Slider items will be loaded here via JavaScript -->
          </div>
        </div>
        <button id="slider-prev" class="absolute left-0 top-1/2 transform -translate-y-1/2 bg-pink-600 text-white rounded-full p-2 shadow-lg z-10 hover:bg-pink-700 transition">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
          </svg>
        </button>
        <button id="slider-next" class="absolute right-0 top-1/2 transform -translate-y-1/2 bg-pink-600 text-white rounded-full p-2 shadow-lg z-10 hover:bg-pink-700 transition">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
          </svg>
        </button>
      </div>
      <div id="slider-indicators" class="flex justify-center mt-4 space-x-2">
        <!-- Indicators will be added dynamically -->
      </div>
    </div>
  </section>

<!-- TikTok Videos Section -->
<section class="bg-pink-100 py-16 px-6 text-center" data-aos="fade-up">
  <div class="max-w-6xl mx-auto">
    <h2 class="text-3xl md:text-4xl font-bold uppercase mb-8 theme-text">Watch the Styles in Action</h2>

    <div class="relative">
      <!-- Slider Container -->
      <div class="overflow-hidden">
        <div id="tiktok-slider-track" class="flex transition-transform duration-500 ease-in-out">
          <!-- TikTok video items will be added by JavaScript -->
        </div>
      </div>

      <!-- Slider Navigation -->
      <button id="tiktok-slider-prev" class="absolute left-0 top-1/2 transform -translate-y-1/2 bg-pink-600 text-white rounded-full p-2 shadow-lg z-10 hover:bg-pink-700 transition">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
        </svg>
      </button>
      <button id="tiktok-slider-next" class="absolute right-0 top-1/2 transform -translate-y-1/2 bg-pink-600 text-white rounded-full p-2 shadow-lg z-10 hover:bg-pink-700 transition">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
        </svg>
      </button>
    </div>

    <!-- Slider Indicators -->
    <div id="tiktok-slider-indicators" class="flex justify-center mt-6 space-x-2">
      <!-- Indicators will be added by JavaScript -->
    </div>

    <!-- Follow on TikTok Button -->
    <div class="mt-8">
      <a href="https://www.tiktok.com/@therealsashafearless" target="_blank" class="inline-flex items-center bg-black text-white px-6 py-3 rounded-full hover:bg-gray-800 transition">
        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 448 512" class="mr-2 fill-current">
          <path d="M448,209.91a210.06,210.06,0,0,1-122.77-39.25V349.38A162.55,162.55,0,1,1,185,188.31V278.2a74.62,74.62,0,1,0,52.23,71.18V0l88,0a121.18,121.18,0,0,0,1.86,22.17h0A122.18,122.18,0,0,0,381,102.39a121.43,121.43,0,0,0,67,20.14Z"/>
        </svg>
        Follow Sasha on TikTok
      </a>
    </div>
  </div>
</section>

<!-- TikTok Video Modal -->
<div id="tiktok-video-modal" class="fixed inset-0 z-50 hidden bg-black bg-opacity-75 flex items-center justify-center p-4">
  <div class="bg-white rounded-xl max-w-2xl w-full mx-auto shadow-2xl">
    <div class="flex justify-between items-center px-6 py-4 bg-pink-600 text-white rounded-t-xl">
      <div>
        <h3 id="tiktok-video-title" class="text-lg font-bold">Video Title</h3>
        <p id="tiktok-video-username" class="text-sm">@username</p>
      </div>
      <button id="close-tiktok-modal" class="text-2xl leading-none hover:text-yellow-300">&times;</button>
    </div>
    <div class="p-6">
      <div id="tiktok-video-container" class="flex justify-center">
        <!-- TikTok embed will be added here by JavaScript -->
      </div>
    </div>
  </div>
</div>

  <!-- Feature Tiles -->
  <section class="bg-yellow-300 px-6 py-16 text-black">
    <div class="max-w-6xl mx-auto grid grid-cols-1 md:grid-cols-3 gap-6">
      <a href="https://get-twisted-hair-studio.square.site/" target="_blank" class="bg-orange-500 hover:bg-orange-600 transition rounded-xl shadow-lg p-6 text-center text-white" data-aos="zoom-in">
        <svg xmlns="http://www.w3.org/2000/svg" class="w-14 h-14 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
          <path stroke-linecap="round" stroke-linejoin="round" d="M17 20h5v-2a4 4 0 00-4-4h-1M9 20H4v-2a4 4 0 014-4h1m6-6a4 4 0 11-8 0 4 4 0 018 0zm6 4a4 4 0 11-8 0 4 4 0 018 0z" />
        </svg>
        <h3 class="text-xl font-bold uppercase">Our Stylists</h3>
        <p class="mt-2 text-sm">Meet & book with your favorite</p>
      </a>
      <a href="services.html" class="bg-teal-500 hover:bg-teal-600 transition rounded-xl shadow-lg p-6 text-center text-white" data-aos="zoom-in" data-aos-delay="100">
        <svg xmlns="http://www.w3.org/2000/svg" class="w-14 h-14 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
          <path stroke-linecap="round" stroke-linejoin="round" d="M14 10h4v4h-4v4h-4v-4H6v-4h4V6h4v4z" />
        </svg>
        <h3 class="text-xl font-bold uppercase">Services</h3>
        <p class="mt-2 text-sm">Twists, Locs, Braids & more</p>
      </a>
      <a href="gallery.html" class="bg-pink-500 hover:bg-pink-600 transition rounded-xl shadow-lg p-6 text-center text-white" data-aos="zoom-in" data-aos-delay="200">
        <svg xmlns="http://www.w3.org/2000/svg" class="w-14 h-14 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
          <path stroke-linecap="round" stroke-linejoin="round" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
        </svg>
        <h3 class="text-xl font-bold uppercase">Gallery</h3>
        <p class="mt-2 text-sm">View our latest styles</p>
      </a>
    </div>
  </section>

  <!-- Hair Tips -->
  <section class="bg-pink-600 py-16 px-6 text-white" data-aos="fade-up">
    <div class="max-w-5xl mx-auto text-center mb-10">
      <h2 class="text-3xl md:text-4xl font-bold uppercase theme-text">
        Hair Tips of the Week
      </h2>
      <p class="mt-2 text-base theme-subtext">
        Boost your natural hair game with these juicy pro tips!
      </p>
    </div>
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-6xl mx-auto">
      <div class="bg-white text-black rounded-xl p-6 shadow-lg" data-aos="fade-up">
        <h3 class="font-bold text-lg mb-2">🧴 Moisturize Daily</h3>
        <p class="text-sm leading-relaxed">Use a water-based leave-in conditioner every 1–2 days to keep curls hydrated and soft.</p>
      </div>
      <div class="bg-white text-black rounded-xl p-6 shadow-lg" data-aos="fade-up" data-aos-delay="100">
        <h3 class="font-bold text-lg mb-2">🧖🏾‍♀️ Deep Condition Weekly</h3>
        <p class="text-sm leading-relaxed">Weekly deep treatments lock in moisture and reduce breakage.</p>
      </div>
      <div class="bg-white text-black rounded-xl p-6 shadow-lg" data-aos="fade-up" data-aos-delay="200">
        <h3 class="font-bold text-lg mb-2">🧼 Sleep in Silk</h3>
        <p class="text-sm leading-relaxed">Use a silk bonnet or pillowcase to protect your style overnight.</p>
      </div>
    </div>
  </section>

  <!-- Google Reviews Slideshow -->
  <section class="px-6 py-16 bg-pink-600 text-white">
    <div class="max-w-4xl mx-auto text-center">
      <h2 class="text-3xl md:text-4xl font-bold mb-2">What Our Clients Say</h2>
      <p class="mb-8">Real reviews from our Google Business profiles</p>

      <div class="bg-white text-black rounded-xl p-6 shadow-lg max-w-2xl mx-auto transition-opacity duration-300" id="reviewsContainer">
        <!-- Reviews will be loaded here by JavaScript -->
      </div>

      <!-- Review Navigation -->
      <div class="flex justify-center items-center mt-6 space-x-4">
        <button onclick="prevReview()" class="bg-white text-pink-600 rounded-full w-10 h-10 flex items-center justify-center shadow-lg hover:bg-gray-100 transition">
          <span class="sr-only">Previous</span>
          ←
        </button>

        <div id="reviewIndicators" class="flex space-x-2">
          <!-- Indicators will be added by JavaScript -->
        </div>

        <button onclick="nextReview()" class="bg-white text-pink-600 rounded-full w-10 h-10 flex items-center justify-center shadow-lg hover:bg-gray-100 transition">
          <span class="sr-only">Next</span>
          →
        </button>
      </div>
    </div>
  </section>

  <!-- Booking Modal -->
  <div id="bookingModal" class="fixed inset-0 z-50 hidden bg-black bg-opacity-60 flex items-center justify-center overflow-y-auto py-10">
    <div class="bg-white rounded-xl max-w-4xl w-full mx-4 md:mx-0 shadow-lg relative">
      <div class="flex justify-between items-center px-6 py-4 bg-pink-600 text-white rounded-t-xl">
        <h3 class="text-lg font-bold">Book Your Appointment</h3>
        <button onclick="toggleBookingModal()" class="text-2xl leading-none hover:text-yellow-300">&times;</button>
      </div>

      <!-- Multi-step booking form -->
      <div class="p-6" id="bookingContainer">
        <!-- Step indicators -->
        <div class="flex justify-center mb-8">
          <div class="flex items-center">
            <div id="step1Indicator" class="w-8 h-8 rounded-full bg-pink-600 text-white flex items-center justify-center font-bold">1</div>
            <div class="w-12 h-1 bg-gray-300" id="line1to2"></div>
            <div id="step2Indicator" class="w-8 h-8 rounded-full bg-gray-300 text-gray-600 flex items-center justify-center font-bold">2</div>
            <div class="w-12 h-1 bg-gray-300" id="line2to3"></div>
            <div id="step3Indicator" class="w-8 h-8 rounded-full bg-gray-300 text-gray-600 flex items-center justify-center font-bold">3</div>
          </div>
        </div>

        <!-- Step 1: Personal Information -->
        <div id="bookingStep1" class="booking-step">
          <h4 class="text-xl font-bold mb-6 text-center">Your Information</h4>
          <form id="personalInfoForm" class="max-w-md mx-auto">
            <div class="mb-4">
              <label for="fullName" class="block text-sm font-medium text-gray-700 mb-1">Full Name</label>
              <input type="text" id="fullName" name="fullName" class="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-pink-500 focus:border-pink-500" required>
            </div>
            <div class="mb-4">
              <label for="email" class="block text-sm font-medium text-gray-700 mb-1">Email Address</label>
              <input type="email" id="email" name="email" class="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-pink-500 focus:border-pink-500" required>
            </div>
            <div class="mb-4">
              <label for="phone" class="block text-sm font-medium text-gray-700 mb-1">Phone Number</label>
              <input type="tel" id="phone" name="phone" class="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-pink-500 focus:border-pink-500" required>
            </div>
            <div class="mb-4">
              <label for="location" class="block text-sm font-medium text-gray-700 mb-1">Preferred Location</label>
              <select id="location" name="location" class="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-pink-500 focus:border-pink-500" required>
                <option value="">Select a location</option>
                <option value="teaneck">Teaneck, NJ</option>
                <option value="pottstown">Pottstown, PA</option>
              </select>
            </div>
            <div class="mt-6 flex justify-end">
              <button type="button" onclick="goToStep(2)" class="bg-pink-600 hover:bg-pink-700 text-white font-bold py-2 px-6 rounded-full transition-colors">Next Step</button>
            </div>
          </form>
        </div>

        <!-- Step 2: Service Selection -->
        <div id="bookingStep2" class="booking-step hidden">
          <h4 class="text-xl font-bold mb-6 text-center">Select Your Service</h4>
          <div class="max-w-2xl mx-auto">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
              <!-- Service Categories -->
              <div class="bg-pink-50 p-4 rounded-lg cursor-pointer service-category" onclick="selectServiceCategory('locs')" id="locsCategory">
                <h5 class="font-bold text-lg mb-2">Locs</h5>
                <p class="text-sm">Starter locs, retwists, maintenance, styling</p>
              </div>
              <div class="bg-pink-50 p-4 rounded-lg cursor-pointer service-category" onclick="selectServiceCategory('braids')" id="braidsCategory">
                <h5 class="font-bold text-lg mb-2">Braids</h5>
                <p class="text-sm">Box braids, knotless braids, twists, cornrows</p>
              </div>
              <div class="bg-pink-50 p-4 rounded-lg cursor-pointer service-category" onclick="selectServiceCategory('cuts')" id="cutsCategory">
                <h5 class="font-bold text-lg mb-2">Haircuts</h5>
                <p class="text-sm">Cuts, fades, shape-ups, beard trims</p>
              </div>
              <div class="bg-pink-50 p-4 rounded-lg cursor-pointer service-category" onclick="selectServiceCategory('color')" id="colorCategory">
                <h5 class="font-bold text-lg mb-2">Color</h5>
                <p class="text-sm">Full color, highlights, balayage</p>
              </div>
            </div>

            <!-- Specific Services (will be populated by JavaScript) -->
            <div id="specificServices" class="mb-6 hidden">
              <h5 class="font-bold mb-3">Select Specific Service:</h5>
              <select id="serviceSelect" class="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-pink-500 focus:border-pink-500">
                <!-- Options will be added dynamically -->
              </select>
            </div>

            <div class="flex justify-between mt-8">
              <button type="button" onclick="goToStep(1)" class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-6 rounded-full transition-colors">Back</button>
              <button type="button" onclick="goToStep(3)" class="bg-pink-600 hover:bg-pink-700 text-white font-bold py-2 px-6 rounded-full transition-colors">Next Step</button>
            </div>
          </div>
        </div>

        <!-- Step 3: Date & Time Selection -->
        <div id="bookingStep3" class="booking-step hidden">
          <h4 class="text-xl font-bold mb-6 text-center">Select Date & Time</h4>
          <div class="max-w-2xl mx-auto">
            <!-- Date Selection -->
            <div class="mb-6">
              <label for="appointmentDate" class="block text-sm font-medium text-gray-700 mb-2">Preferred Date</label>
              <input type="date" id="appointmentDate" name="appointmentDate" class="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-pink-500 focus:border-pink-500" required>
            </div>

            <!-- Time Selection -->
            <div class="mb-6">
              <label for="appointmentTime" class="block text-sm font-medium text-gray-700 mb-2">Preferred Time</label>
              <select id="appointmentTime" name="appointmentTime" class="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-pink-500 focus:border-pink-500" required>
                <option value="">Select a time</option>
                <option value="9:00">9:00 AM</option>
                <option value="10:00">10:00 AM</option>
                <option value="11:00">11:00 AM</option>
                <option value="12:00">12:00 PM</option>
                <option value="13:00">1:00 PM</option>
                <option value="14:00">2:00 PM</option>
                <option value="15:00">3:00 PM</option>
                <option value="16:00">4:00 PM</option>
                <option value="17:00">5:00 PM</option>
              </select>
            </div>

            <!-- Additional Notes -->
            <div class="mb-6">
              <label for="notes" class="block text-sm font-medium text-gray-700 mb-2">Additional Notes (Optional)</label>
              <textarea id="notes" name="notes" rows="3" class="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-pink-500 focus:border-pink-500"></textarea>
            </div>

            <div class="flex justify-between mt-8">
              <button type="button" onclick="goToStep(2)" class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-6 rounded-full transition-colors">Back</button>
              <button type="button" onclick="submitBooking()" class="bg-pink-600 hover:bg-pink-700 text-white font-bold py-2 px-6 rounded-full transition-colors">Complete Booking</button>
            </div>
          </div>
        </div>

        <!-- Confirmation Step -->
        <div id="bookingConfirmation" class="booking-step hidden">
          <div class="text-center py-8">
            <div class="w-16 h-16 bg-green-100 text-green-600 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
              </svg>
            </div>
            <h4 class="text-2xl font-bold mb-4">Booking Confirmed!</h4>
            <p class="mb-6">Thank you for booking with GetTwisted Hair Studios. We've sent a confirmation to your email.</p>
            <div class="bg-gray-50 p-6 rounded-lg max-w-md mx-auto mb-6">
              <h5 class="font-bold mb-4 text-left">Booking Details:</h5>
              <div class="text-left space-y-2">
                <p><span class="font-medium">Name:</span> <span id="confirmName"></span></p>
                <p><span class="font-medium">Service:</span> <span id="confirmService"></span></p>
                <p><span class="font-medium">Location:</span> <span id="confirmLocation"></span></p>
                <p><span class="font-medium">Date & Time:</span> <span id="confirmDateTime"></span></p>
              </div>
            </div>
            <p class="text-sm text-gray-600 mb-6">If you need to make any changes to your appointment, please call us at (610) 288-0343.</p>
            <button onclick="toggleBookingModal()" class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-6 rounded-full transition-colors">Close</button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Chatbot -->
  <div id="chatbot" class="fixed bottom-28 right-5 bg-white w-80 rounded-xl shadow-lg overflow-hidden text-black z-50 hidden">
    <div class="bg-pink-600 text-white p-4 flex justify-between items-center">
      <h4 class="font-bold text-sm">💬 TwistyBot</h4>
      <button onclick="toggleChat()" class="text-white text-lg font-bold">&times;</button>
    </div>
    <div class="p-4 h-60 overflow-y-auto text-sm space-y-3">
      <div class="bg-pink-100 text-black p-3 rounded-lg w-fit max-w-[80%]">Hi! I'm TwistyBot 💖</div>
      <div class="bg-pink-100 text-black p-3 rounded-lg w-fit max-w-[80%]">How can I help you today?</div>
    </div>
    <div class="flex p-2 border-t">
      <input id="chatInput" type="text" placeholder="Type your question..." class="flex-1 text-sm p-2 outline-none" />
      <button id="sendBtn" class="bg-pink-600 text-white px-3 ml-2 rounded">Send</button>
    </div>
  </div>

<!-- Footer -->
<footer class="px-4 sm:px-6 py-10 mt-20 text-center md:text-left">
  <div class="max-w-6xl mx-auto px-4 sm:px-6 grid grid-cols-1 md:grid-cols-3 gap-10 text-sm md:text-base text-center md:text-left">

    <!-- Brand Info -->
    <div>
      <h3 class="text-xl font-bold mb-2">GetTwisted Hair Studios</h3>
      <p>Where natural beauty meets expert care.</p>
      <p class="mt-2 italic">"Twist. Slay. Repeat."</p>
    </div>

    <!-- Contact Info with Multiple Locations -->
    <div>
      <h4 class="font-semibold mb-1">Contact Us</h4>
      <p>📍 Teaneck, NJ</p>
      <p>📍 Pottstown, PA</p>
      <p>📞 (610) 288-0343</p>
      <p>📧 <EMAIL></p>
    </div>

    <!-- Quick Links -->
    <div class="space-y-2">
      <h4 class="font-semibold mb-1">Quick Links</h4>
      <a href="#" onclick="toggleBookingModal(); return false;" class="block hover:underline">Book Appointment</a>
      <a href="stylists.html" class="block hover:underline">Meet Our Stylists</a>
      <a href="services.html" class="block hover:underline">Our Services</a>
      <a href="gallery.html" class="block hover:underline">Our Gallery</a>
      <a href="careers.html" class="block hover:underline">Careers</a>
    </div>
  </div>

  <p class="text-center text-xs footer-copy mt-8">&copy; 2024 GetTwisted Hair Studios. All rights reserved.</p>
</footer>


  <!-- Scripts -->
  <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
  <script src="js/main.js"></script>
  <script src="js/chatbot.js"></script>
  <script src="js/reviews.js"></script>
  <script src="js/booking.js"></script>
  <script src="js/home-slider.js"></script>
  <script src="js/tiktok-slider.js"></script>
  <!-- Legacy script - to be removed after testing -->
  <script>
    // Legacy AOS initialization - now handled in main.js
    if (typeof AOS !== 'undefined') {
      AOS.init();
    }

    // Legacy chat function - now handled in main.js
    function toggleChat() {
      const chat = document.getElementById('chatbot');
      if (chat) {
        chat.classList.toggle('hidden');
        chat.classList.toggle('animate-slide-in');
      }
    }

    // Booking functionality moved to booking.js
    // Theme functionality moved to main.js
  </script>
  <script>
    let chatbotKnowledge = [];

    // Load chatbot Q&A from JSON file
    fetch('assets/data/chatbot-knowledge.json')
      .then(response => response.json())
      .then(data => {
        chatbotKnowledge = data;
        console.log("Chatbot knowledge loaded:", chatbotKnowledge);
      })
      .catch(err => console.error("Error loading chatbot knowledge:", err));

    // Clean text for better matching
    function sanitize(text) {
      return text.toLowerCase().replace(/[^\w\s]/gi, '').trim();
    }

    // Handle chatbot input
    function handleChatInput(inputText) {
      const cleanInput = sanitize(inputText);

      // Try exact match
      let match = chatbotKnowledge.find(item =>
        sanitize(item.question) === cleanInput
      );

      // Fallback to partial match
      if (!match) {
        match = chatbotKnowledge.find(item =>
          cleanInput.includes(sanitize(item.question))
        );
      }

      return match
        ? match.answer
        : "I'm here to help, but I don't have that answer yet. You can text us directly at (************* or visit our booking page to learn more.";
    }

    document.addEventListener("DOMContentLoaded", () => {
      const input = document.getElementById('chatInput');
      const sendBtn = document.getElementById('sendBtn');
      const chatBox = document.querySelector('#chatbot .p-4.h-60');

      function sendMessage() {
        const text = input.value.trim();
        if (!text) return;

        const userBubble = `<div class="bg-yellow-100 text-black p-3 rounded-lg w-fit max-w-[80%] ml-auto">${text}</div>`;
        const botReply = handleChatInput(text);
        const botBubble = `<div class="bg-pink-100 text-black p-3 rounded-lg w-fit max-w-[80%]">${botReply}</div>`;

        chatBox.innerHTML += userBubble + botBubble;
        input.value = '';
        chatBox.scrollTop = chatBox.scrollHeight;
      }

      sendBtn.addEventListener('click', sendMessage);

      input.addEventListener('keydown', (e) => {
        if (e.key === 'Enter') sendMessage();
      });
    });
  </script>

</body>
</html>
