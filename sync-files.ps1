# Script to synchronize files between main directory and frontend/public directory

# List of files to synchronize
$filesToSync = @(
    "careers.html",
    "help.html",
    "index.html",
    "services.html",
    "stylists.html",
    "clientele.html",
    "404.html",
    "CNAME",
    "robots.txt"
)

# Copy files from main directory to frontend/public
foreach ($file in $filesToSync) {
    Write-Host "Copying $file to frontend/public/$file"
    Copy-Item -Path $file -Destination "frontend/public/$file" -Force
}

# Copy CSS files
Write-Host "Copying CSS files..."
Copy-Item -Path "css/*" -Destination "frontend/public/css/" -Force -Recurse

# Copy JS files
Write-Host "Copying JS files..."
Copy-Item -Path "js/*" -Destination "frontend/public/js/" -Force -Recurse

# Copy assets
Write-Host "Copying assets..."
Copy-Item -Path "assets/*" -Destination "frontend/public/assets/" -Force -Recurse

Write-Host "Synchronization complete!"
