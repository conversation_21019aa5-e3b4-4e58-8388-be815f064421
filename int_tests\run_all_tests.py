"""
Comprehensive test runner script for GetTwisted Hair Studios website.
"""
import os
import sys
import subprocess
import argparse
import time
import datetime

# Define test categories
TEST_CATEGORIES = {
    "all": ["test_*.py"],
    "pages": ["test_home.py", "test_gallery.py", "test_services.py", "test_stylists.py", "test_careers.py", "test_help.py"],
    "navigation": ["test_navigation.py"],
    "theme": ["test_theme.py"],
    "mobile": ["test_mobile.py"],
    "forms": ["test_forms.py"],
    "booking": ["test_booking.py"],
    "chatbot": ["test_chatbot.py"],
    "gallery": ["test_gallery.py"],
}

def run_tests(category=None, test_file=None, verbose=False, html_report=True):
    """
    Run the integration tests.
    
    Args:
        category: Test category to run (optional)
        test_file: Specific test file to run (optional)
        verbose: Whether to run tests in verbose mode
        html_report: Whether to generate an HTML report
    """
    # Build the command
    cmd = ["python", "-m", "pytest"]
    
    # Add verbose flag if requested
    if verbose:
        cmd.append("-v")
    
    # Add HTML report generation if requested
    if html_report:
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = f"report_{timestamp}.html"
        cmd.extend(["--html=" + report_file, "--self-contained-html"])
    
    # Add specific test file if provided
    if test_file:
        cmd.append(test_file)
    # Add test category if provided
    elif category and category in TEST_CATEGORIES:
        cmd.extend(TEST_CATEGORIES[category])
    
    # Print the command being run
    print(f"Running command: {' '.join(cmd)}")
    
    # Run the tests
    start_time = time.time()
    result = subprocess.run(cmd)
    end_time = time.time()
    
    # Print the result
    print(f"Tests completed in {end_time - start_time:.2f} seconds with exit code {result.returncode}")
    
    if html_report:
        print(f"HTML report generated: {report_file}")
    
    return result.returncode

def list_categories():
    """List all available test categories."""
    print("Available test categories:")
    for category, files in TEST_CATEGORIES.items():
        print(f"  {category}: {', '.join(files)}")

def list_test_files():
    """List all available test files."""
    print("Available test files:")
    for file in sorted([f for f in os.listdir(".") if f.startswith("test_") and f.endswith(".py")]):
        print(f"  {file}")

def main():
    """Main function to parse arguments and run tests."""
    parser = argparse.ArgumentParser(description="Run integration tests for GetTwisted Hair Studios website.")
    parser.add_argument("--category", choices=TEST_CATEGORIES.keys(), help="Test category to run")
    parser.add_argument("--file", help="Specific test file to run (e.g., test_gallery.py)")
    parser.add_argument("--verbose", "-v", action="store_true", help="Run tests in verbose mode")
    parser.add_argument("--no-html", action="store_true", help="Disable HTML report generation")
    parser.add_argument("--list-categories", action="store_true", help="List all available test categories")
    parser.add_argument("--list-files", action="store_true", help="List all available test files")
    
    args = parser.parse_args()
    
    # Check if we should list categories or files
    if args.list_categories:
        list_categories()
        return 0
    
    if args.list_files:
        list_test_files()
        return 0
    
    # Run the tests
    return run_tests(
        category=args.category,
        test_file=args.file,
        verbose=args.verbose,
        html_report=not args.no_html
    )

if __name__ == "__main__":
    sys.exit(main())
