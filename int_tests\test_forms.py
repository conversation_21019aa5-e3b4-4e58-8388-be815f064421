"""
Integration tests for form functionality.
"""
import pytest
import time
from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.ui import Select
from conftest import navigate_to_page, wait_for_element, wait_for_element_clickable, check_element_exists

def test_contact_form_exists(driver, wait):
    """Test that the contact form exists on the help page."""
    navigate_to_page(driver, "help.html")
    
    # Look for a contact form
    contact_forms = driver.find_elements(By.CSS_SELECTOR, "form, #contactForm")
    
    # If no contact form is found, skip the test
    if len(contact_forms) == 0:
        pytest.skip("No contact form found on the help page")
    
    # Check the contact form
    contact_form = contact_forms[0]
    assert contact_form.is_displayed(), "Contact form not displayed"
    
    # Check for common form fields
    name_field = contact_form.find_elements(By.XPATH, ".//input[@name='name' or @id='name' or @placeholder='Name']")
    email_field = contact_form.find_elements(By.XPATH, ".//input[@name='email' or @id='email' or @placeholder='Email']")
    message_field = contact_form.find_elements(By.XPATH, ".//textarea[@name='message' or @id='message' or @placeholder='Message']")
    
    # Check that at least some of the expected fields are present
    assert len(name_field) > 0 or len(email_field) > 0 or len(message_field) > 0, "Contact form does not contain expected fields"
    
    # Check for a submit button
    submit_buttons = contact_form.find_elements(By.XPATH, ".//button[@type='submit'] | .//input[@type='submit'] | .//button[contains(text(), 'Submit')] | .//button[contains(text(), 'Send')]")
    assert len(submit_buttons) > 0, "Contact form does not contain a submit button"

def test_contact_form_validation(driver, wait):
    """Test that the contact form validates input correctly."""
    navigate_to_page(driver, "help.html")
    
    # Look for a contact form
    contact_forms = driver.find_elements(By.CSS_SELECTOR, "form, #contactForm")
    
    # If no contact form is found, skip the test
    if len(contact_forms) == 0:
        pytest.skip("No contact form found on the help page")
    
    # Get the contact form
    contact_form = contact_forms[0]
    
    # Find the submit button
    submit_buttons = contact_form.find_elements(By.XPATH, ".//button[@type='submit'] | .//input[@type='submit'] | .//button[contains(text(), 'Submit')] | .//button[contains(text(), 'Send')]")
    
    if len(submit_buttons) == 0:
        pytest.skip("No submit button found in the contact form")
    
    submit_button = submit_buttons[0]
    
    # Try to submit the form without filling in required fields
    submit_button.click()
    time.sleep(1)  # Allow validation to trigger
    
    # Check if validation errors appear
    # This could be browser's built-in validation or custom validation
    invalid_fields = driver.find_elements(By.CSS_SELECTOR, ":invalid")
    error_messages = driver.find_elements(By.CSS_SELECTOR, ".error, .text-red-500, .text-red-600, .invalid-feedback")
    
    # Check that either invalid fields or error messages are present
    assert len(invalid_fields) > 0 or len(error_messages) > 0, "No validation errors found after submitting empty form"

def test_careers_form_exists(driver, wait):
    """Test that the careers application form exists."""
    navigate_to_page(driver, "careers.html")
    
    # Find all apply buttons
    apply_buttons = driver.find_elements(By.XPATH, "//button[contains(text(), 'Apply')] | //a[contains(text(), 'Apply')]")
    
    # Check that at least one apply button exists
    if len(apply_buttons) == 0:
        pytest.skip("No apply buttons found on the careers page")
    
    # Click the first apply button
    apply_button = apply_buttons[0]
    driver.execute_script("arguments[0].scrollIntoView(true);", apply_button)
    time.sleep(1)  # Allow scroll to complete
    apply_button.click()
    time.sleep(1)  # Allow modal to open
    
    # Check if a modal or form appears
    form_elements = driver.find_elements(By.CSS_SELECTOR, "form, #applicationForm, .modal form")
    
    # If no form is found, the apply button might be a link to an external site
    if len(form_elements) == 0:
        # Check if the apply button is a link
        if apply_button.tag_name.lower() == "a":
            # Check that the link has a valid href
            href = apply_button.get_attribute("href")
            assert href and href != "#", "Apply button link does not have a valid href"
        else:
            pytest.fail("No application form found after clicking apply button")
    else:
        # Get the form
        form = form_elements[0]
        
        # Check for common form fields
        name_field = form.find_elements(By.XPATH, ".//input[@name='name' or @id='name' or @placeholder='Name']")
        email_field = form.find_elements(By.XPATH, ".//input[@name='email' or @id='email' or @placeholder='Email']")
        phone_field = form.find_elements(By.XPATH, ".//input[@name='phone' or @id='phone' or @placeholder='Phone']")
        
        # Check that at least some of the expected fields are present
        assert len(name_field) > 0 or len(email_field) > 0 or len(phone_field) > 0, "Application form does not contain expected fields"
        
        # Check for a submit button
        submit_buttons = form.find_elements(By.XPATH, ".//button[@type='submit'] | .//input[@type='submit'] | .//button[contains(text(), 'Submit')] | .//button[contains(text(), 'Apply')]")
        assert len(submit_buttons) > 0, "Application form does not contain a submit button"

def test_form_action_urls(driver, wait):
    """Test that forms have valid action URLs."""
    pages = ["help.html", "careers.html"]
    
    for page in pages:
        navigate_to_page(driver, page)
        
        # Find all forms
        forms = driver.find_elements(By.TAG_NAME, "form")
        
        for form in forms:
            # Check if the form has an action attribute
            action = form.get_attribute("action")
            
            # If the form has an action, check that it's valid
            if action:
                assert action.startswith("http") or action.startswith("/") or action == "#", f"Form on {page} has invalid action URL: {action}"
                
                # Check if the action is a FormSpree URL
                if "formspree.io" in action:
                    # Check that the FormSpree URL is valid
                    assert "f/" in action, f"FormSpree URL on {page} is invalid: {action}"

def test_form_method(driver, wait):
    """Test that forms have valid method attributes."""
    pages = ["help.html", "careers.html"]
    
    for page in pages:
        navigate_to_page(driver, page)
        
        # Find all forms
        forms = driver.find_elements(By.TAG_NAME, "form")
        
        for form in forms:
            # Check if the form has a method attribute
            method = form.get_attribute("method")
            
            # If the form has a method, check that it's valid
            if method:
                assert method.lower() in ["get", "post"], f"Form on {page} has invalid method: {method}"
                
                # If the form action is a FormSpree URL, check that the method is POST
                action = form.get_attribute("action")
                if action and "formspree.io" in action:
                    assert method.lower() == "post", f"FormSpree form on {page} should use POST method, not {method}"

def test_form_enctype(driver, wait):
    """Test that forms have valid enctype attributes if they include file uploads."""
    pages = ["help.html", "careers.html"]
    
    for page in pages:
        navigate_to_page(driver, page)
        
        # Find all forms
        forms = driver.find_elements(By.TAG_NAME, "form")
        
        for form in forms:
            # Check if the form includes a file upload field
            file_fields = form.find_elements(By.CSS_SELECTOR, "input[type='file']")
            
            if len(file_fields) > 0:
                # If the form includes a file upload, check that it has the correct enctype
                enctype = form.get_attribute("enctype")
                assert enctype == "multipart/form-data", f"Form on {page} with file upload should have enctype='multipart/form-data', not {enctype}"
