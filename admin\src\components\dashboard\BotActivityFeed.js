import React from 'react';

const BotActivityFeed = ({ activities }) => {
  // Function to format the timestamp
  const formatTime = (timestamp) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };
  
  // Function to get the appropriate class for the activity type
  const getActivityClass = (activity) => {
    if (activity.type === 'TRADE') {
      return activity.action === 'OPEN' ? 'trade-open' : 'trade-close';
    }
    if (activity.type === 'SYSTEM') {
      return 'system';
    }
    if (activity.type === 'ERROR') {
      return 'error';
    }
    return '';
  };
  
  return (
    <div className="bot-activity-feed">
      <h3 className="feed-title">Real-Time Bot Activity</h3>
      
      <div className="activity-list">
        {activities.map((activity, index) => (
          <div key={index} className={`activity-item ${getActivityClass(activity)}`}>
            <div className="activity-time">[{formatTime(activity.timestamp)}]</div>
            <div className="activity-content">
              {activity.botName && (
                <span className="activity-bot">
                  {activity.botName} 
                  <span className={`bot-type-badge ${activity.botType.toLowerCase()}`}>
                    {activity.botType}
                  </span>
                </span>
              )}
              <span className="activity-message">{activity.message}</span>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default BotActivityFeed;
