// API client for GetTwisted Hair Studios
const API_URL = 'http://localhost:5000/api';

// Fetch stylists from the API
async function fetchStylists() {
  try {
    const response = await fetch(`${API_URL}/stylists`);
    if (!response.ok) {
      throw new Error('Failed to fetch stylists');
    }
    const data = await response.json();
    return data.data;
  } catch (error) {
    console.error('Error fetching stylists:', error);
    return [];
  }
}

// Fetch services from the API
async function fetchServices() {
  try {
    const response = await fetch(`${API_URL}/services`);
    if (!response.ok) {
      throw new Error('Failed to fetch services');
    }
    const data = await response.json();
    return data.data;
  } catch (error) {
    console.error('Error fetching services:', error);
    return [];
  }
}

// Fetch services by category
async function fetchServicesByCategory(category) {
  try {
    const response = await fetch(`${API_URL}/services/category/${category}`);
    if (!response.ok) {
      throw new Error(`Failed to fetch services for category: ${category}`);
    }
    const data = await response.json();
    return data.data;
  } catch (error) {
    console.error(`Error fetching services for category ${category}:`, error);
    return [];
  }
}

// Example of how to use these functions:
// document.addEventListener('DOMContentLoaded', async () => {
//   const stylists = await fetchStylists();
//   console.log('Stylists:', stylists);
//
//   const services = await fetchServices();
//   console.log('Services:', services);
//
//   const locServices = await fetchServicesByCategory('Locs');
//   console.log('Loc Services:', locServices);
// });
