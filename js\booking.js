/**
 * Booking Form Functionality
 * This script handles the multi-step booking form
 */

// Booking form data
let bookingData = {
  fullName: '',
  email: '',
  phone: '',
  location: '',
  serviceCategory: '',
  service: '',
  date: '',
  time: '',
  notes: ''
};

// Service options by category
const serviceOptions = {
  locs: [
    { id: 'starter-locs', name: 'Starter Locs', price: '$160' },
    { id: 'retwist', name: '<PERSON><PERSON>wi<PERSON>', price: '$130' },
    { id: 'loc-repair', name: 'Loc Repair', price: '$50+' },
    { id: 'loc-styling', name: '<PERSON><PERSON> <PERSON>yl<PERSON>', price: '$25+' },
    { id: 'loc-detox', name: 'Loc <PERSON>ox', price: '$50' }
  ],
  braids: [
    { id: 'box-braids', name: '<PERSON> Braids', price: 'Varies' },
    { id: 'knotless-braids', name: 'Knot<PERSON> Braids', price: 'Varies' },
    { id: 'cornrows', name: '<PERSON><PERSON><PERSON>', price: 'Varies' },
    { id: 'twists', name: 'Twists', price: 'Varies' }
  ],
  cuts: [
    { id: 'haircut', name: 'Haircut', price: '$45' },
    { id: 'beard-trim', name: 'Beard Trim', price: '$20' },
    { id: 'shape-up', name: 'Shape Up', price: '$25' },
    { id: 'kids-cut', name: 'Kids Haircut', price: '$25' },
    { id: 'baldie', name: 'Baldie', price: '$50' }
  ],
  color: [
    { id: 'full-color', name: 'Full Color', price: '$50+' },
    { id: 'highlights', name: 'Highlights', price: 'Varies' },
    { id: 'color-touch-up', name: 'Color Touch-Up', price: 'Varies' }
  ]
};

// Initialize booking form
document.addEventListener('DOMContentLoaded', function() {
  // Make functions globally available
  window.goToStep = goToStep;
  window.resetBookingData = resetBookingData;
  window.selectServiceCategory = selectServiceCategory;
  window.submitBooking = submitBooking;
  
  // Set up event listeners for service categories
  setupServiceCategories();
});

// Toggle booking modal
function toggleBookingModal() {
  const modal = document.getElementById('bookingModal');
  
  if (modal.classList.contains('hidden')) {
    // Opening the modal
    modal.classList.remove('hidden');
    document.body.style.overflow = 'hidden'; // Prevent scrolling behind modal
    
    // Reset to step 1
    goToStep(1);
  } else {
    // Closing the modal
    modal.classList.add('hidden');
    document.body.style.overflow = ''; // Restore scrolling
    
    // Reset booking data
    resetBookingData();
  }
}

// Set up service categories
function setupServiceCategories() {
  const categories = document.querySelectorAll('.service-category');
  if (categories.length === 0) return; // Exit if elements don't exist yet
  
  categories.forEach(category => {
    const id = category.id.replace('Category', '');
    category.addEventListener('click', () => selectServiceCategory(id));
  });
}

// Reset booking data
function resetBookingData() {
  bookingData = {
    fullName: '',
    email: '',
    phone: '',
    location: '',
    serviceCategory: '',
    service: '',
    date: '',
    time: '',
    notes: ''
  };
  
  // Reset forms
  const fullNameInput = document.getElementById('fullName');
  const emailInput = document.getElementById('email');
  const phoneInput = document.getElementById('phone');
  const locationInput = document.getElementById('location');
  
  if (fullNameInput) fullNameInput.value = '';
  if (emailInput) emailInput.value = '';
  if (phoneInput) phoneInput.value = '';
  if (locationInput) locationInput.value = '';
  
  // Reset service selection
  const categories = document.querySelectorAll('.service-category');
  categories.forEach(category => {
    category.classList.remove('bg-pink-100', 'border-pink-500', 'border-2');
  });
  
  const servicesDiv = document.getElementById('specificServices');
  if (servicesDiv) servicesDiv.classList.add('hidden');
  
  // Reset date/time
  const dateInput = document.getElementById('appointmentDate');
  const timeInput = document.getElementById('appointmentTime');
  const notesInput = document.getElementById('notes');
  
  if (dateInput) dateInput.value = '';
  if (timeInput) timeInput.value = '';
  if (notesInput) notesInput.value = '';
}

// Navigate between steps
function goToStep(step) {
  // Validate current step before proceeding
  if (step > 1 && !validateStep(step - 1)) {
    return;
  }
  
  // Hide all steps
  const steps = document.querySelectorAll('.booking-step');
  steps.forEach(s => s.classList.add('hidden'));
  
  // Show the requested step
  const stepElement = document.getElementById(`bookingStep${step}`);
  if (stepElement) {
    stepElement.classList.remove('hidden');
  } else {
    console.error(`Step element bookingStep${step} not found`);
    return;
  }
  
  // Update step indicators
  updateStepIndicators(step);
  
  // If going to step 3, update the date input to start from today
  if (step === 3) {
    const dateInput = document.getElementById('appointmentDate');
    if (dateInput) {
      const today = new Date().toISOString().split('T')[0];
      dateInput.min = today;
    }
  }
}

// Update step indicators
function updateStepIndicators(currentStep) {
  // Reset all indicators
  for (let i = 1; i <= 3; i++) {
    const indicator = document.getElementById(`step${i}Indicator`);
    if (!indicator) continue;
    
    if (i <= currentStep) {
      indicator.classList.remove('bg-gray-300', 'text-gray-600');
      indicator.classList.add('bg-pink-600', 'text-white');
    } else {
      indicator.classList.remove('bg-pink-600', 'text-white');
      indicator.classList.add('bg-gray-300', 'text-gray-600');
    }
  }
  
  // Update lines between indicators
  const line1to2 = document.getElementById('line1to2');
  const line2to3 = document.getElementById('line2to3');
  
  if (line1to2) {
    if (currentStep >= 2) {
      line1to2.classList.remove('bg-gray-300');
      line1to2.classList.add('bg-pink-600');
    } else {
      line1to2.classList.remove('bg-pink-600');
      line1to2.classList.add('bg-gray-300');
    }
  }
  
  if (line2to3) {
    if (currentStep >= 3) {
      line2to3.classList.remove('bg-gray-300');
      line2to3.classList.add('bg-pink-600');
    } else {
      line2to3.classList.remove('bg-pink-600');
      line2to3.classList.add('bg-gray-300');
    }
  }
}

// Validate step data
function validateStep(step) {
  if (step === 1) {
    // Validate personal information
    const fullNameInput = document.getElementById('fullName');
    const emailInput = document.getElementById('email');
    const phoneInput = document.getElementById('phone');
    const locationInput = document.getElementById('location');
    
    if (!fullNameInput || !emailInput || !phoneInput || !locationInput) {
      console.error('Form elements not found');
      return false;
    }
    
    const fullName = fullNameInput.value.trim();
    const email = emailInput.value.trim();
    const phone = phoneInput.value.trim();
    const location = locationInput.value;
    
    if (!fullName || !email || !phone || !location) {
      alert('Please fill in all required fields.');
      return false;
    }
    
    // Store the data
    bookingData.fullName = fullName;
    bookingData.email = email;
    bookingData.phone = phone;
    bookingData.location = location;
    
    return true;
  } else if (step === 2) {
    // Validate service selection
    if (!bookingData.serviceCategory || !bookingData.service) {
      alert('Please select a service.');
      return false;
    }
    return true;
  }
  
  return true;
}

// Select service category
function selectServiceCategory(category) {
  // Update UI to show selected category
  const categories = document.querySelectorAll('.service-category');
  categories.forEach(cat => {
    cat.classList.remove('bg-pink-100', 'border-pink-500', 'border-2');
  });
  
  const selectedCategory = document.getElementById(`${category}Category`);
  if (selectedCategory) {
    selectedCategory.classList.add('bg-pink-100', 'border-pink-500', 'border-2');
  }
  
  // Store the selected category
  bookingData.serviceCategory = category;
  
  // Show specific services for this category
  const servicesDiv = document.getElementById('specificServices');
  if (servicesDiv) {
    servicesDiv.classList.remove('hidden');
  }
  
  // Populate the services dropdown
  const serviceSelect = document.getElementById('serviceSelect');
  if (serviceSelect) {
    serviceSelect.innerHTML = '<option value="">Select a service</option>';
    
    serviceOptions[category].forEach(service => {
      const option = document.createElement('option');
      option.value = service.id;
      option.textContent = `${service.name} (${service.price})`;
      serviceSelect.appendChild(option);
    });
    
    // Add change event listener to the service select
    serviceSelect.onchange = function() {
      bookingData.service = this.value;
      const selectedService = serviceOptions[category].find(s => s.id === this.value);
      if (selectedService) {
        bookingData.serviceName = selectedService.name;
        bookingData.servicePrice = selectedService.price;
      }
    };
  }
}

// Submit booking
function submitBooking() {
  // Validate date and time
  const dateInput = document.getElementById('appointmentDate');
  const timeInput = document.getElementById('appointmentTime');
  const notesInput = document.getElementById('notes');
  
  if (!dateInput || !timeInput) {
    console.error('Date or time inputs not found');
    return;
  }
  
  const date = dateInput.value;
  const time = timeInput.value;
  const notes = notesInput ? notesInput.value : '';
  
  if (!date || !time) {
    alert('Please select a date and time for your appointment.');
    return;
  }
  
  // Store the data
  bookingData.date = date;
  bookingData.time = time;
  bookingData.notes = notes;
  
  // Format date for display
  const formattedDate = new Date(date).toLocaleDateString('en-US', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
  
  // Format time for display
  const timeValue = parseInt(time.split(':')[0]);
  const formattedTime = timeValue > 12 ? 
    `${timeValue - 12}:00 PM` : 
    timeValue === 12 ? '12:00 PM' : `${timeValue}:00 AM`;
  
  // Store booking data in localStorage
  localStorage.setItem('lastBooking', JSON.stringify(bookingData));
  
  // In a real implementation, you would send this data to your server or API
  // For now, we'll just show the confirmation
  
  // Update confirmation screen with booking details
  const confirmName = document.getElementById('confirmName');
  const confirmService = document.getElementById('confirmService');
  const confirmLocation = document.getElementById('confirmLocation');
  const confirmDateTime = document.getElementById('confirmDateTime');
  
  if (confirmName) confirmName.textContent = bookingData.fullName;
  if (confirmService) confirmService.textContent = `${bookingData.serviceName} (${bookingData.servicePrice})`;
  if (confirmLocation) confirmLocation.textContent = bookingData.location === 'teaneck' ? 'Teaneck, NJ' : 'Pottstown, PA';
  if (confirmDateTime) confirmDateTime.textContent = `${formattedDate} at ${formattedTime}`;
  
  // Show confirmation screen
  const steps = document.querySelectorAll('.booking-step');
  steps.forEach(s => s.classList.add('hidden'));
  
  const confirmationScreen = document.getElementById('bookingConfirmation');
  if (confirmationScreen) {
    confirmationScreen.classList.remove('hidden');
  }
  
  // In a real implementation, you would also send an email confirmation
  console.log('Booking submitted:', bookingData);
}

// Make toggleBookingModal globally available
window.toggleBookingModal = toggleBookingModal;
