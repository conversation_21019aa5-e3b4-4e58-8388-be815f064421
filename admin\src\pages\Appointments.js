import React from 'react';

const Appointments = () => {
  // In a real app, this would fetch data from the API
  const appointments = [
    {
      id: 1,
      client: '<PERSON>',
      service: '<PERSON><PERSON> Retwist',
      stylist: '<PERSON><PERSON>',
      date: 'Apr 12, 2025',
      time: '10:00 AM',
      status: 'Completed'
    },
    {
      id: 2,
      client: '<PERSON>',
      service: 'Starter Locs',
      stylist: '<PERSON>',
      date: 'Apr 11, 2025',
      time: '2:30 PM',
      status: 'Completed'
    },
    {
      id: 3,
      client: '<PERSON><PERSON> Brown',
      service: 'Box Braids',
      stylist: '<PERSON>',
      date: 'Apr 13, 2025',
      time: '11:00 AM',
      status: 'Scheduled'
    },
    {
      id: 4,
      client: '<PERSON><PERSON>',
      service: 'Fade & Shape Up',
      stylist: '<PERSON>',
      date: 'Apr 13, 2025',
      time: '3:00 PM',
      status: 'Scheduled'
    },
    {
      id: 5,
      client: '<PERSON><PERSON><PERSON>',
      service: 'Goddess Locs',
      stylist: '<PERSON><PERSON>',
      date: 'Apr 14, 2025',
      time: '1:00 PM',
      status: 'Scheduled'
    }
  ];

  return (
    <div>
      <div className="flex justify-between items-center mb-4">
        <h1>Appointments</h1>
        <button className="btn btn-primary">Add New Appointment</button>
      </div>
      
      <div className="card">
        <div className="card-header">
          <div className="flex gap-4">
            <button className="btn btn-secondary">All</button>
            <button className="btn">Scheduled</button>
            <button className="btn">Completed</button>
            <button className="btn">Cancelled</button>
          </div>
        </div>
        
        <table className="table">
          <thead>
            <tr>
              <th>Client</th>
              <th>Service</th>
              <th>Stylist</th>
              <th>Date</th>
              <th>Time</th>
              <th>Status</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {appointments.map(appointment => (
              <tr key={appointment.id}>
                <td>{appointment.client}</td>
                <td>{appointment.service}</td>
                <td>{appointment.stylist}</td>
                <td>{appointment.date}</td>
                <td>{appointment.time}</td>
                <td>
                  <span className={`p-4 rounded ${
                    appointment.status === 'Completed' ? 'bg-success' : 
                    appointment.status === 'Scheduled' ? 'bg-info' : 'bg-danger'
                  }`}>
                    {appointment.status}
                  </span>
                </td>
                <td>
                  <div className="flex gap-4">
                    <button className="btn btn-secondary">Edit</button>
                    <button className="btn btn-danger">Cancel</button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default Appointments;
