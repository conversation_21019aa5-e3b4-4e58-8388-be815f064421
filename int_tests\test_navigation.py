"""
Integration tests for navigation between pages.
"""
import pytest
import time
from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from conftest import navigate_to_page, wait_for_element, wait_for_element_clickable, check_element_exists

def test_navigation_from_home(driver, wait):
    """Test navigation from the home page to other pages."""
    navigate_to_page(driver, "index.html")

    # Navigate to the Stylists page
    stylists_link = wait_for_element_clickable(driver, By.XPATH, "//a[text()='Stylists']")
    stylists_link.click()
    time.sleep(1)  # Allow page to load

    # Check that we're on the Stylists page
    assert "Stylists" in driver.title and "GetTwisted Hair Studios" in driver.title
    print(f"Stylists page title: {driver.title}")

    # Navigate to the Services page
    services_link = wait_for_element_clickable(driver, By.XPATH, "//a[text()='Services']")
    services_link.click()
    time.sleep(1)  # Allow page to load

    # Check that we're on the Services page
    assert "Services" in driver.title and "GetTwisted Hair Studios" in driver.title
    print(f"Services page title: {driver.title}")

    # Navigate to the Gallery page
    gallery_link = wait_for_element_clickable(driver, By.XPATH, "//a[text()='Gallery']")
    gallery_link.click()
    time.sleep(1)  # Allow page to load

    # Check that we're on the Gallery page
    assert "Gallery" in driver.title and "GetTwisted Hair Studios" in driver.title
    print(f"Gallery page title: {driver.title}")

    # Navigate to the Careers page
    careers_link = wait_for_element_clickable(driver, By.XPATH, "//a[text()='Careers']")
    careers_link.click()
    time.sleep(1)  # Allow page to load

    # Check that we're on the Careers page
    assert "Careers" in driver.title and "GetTwisted Hair Studios" in driver.title
    print(f"Careers page title: {driver.title}")

    # Navigate to the Help page
    help_link = wait_for_element_clickable(driver, By.XPATH, "//a[text()='Help']")
    help_link.click()
    time.sleep(1)  # Allow page to load

    # Check that we're on the Help page
    # The Help page might not have "Help" in its title, so we'll just check for GetTwisted
    assert "GetTwisted Hair Studios" in driver.title
    print(f"Help page title: {driver.title}")

    # Also check that we're on the help.html page
    assert "help.html" in driver.current_url

    # Navigate back to the Home page using the logo
    logo_link = wait_for_element_clickable(driver, By.CSS_SELECTOR, "header a.text-3xl")
    logo_link.click()
    time.sleep(1)  # Allow page to load

    # Check that we're back on the Home page
    assert "GetTwisted Hair Studios" in driver.title
    assert "index.html" in driver.current_url
    print(f"Home page title: {driver.title}")

def test_navigation_from_mobile_menu(driver, wait):
    """Test navigation using the mobile menu."""
    # Set window size to mobile dimensions
    driver.set_window_size(375, 812)  # iPhone X dimensions

    navigate_to_page(driver, "index.html")

    # Open the mobile menu
    toggle = wait_for_element_clickable(driver, By.ID, "mobileMenuToggle")
    toggle.click()
    time.sleep(1)  # Allow menu to open

    # Navigate to the Gallery page
    gallery_link = wait_for_element_clickable(driver, By.XPATH, "//div[@id='mobileMenu']//a[text()='Gallery']")
    gallery_link.click()
    time.sleep(1)  # Allow page to load

    # Check that we're on the Gallery page
    assert "Gallery - GetTwisted Hair Studios" in driver.title

    # Open the mobile menu again
    toggle = wait_for_element_clickable(driver, By.ID, "mobileMenuToggle")
    toggle.click()
    time.sleep(1)  # Allow menu to open

    # Navigate to the Services page
    services_link = wait_for_element_clickable(driver, By.XPATH, "//div[@id='mobileMenu']//a[text()='Services']")
    services_link.click()
    time.sleep(1)  # Allow page to load

    # Check that we're on the Services page
    assert "Services - GetTwisted Hair Studios" in driver.title

    # Reset window size
    driver.maximize_window()

def test_footer_navigation(driver, wait):
    """Test navigation using the footer links."""
    navigate_to_page(driver, "index.html")

    # Scroll to the footer
    footer = wait_for_element(driver, By.TAG_NAME, "footer")
    driver.execute_script("arguments[0].scrollIntoView(true);", footer)
    time.sleep(1)  # Allow scroll to complete

    # Navigate to the Stylists page
    stylists_link = wait_for_element_clickable(driver, By.XPATH, "//footer//a[contains(text(), 'Meet Our Stylists')]")
    stylists_link.click()
    time.sleep(1)  # Allow page to load

    # Check that we're on the Stylists page
    assert "Stylists - GetTwisted Hair Studios" in driver.title

    # Scroll to the footer again
    footer = wait_for_element(driver, By.TAG_NAME, "footer")
    driver.execute_script("arguments[0].scrollIntoView(true);", footer)
    time.sleep(1)  # Allow scroll to complete

    # Navigate to the Services page
    services_link = wait_for_element_clickable(driver, By.XPATH, "//footer//a[contains(text(), 'Our Services')]")
    services_link.click()
    time.sleep(1)  # Allow page to load

    # Check that we're on the Services page
    assert "Services - GetTwisted Hair Studios" in driver.title

    # Scroll to the footer again
    footer = wait_for_element(driver, By.TAG_NAME, "footer")
    driver.execute_script("arguments[0].scrollIntoView(true);", footer)
    time.sleep(1)  # Allow scroll to complete

    # Navigate to the Gallery page
    gallery_link = wait_for_element_clickable(driver, By.XPATH, "//footer//a[contains(text(), 'Our Gallery')]")
    gallery_link.click()
    time.sleep(1)  # Allow page to load

    # Check that we're on the Gallery page
    assert "Gallery - GetTwisted Hair Studios" in driver.title

def test_theme_persistence(driver, wait):
    """Test that the theme persists when navigating between pages."""
    navigate_to_page(driver, "index.html")

    # Get the initial theme
    body = driver.find_element(By.TAG_NAME, "body")
    initial_theme = body.get_attribute("class")

    # Click the theme toggle button to change the theme
    theme_button = wait_for_element_clickable(driver, By.ID, "themeToggleBtn")
    theme_button.click()
    time.sleep(1)  # Allow theme to change

    # Get the new theme
    new_theme = body.get_attribute("class")
    assert initial_theme != new_theme

    # Navigate to the Gallery page
    gallery_link = wait_for_element_clickable(driver, By.XPATH, "//a[text()='Gallery']")
    gallery_link.click()
    time.sleep(1)  # Allow page to load

    # Check that the theme persists on the Gallery page
    body = driver.find_element(By.TAG_NAME, "body")
    gallery_theme = body.get_attribute("class")
    assert gallery_theme == new_theme

    # Navigate to the Services page
    services_link = wait_for_element_clickable(driver, By.XPATH, "//a[text()='Services']")
    services_link.click()
    time.sleep(1)  # Allow page to load

    # Check that the theme persists on the Services page
    body = driver.find_element(By.TAG_NAME, "body")
    services_theme = body.get_attribute("class")
    assert services_theme == new_theme
