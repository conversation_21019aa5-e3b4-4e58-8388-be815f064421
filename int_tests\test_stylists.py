"""
Integration tests for the stylists page.
"""
import pytest
import time
from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from conftest import navigate_to_page, wait_for_element, wait_for_element_clickable, check_element_exists

def test_stylists_page_loads(driver, wait):
    """Test that the stylists page loads successfully."""
    navigate_to_page(driver, "stylists.html")
    
    # Check that the page title is correct
    assert "Stylists - GetTwisted Hair Studios" in driver.title
    
    # Check that the stylists banner is displayed
    banner = wait_for_element(driver, By.CSS_SELECTOR, "section h2")
    assert "Our Stylists" in banner.text
    
    # Check that the stylists section is present
    stylists_section = wait_for_element(driver, By.CSS_SELECTOR, "section.py-16")
    assert stylists_section.is_displayed()

def test_stylist_cards(driver, wait):
    """Test that the stylist cards are displayed correctly."""
    navigate_to_page(driver, "stylists.html")
    
    # Check that stylist cards are present
    stylist_cards = driver.find_elements(By.CSS_SELECTOR, ".stylist-card")
    assert len(stylist_cards) > 0
    
    # Check the first stylist card
    first_card = stylist_cards[0]
    assert first_card.is_displayed()
    
    # Check that the card contains a stylist name
    stylist_name = first_card.find_element(By.CSS_SELECTOR, "h3")
    assert stylist_name.text.strip() != ""
    
    # Check that the card contains a stylist role
    stylist_role = first_card.find_element(By.CSS_SELECTOR, "p.text-lg")
    assert stylist_role.text.strip() != ""
    
    # Check that the card contains a stylist image
    stylist_image = first_card.find_element(By.TAG_NAME, "img")
    assert stylist_image.get_attribute("src") != ""
    
    # Check that the card contains a book button
    book_button = first_card.find_element(By.CSS_SELECTOR, "a.book-btn")
    assert book_button.is_displayed()
    assert "Book" in book_button.text

def test_book_appointment_links(driver, wait):
    """Test that the book appointment links work correctly."""
    navigate_to_page(driver, "stylists.html")
    
    # Find all book appointment links
    book_links = driver.find_elements(By.CSS_SELECTOR, "a.book-btn")
    
    # Check that at least one book appointment link exists
    assert len(book_links) > 0
    
    # Check that the first link is displayed and has the correct href
    book_link = book_links[0]
    assert book_link.is_displayed()
    assert "square.site" in book_link.get_attribute("href") or "squareup.com" in book_link.get_attribute("href")

def test_stylist_specialties(driver, wait):
    """Test that stylist specialties are displayed correctly."""
    navigate_to_page(driver, "stylists.html")
    
    # Find all stylist cards
    stylist_cards = driver.find_elements(By.CSS_SELECTOR, ".stylist-card")
    
    # Check that at least one stylist card exists
    assert len(stylist_cards) > 0
    
    # Check the first stylist card for specialties
    first_card = stylist_cards[0]
    
    # Look for specialties section (might be in different formats)
    specialties = first_card.find_elements(By.CSS_SELECTOR, "ul li, p.text-sm")
    
    # Check that at least one specialty is listed
    assert len(specialties) > 0
    
    # Check that the specialties have text
    for specialty in specialties:
        if specialty.is_displayed():
            assert specialty.text.strip() != ""

def test_stylist_locations(driver, wait):
    """Test that stylist locations are displayed correctly."""
    navigate_to_page(driver, "stylists.html")
    
    # Find all stylist cards
    stylist_cards = driver.find_elements(By.CSS_SELECTOR, ".stylist-card")
    
    # Check that at least one stylist card exists
    assert len(stylist_cards) > 0
    
    # Check for location information in the cards
    location_elements = driver.find_elements(By.XPATH, "//*[contains(text(), 'Teaneck') or contains(text(), 'Pottstown')]")
    
    # Check that at least one location is mentioned
    assert len(location_elements) > 0

def test_theme_compatibility(driver, wait):
    """Test that the stylists page works correctly with different themes."""
    navigate_to_page(driver, "stylists.html")
    
    # Get the initial theme
    body = driver.find_element(By.TAG_NAME, "body")
    initial_theme = body.get_attribute("class")
    
    # Click the theme toggle button
    theme_button = wait_for_element_clickable(driver, By.ID, "themeToggleBtn")
    theme_button.click()
    time.sleep(1)  # Allow theme to change
    
    # Check that the theme has changed
    new_theme = body.get_attribute("class")
    assert initial_theme != new_theme
    
    # Check that the stylist cards are still displayed
    stylist_cards = driver.find_elements(By.CSS_SELECTOR, ".stylist-card")
    assert len(stylist_cards) > 0
    assert stylist_cards[0].is_displayed()
    
    # Reset the theme
    theme_button.click()
    time.sleep(1)  # Allow theme to change
