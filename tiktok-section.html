<!-- TikTok Videos Section -->
<section class="bg-pink-100 py-16 px-6 text-center" data-aos="fade-up">
  <div class="max-w-6xl mx-auto">
    <h2 class="text-3xl md:text-4xl font-bold uppercase mb-8 theme-text">Watch the Styles in Action</h2>
    
    <div class="relative">
      <!-- Slider Container -->
      <div class="overflow-hidden">
        <div id="tiktok-slider-track" class="flex transition-transform duration-500 ease-in-out">
          <!-- TikTok video items will be added by JavaScript -->
        </div>
      </div>
      
      <!-- Slider Navigation -->
      <button id="tiktok-slider-prev" class="absolute left-0 top-1/2 transform -translate-y-1/2 bg-pink-600 text-white rounded-full p-2 shadow-lg z-10 hover:bg-pink-700 transition">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
        </svg>
      </button>
      <button id="tiktok-slider-next" class="absolute right-0 top-1/2 transform -translate-y-1/2 bg-pink-600 text-white rounded-full p-2 shadow-lg z-10 hover:bg-pink-700 transition">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
        </svg>
      </button>
    </div>
    
    <!-- Slider Indicators -->
    <div id="tiktok-slider-indicators" class="flex justify-center mt-6 space-x-2">
      <!-- Indicators will be added by JavaScript -->
    </div>
    
    <!-- Follow on TikTok Button -->
    <div class="mt-8">
      <a href="https://www.tiktok.com/@therealsashafearless" target="_blank" class="inline-flex items-center bg-black text-white px-6 py-3 rounded-full hover:bg-gray-800 transition">
        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 448 512" class="mr-2 fill-current">
          <path d="M448,209.91a210.06,210.06,0,0,1-122.77-39.25V349.38A162.55,162.55,0,1,1,185,188.31V278.2a74.62,74.62,0,1,0,52.23,71.18V0l88,0a121.18,121.18,0,0,0,1.86,22.17h0A122.18,122.18,0,0,0,381,102.39a121.43,121.43,0,0,0,67,20.14Z"/>
        </svg>
        Follow Sasha on TikTok
      </a>
    </div>
  </div>
</section>

<!-- TikTok Video Modal -->
<div id="tiktok-video-modal" class="fixed inset-0 z-50 hidden bg-black bg-opacity-75 flex items-center justify-center p-4">
  <div class="bg-white rounded-xl max-w-2xl w-full mx-auto shadow-2xl">
    <div class="flex justify-between items-center px-6 py-4 bg-pink-600 text-white rounded-t-xl">
      <div>
        <h3 id="tiktok-video-title" class="text-lg font-bold">Video Title</h3>
        <p id="tiktok-video-username" class="text-sm">@username</p>
      </div>
      <button id="close-tiktok-modal" class="text-2xl leading-none hover:text-yellow-300">&times;</button>
    </div>
    <div class="p-6">
      <div id="tiktok-video-container" class="flex justify-center">
        <!-- TikTok embed will be added here by JavaScript -->
      </div>
    </div>
  </div>
</div>
