import React from 'react';
import { NavLink } from 'react-router-dom';

const Sidebar = ({ open }) => {
  return (
    <aside className={`sidebar ${open ? '' : 'closed'}`}>
      <div className="sidebar-menu">
        <NavLink to="/" className={({ isActive }) => 
          `sidebar-item ${isActive ? 'active' : ''}`
        } end>
          <span className="sidebar-item-icon">📊</span>
          Dashboard
        </NavLink>
        <NavLink to="/stylists" className={({ isActive }) => 
          `sidebar-item ${isActive ? 'active' : ''}`
        }>
          <span className="sidebar-item-icon">👩‍💇</span>
          Stylists
        </NavLink>
        <NavLink to="/services" className={({ isActive }) => 
          `sidebar-item ${isActive ? 'active' : ''}`
        }>
          <span className="sidebar-item-icon">✂️</span>
          Services
        </NavLink>
        <NavLink to="/appointments" className={({ isActive }) => 
          `sidebar-item ${isActive ? 'active' : ''}`
        }>
          <span className="sidebar-item-icon">📅</span>
          Appointments
        </NavLink>
        <NavLink to="/settings" className={({ isActive }) => 
          `sidebar-item ${isActive ? 'active' : ''}`
        }>
          <span className="sidebar-item-icon">⚙️</span>
          Settings
        </NavLink>
      </div>
    </aside>
  );
};

export default Sidebar;
