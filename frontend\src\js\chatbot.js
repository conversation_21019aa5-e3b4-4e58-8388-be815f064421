// Chatbot functionality for GetTwisted Hair Studios

let chatbotKnowledge = [];

// Load chatbot Q&A from JSON file
function loadChatbotKnowledge() {
  fetch('/assets/data/chatbot-knowledge.json')
    .then(response => response.json())
    .then(data => {
      chatbotKnowledge = data;
      console.log("Chatbot knowledge loaded:", chatbotKnowledge);
    })
    .catch(err => console.error("Error loading chatbot knowledge:", err));
}

// Clean text for better matching
function sanitize(text) {
  return text.toLowerCase().replace(/[^\w\s]/gi, '').trim();
}

// Handle chatbot input
function handleChatInput(inputText) {
  const cleanInput = sanitize(inputText);

  // Try exact match
  let match = chatbotKnowledge.find(item =>
    sanitize(item.question) === cleanInput
  );

  // Fallback to partial match
  if (!match) {
    match = chatbotKnowledge.find(item =>
      cleanInput.includes(sanitize(item.question))
    );
  }

  return match
    ? match.answer
    : "I'm here to help, but I don't have that answer yet. You can text us directly at (************* or visit our booking page to learn more.";
}

document.addEventListener("DOMContentLoaded", () => {
  loadChatbotKnowledge();
  
  const input = document.getElementById('chatInput');
  const sendBtn = document.getElementById('sendBtn');
  const chatBox = document.querySelector('#chatbot .p-4.h-60');

  if (input && sendBtn && chatBox) {
    function sendMessage() {
      const text = input.value.trim();
      if (!text) return;

      const userBubble = `<div class="bg-yellow-100 text-black p-3 rounded-lg w-fit max-w-[80%] ml-auto">${text}</div>`;
      const botReply = handleChatInput(text);
      const botBubble = `<div class="bg-pink-100 text-black p-3 rounded-lg w-fit max-w-[80%]">${botReply}</div>`;

      chatBox.innerHTML += userBubble + botBubble;
      input.value = '';
      chatBox.scrollTop = chatBox.scrollHeight;
    }

    sendBtn.addEventListener('click', sendMessage);

    input.addEventListener('keydown', (e) => {
      if (e.key === 'Enter') sendMessage();
    });
  }
});
