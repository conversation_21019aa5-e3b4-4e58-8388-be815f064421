// Chatbot functionality for GetTwisted Hair Studios

let chatbotKnowledge = [];
let servicesPricing = [];

// Load chatbot Q&A and services pricing from JSON files
function loadChatbotData() {
  // Load chatbot knowledge
  fetch('assets/data/chatbot-knowledge.json')
    .then(response => response.json())
    .then(data => {
      chatbotKnowledge = data;
      console.log("Chatbot knowledge loaded:", chatbotKnowledge.length, "items");
    })
    .catch(err => {
      console.error("Error loading chatbot knowledge:", err);
      // Fallback to basic knowledge if file can't be loaded
      chatbotKnowledge = [
        {
          "question": "What services do you offer?",
          "answer": "We specialize in natural hair care including locs, braids, twists, and barber services. Visit our Services page for a complete list with pricing."
        },
        {
          "question": "How do I book an appointment?",
          "answer": "You can book an appointment through our website by clicking the 'Book Appointment' button, or by texting us directly at (*************."
        },
        {
          "question": "Where are you located?",
          "answer": "We have locations in Teaneck, NJ and Pottstown, PA. Please contact us for specific address information."
        }
      ];
    });

  // Load services and pricing data
  fetch('assets/data/services-pricing.json')
    .then(response => response.json())
    .then(data => {
      servicesPricing = data;
      console.log("Services pricing loaded:", servicesPricing.categories?.length, "categories");
    })
    .catch(err => {
      console.error("Error loading services pricing:", err);
    });
}

// Clean text for better matching
function sanitize(text) {
  return text.toLowerCase().replace(/[^\w\s]/gi, '').trim();
}

// Find service information based on user query
function findServiceInfo(query) {
  if (!servicesPricing.categories || servicesPricing.categories.length === 0) {
    return null;
  }

  const cleanQuery = sanitize(query);
  let bestMatch = null;
  let highestScore = 0;

  // Common service keywords to improve matching
  const serviceKeywords = {
    'box braids': ['box braid', 'box', 'boxbraid'],
    'knotless': ['knotless', 'knot less', 'no knot'],
    'loc': ['loc', 'lock', 'dread', 'dreadlock'],
    'retwist': ['retwist', 'twist', 're-twist', 'maintenance'],
    'starter': ['starter', 'start', 'new', 'begin', 'install'],
    'cornrow': ['cornrow', 'corn row', 'feed in', 'feedin'],
    'twist': ['twist', 'twists', 'two strand'],
    'color': ['color', 'dye', 'tint', 'highlight'],
    'cut': ['cut', 'trim', 'shape', 'barber'],
    'style': ['style', 'updo', 'design', 'pattern']
  };

  // Check for service keywords in the query
  let detectedKeywords = [];
  for (const [key, variants] of Object.entries(serviceKeywords)) {
    if (variants.some(variant => cleanQuery.includes(variant))) {
      detectedKeywords.push(key);
    }
  }

  // Search through all categories and services
  servicesPricing.categories.forEach(category => {
    category.services.forEach(service => {
      // Calculate match score based on service name and description
      let score = 0;
      const serviceName = sanitize(service.name);
      const serviceDesc = sanitize(service.description);

      // Check for exact matches in name (highest priority)
      if (serviceName === cleanQuery) {
        score += 10;
      }

      // Check for partial matches in name
      if (serviceName.includes(cleanQuery) || cleanQuery.includes(serviceName)) {
        score += 5;
      }

      // Check for detected keywords in service name
      detectedKeywords.forEach(keyword => {
        if (serviceName.includes(keyword)) {
          score += 3;
        }
      });

      // Check for word matches in name
      const queryWords = cleanQuery.split(' ');
      const nameWords = serviceName.split(' ');

      queryWords.forEach(word => {
        if (word.length > 3 && nameWords.some(nameWord => nameWord.includes(word))) {
          score += 2;
        }
      });

      // Check for matches in description
      if (serviceDesc.includes(cleanQuery)) {
        score += 3;
      }

      // If this service has a better match score, update bestMatch
      if (score > highestScore) {
        highestScore = score;
        bestMatch = {
          category: category.name,
          service: service.name,
          price: service.price_range,
          time: service.time_range,
          notes: service.notes
        };
      }
    });
  });

  // Only return if we have a reasonable match
  return highestScore >= 2 ? bestMatch : null;
}

// Handle chatbot input
function handleChatInput(inputText) {
  const cleanInput = sanitize(inputText);
  console.log('Processing query:', cleanInput);

  // Check for greetings
  if (['hi', 'hello', 'hey', 'whats up', 'sup', 'yo'].includes(cleanInput)) {
    return "Hi there! 👋 I'm TwistyBot, here to help with questions about our hair services, pricing, and booking. How can I assist you today?";
  }

  // Check for pricing or service inquiries
  const isPricingQuestion = cleanInput.includes('price') || cleanInput.includes('cost') ||
      cleanInput.includes('how much') || cleanInput.includes('pricing') ||
      cleanInput.includes('charge') || cleanInput.includes('fee') ||
      cleanInput.includes('pay') || cleanInput.includes('dollar') ||
      cleanInput.includes('$');

  const isServiceQuestion = cleanInput.includes('service') || cleanInput.includes('offer') ||
      cleanInput.includes('do you do') || cleanInput.includes('can you do') ||
      cleanInput.includes('provide');

  if (isPricingQuestion || isServiceQuestion) {
    // Try to find specific service information
    const serviceInfo = findServiceInfo(cleanInput);
    console.log('Service match found:', serviceInfo);

    if (serviceInfo) {
      return `For ${serviceInfo.service} (${serviceInfo.category}), our pricing is ${serviceInfo.price} and typically takes ${serviceInfo.time}. ${serviceInfo.notes}. Would you like to book an appointment?`;
    }

    // If no specific service found but asking about services
    if (isServiceQuestion) {
      return "We offer a wide range of services including locs, braids, twists, barber services, and natural hair care. Is there a specific service you're interested in?";
    }

    // If asking about pricing but no specific service identified
    if (isPricingQuestion) {
      // Check for common service keywords to give a more helpful response
      if (cleanInput.includes('braid') || cleanInput.includes('plait')) {
        if (cleanInput.includes('box')) {
          return "Box braids start at $150 for shoulder length, $180-$220 for mid-back length, and $220-$280 for waist length. The service takes 4-8 hours depending on size and length. Would you like to book an appointment?";
        } else if (cleanInput.includes('knotless')) {
          return "Knotless braids start at $180 for shoulder length, $220-$260 for mid-back length, and $260-$320 for waist length. The service takes 4-9 hours depending on size and length. Would you like to book an appointment?";
        } else {
          return "Our braiding services range from $80-$320 depending on the style, length, and complexity. This includes box braids, knotless braids, cornrows, feed-ins, and more. Which specific braiding style are you interested in?";
        }
      } else if (cleanInput.includes('loc') || cleanInput.includes('lock') || cleanInput.includes('dread')) {
        if (cleanInput.includes('start') || cleanInput.includes('new') || cleanInput.includes('install')) {
          return "Starter locs begin at $150-$200 depending on hair length and method. We offer comb coils, two-strand twists, and interlocking. A consultation is recommended for a precise quote. Would you like to book a consultation?";
        } else if (cleanInput.includes('retwist') || cleanInput.includes('maintenance')) {
          return "Loc retwists start at $85 for short locs, $100-$125 for medium length, and $125-$150 for longer locs. The service typically takes 1-3 hours depending on length. Would you like to book an appointment?";
        } else {
          return "Our loc services range from $85-$225 depending on the service, length, and complexity. This includes starter locs, retwists, styling, and repairs. Which specific loc service are you interested in?";
        }
      }

      return "Our prices vary based on the service, hair length, and complexity. Could you tell me which specific service you're interested in? For example: box braids, starter locs, haircuts, etc.";
    }
  }

  // Check for walk-in questions
  if (cleanInput.includes('walk in') || cleanInput.includes('walkin') ||
      cleanInput.includes('appointment needed') || cleanInput.includes('need appointment') ||
      cleanInput.includes('need to book') || cleanInput.includes('drop in')) {
    return "Yes, walk-ins are welcome for our barber services! For other services like braiding, locs, and twists, we recommend booking an appointment to ensure availability. You can book through our website or by texting us at (*************.";
  }

  // Check for consultation requests
  if (cleanInput.includes('consult') || cleanInput.includes('advice') ||
      cleanInput.includes('recommend') || cleanInput.includes('appointment') ||
      cleanInput.includes('book')) {
    return "We offer free consultations to discuss your hair goals and provide personalized recommendations. Loc extension consultations are always FREE. You can book a consultation through our website by clicking the 'Book Appointment' button or by texting us at (*************.";
  }

  // Try exact match
  let match = chatbotKnowledge.find(item =>
    sanitize(item.question) === cleanInput
  );

  // Fallback to keyword matching
  if (!match) {
    // Split input into words and look for matches with any question keywords
    const inputWords = cleanInput.split(' ');

    // Score each knowledge item based on word matches
    const scoredMatches = chatbotKnowledge.map(item => {
      const questionWords = sanitize(item.question).split(' ');
      let score = 0;

      // Count matching words
      inputWords.forEach(word => {
        if (word.length > 3 && questionWords.includes(word)) { // Only count words longer than 3 chars
          score++;
        }
      });

      return { item, score };
    });

    // Sort by score and get the best match if score > 0
    const bestMatch = scoredMatches.sort((a, b) => b.score - a.score)[0];
    if (bestMatch && bestMatch.score > 0) {
      match = bestMatch.item;
    }
  }

  return match
    ? match.answer
    : "I'm here to help with questions about our hair services, pricing, and booking. If you have specific questions about treatments, pricing, or would like to book a consultation, please let me know. You can also text us directly at (*************.";
}

document.addEventListener("DOMContentLoaded", () => {
  loadChatbotData();

  const input = document.getElementById('chatInput');
  const sendBtn = document.getElementById('sendBtn');
  const chatBox = document.querySelector('#chatbot .p-4.h-60');

  if (input && sendBtn && chatBox) {
    function sendMessage() {
      const text = input.value.trim();
      if (!text) return;

      const userBubble = `<div class="bg-yellow-100 text-black p-3 rounded-lg w-fit max-w-[80%] ml-auto">${text}</div>`;
      const botReply = handleChatInput(text);
      const botBubble = `<div class="bg-pink-100 text-black p-3 rounded-lg w-fit max-w-[80%]">${botReply}</div>`;

      chatBox.innerHTML += userBubble + botBubble;
      input.value = '';
      chatBox.scrollTop = chatBox.scrollHeight;
    }

    sendBtn.addEventListener('click', sendMessage);

    input.addEventListener('keydown', (e) => {
      if (e.key === 'Enter') sendMessage();
    });
  }
});
