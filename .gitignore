# Dependencies
node_modules/
npm-debug.log
yarn-error.log
yarn-debug.log
package-lock.json
yarn.lock

# Environment variables and secrets
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
*.pem
*.key
*.cert
*.p12
*.pfx
*.password
*.secret

# Build files
/admin/build
/dist

# IDE and editor files
.idea/
.vscode/
*.swp
*.swo
.DS_Store

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Testing
/coverage

# Misc
.DS_Store
Thumbs.db
