const Contact = require('../models/Contact');

// @desc    Submit a contact form
// @route   POST /api/contact
// @access  Public
exports.submitContactForm = async (req, res) => {
  try {
    const { name, email, subject, message } = req.body;
    
    // Validate input
    if (!name || !email || !subject || !message) {
      return res.status(400).json({
        success: false,
        error: 'Please provide all required fields'
      });
    }
    
    // Create contact entry
    const contact = await Contact.create({
      name,
      email,
      subject,
      message
    });
    
    // Send email notification (would be implemented in production)
    // await sendEmail({
    //   to: '<EMAIL>',
    //   subject: `New Contact Form: ${subject}`,
    //   text: `Name: ${name}\nEmail: ${email}\nMessage: ${message}`
    // });
    
    res.status(201).json({
      success: true,
      data: contact
    });
  } catch (error) {
    console.error('Error submitting contact form:', error);
    
    // Handle validation errors
    if (error.name === 'ValidationError') {
      const messages = Object.values(error.errors).map(val => val.message);
      return res.status(400).json({
        success: false,
        error: messages
      });
    }
    
    res.status(500).json({
      success: false,
      error: 'Server Error'
    });
  }
};
