/**
 * Secure Build Script for GetTwisted Hair Studios
 *
 * This script:
 * 1. Ensures all HTML files have security headers
 * 2. Checks for hardcoded API endpoints and other security issues
 * 3. Validates file integrity and security compliance
 */

const fs = require('fs');
const path = require('path');
const glob = require('glob');

// Security headers that should be in every HTML file
const SECURITY_HEADERS = `
  <!-- Security Headers -->
  <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline' https://cdn.tailwindcss.com https://unpkg.com https://www.googletagmanager.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://unpkg.com; font-src 'self' https://fonts.gstatic.com; frame-src https://*.square.site/ https://*.squareup.com/; connect-src https://*.square.site/ https://*.squareup.com/; img-src 'self' https://*.square.site/ https://*.squareup.com/ data:; object-src 'none';">
  <meta http-equiv="X-Content-Type-Options" content="nosniff">
  <meta http-equiv="Referrer-Policy" content="no-referrer-when-downgrade">
  <!-- <meta http-equiv="X-Frame-Options" content="DENY"> -->
  <meta http-equiv="Permissions-Policy" content="geolocation=(), microphone=(), camera=()">
`;

// Function to ensure HTML files have security headers
function ensureSecurityHeaders(filePath) {
  let content = fs.readFileSync(filePath, 'utf8');

  // Check if security headers already exist
  if (!content.includes('Content-Security-Policy')) {
    // Find the position after the opening head tag
    const headPos = content.indexOf('</head>');
    if (headPos !== -1) {
      // Insert security headers before closing head tag
      content = content.slice(0, headPos) + SECURITY_HEADERS + content.slice(headPos);
      fs.writeFileSync(filePath, content);
      console.log(`✅ Added security headers to ${filePath}`);
    } else {
      console.warn(`⚠️ Could not find </head> in ${filePath}`);
    }
  } else {
    // Headers exist but may need updating
    // Check if we need to update the CSP for Square integration
    if (!content.includes('frame-src') && !content.includes('square.site')) {
      // Replace the existing CSP with our updated one
      content = content.replace(
        /<meta http-equiv="Content-Security-Policy"[^>]*>/,
        `<meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline' https://cdn.tailwindcss.com https://unpkg.com https://www.googletagmanager.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://unpkg.com; font-src 'self' https://fonts.gstatic.com; frame-src https://*.square.site/ https://*.squareup.com/; connect-src https://*.square.site/ https://*.squareup.com/; img-src 'self' https://*.square.site/ https://*.squareup.com/ data:; object-src 'none';">`
      );

      // Comment out X-Frame-Options if it exists
      content = content.replace(
        /<meta http-equiv="X-Frame-Options" content="DENY">/,
        `<!-- <meta http-equiv="X-Frame-Options" content="DENY"> -->`
      );

      fs.writeFileSync(filePath, content);
      console.log(`✅ Updated security headers in ${filePath} for Square integration`);
    } else {
      console.log(`✓ Security headers already present and up-to-date in ${filePath}`);
    }
  }
}

// Function to check for hardcoded API endpoints
function checkForHardcodedEndpoints(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');

  // Check for hardcoded localhost URLs
  const localhostMatches = content.match(/http:\/\/localhost:[0-9]+/g);
  if (localhostMatches) {
    console.warn(`⚠️ Found hardcoded localhost URLs in ${filePath}:`);
    localhostMatches.forEach(match => console.warn(`   - ${match}`));
  }

  // Check for hardcoded API endpoints
  const apiMatches = content.match(/fetch\(['"]https?:\/\/[^'"]+['"]\)/g);
  if (apiMatches) {
    console.warn(`⚠️ Found hardcoded API endpoints in ${filePath}:`);
    apiMatches.forEach(match => console.warn(`   - ${match}`));
  }
}

// Main function
function main() {
  console.log('🔒 Running secure build process...');

  // Process all HTML files
  const htmlFiles = glob.sync('**/*.html', { ignore: 'node_modules/**' });
  htmlFiles.forEach(file => {
    const filePath = path.resolve(file);
    ensureSecurityHeaders(filePath);
    checkForHardcodedEndpoints(filePath);
  });

  console.log('✅ Secure build completed');
}

main();
