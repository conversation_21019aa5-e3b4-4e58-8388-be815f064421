<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8"/>
    <title id="head-title">report_20250517_000256.html</title>
      <style type="text/css">body {
  font-family: Helvetica, Arial, sans-serif;
  font-size: 12px;
  /* do not increase min-width as some may use split screens */
  min-width: 800px;
  color: #999;
}

h1 {
  font-size: 24px;
  color: black;
}

h2 {
  font-size: 16px;
  color: black;
}

p {
  color: black;
}

a {
  color: #999;
}

table {
  border-collapse: collapse;
}

/******************************
 * SUMMARY INFORMATION
 ******************************/
#environment td {
  padding: 5px;
  border: 1px solid #e6e6e6;
  vertical-align: top;
}
#environment tr:nth-child(odd) {
  background-color: #f6f6f6;
}
#environment ul {
  margin: 0;
  padding: 0 20px;
}

/******************************
 * TEST RESULT COLORS
 ******************************/
span.passed,
.passed .col-result {
  color: green;
}

span.skipped,
span.xfailed,
span.rerun,
.skipped .col-result,
.xfailed .col-result,
.rerun .col-result {
  color: orange;
}

span.error,
span.failed,
span.xpassed,
.error .col-result,
.failed .col-result,
.xpassed .col-result {
  color: red;
}

.col-links__extra {
  margin-right: 3px;
}

/******************************
 * RESULTS TABLE
 *
 * 1. Table Layout
 * 2. Extra
 * 3. Sorting items
 *
 ******************************/
/*------------------
 * 1. Table Layout
 *------------------*/
#results-table {
  border: 1px solid #e6e6e6;
  color: #999;
  font-size: 12px;
  width: 100%;
}
#results-table th,
#results-table td {
  padding: 5px;
  border: 1px solid #e6e6e6;
  text-align: left;
}
#results-table th {
  font-weight: bold;
}

/*------------------
 * 2. Extra
 *------------------*/
.logwrapper {
  max-height: 230px;
  overflow-y: scroll;
  background-color: #e6e6e6;
}
.logwrapper.expanded {
  max-height: none;
}
.logwrapper.expanded .logexpander:after {
  content: "collapse [-]";
}
.logwrapper .logexpander {
  z-index: 1;
  position: sticky;
  top: 10px;
  width: max-content;
  border: 1px solid;
  border-radius: 3px;
  padding: 5px 7px;
  margin: 10px 0 10px calc(100% - 80px);
  cursor: pointer;
  background-color: #e6e6e6;
}
.logwrapper .logexpander:after {
  content: "expand [+]";
}
.logwrapper .logexpander:hover {
  color: #000;
  border-color: #000;
}
.logwrapper .log {
  min-height: 40px;
  position: relative;
  top: -50px;
  height: calc(100% + 50px);
  border: 1px solid #e6e6e6;
  color: black;
  display: block;
  font-family: "Courier New", Courier, monospace;
  padding: 5px;
  padding-right: 80px;
  white-space: pre-wrap;
}

div.media {
  border: 1px solid #e6e6e6;
  float: right;
  height: 240px;
  margin: 0 5px;
  overflow: hidden;
  width: 320px;
}

.media-container {
  display: grid;
  grid-template-columns: 25px auto 25px;
  align-items: center;
  flex: 1 1;
  overflow: hidden;
  height: 200px;
}

.media-container--fullscreen {
  grid-template-columns: 0px auto 0px;
}

.media-container__nav--right,
.media-container__nav--left {
  text-align: center;
  cursor: pointer;
}

.media-container__viewport {
  cursor: pointer;
  text-align: center;
  height: inherit;
}
.media-container__viewport img,
.media-container__viewport video {
  object-fit: cover;
  width: 100%;
  max-height: 100%;
}

.media__name,
.media__counter {
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  flex: 0 0 25px;
  align-items: center;
}

.collapsible td:not(.col-links) {
  cursor: pointer;
}
.collapsible td:not(.col-links):hover::after {
  color: #bbb;
  font-style: italic;
  cursor: pointer;
}

.col-result {
  width: 130px;
}
.col-result:hover::after {
  content: " (hide details)";
}

.col-result.collapsed:hover::after {
  content: " (show details)";
}

#environment-header h2:hover::after {
  content: " (hide details)";
  color: #bbb;
  font-style: italic;
  cursor: pointer;
  font-size: 12px;
}

#environment-header.collapsed h2:hover::after {
  content: " (show details)";
  color: #bbb;
  font-style: italic;
  cursor: pointer;
  font-size: 12px;
}

/*------------------
 * 3. Sorting items
 *------------------*/
.sortable {
  cursor: pointer;
}
.sortable.desc:after {
  content: " ";
  position: relative;
  left: 5px;
  bottom: -12.5px;
  border: 10px solid #4caf50;
  border-bottom: 0;
  border-left-color: transparent;
  border-right-color: transparent;
}
.sortable.asc:after {
  content: " ";
  position: relative;
  left: 5px;
  bottom: 12.5px;
  border: 10px solid #4caf50;
  border-top: 0;
  border-left-color: transparent;
  border-right-color: transparent;
}

.hidden, .summary__reload__button.hidden {
  display: none;
}

.summary__data {
  flex: 0 0 550px;
}
.summary__reload {
  flex: 1 1;
  display: flex;
  justify-content: center;
}
.summary__reload__button {
  flex: 0 0 300px;
  display: flex;
  color: white;
  font-weight: bold;
  background-color: #4caf50;
  text-align: center;
  justify-content: center;
  align-items: center;
  border-radius: 3px;
  cursor: pointer;
}
.summary__reload__button:hover {
  background-color: #46a049;
}
.summary__spacer {
  flex: 0 0 550px;
}

.controls {
  display: flex;
  justify-content: space-between;
}

.filters,
.collapse {
  display: flex;
  align-items: center;
}
.filters button,
.collapse button {
  color: #999;
  border: none;
  background: none;
  cursor: pointer;
  text-decoration: underline;
}
.filters button:hover,
.collapse button:hover {
  color: #ccc;
}

.filter__label {
  margin-right: 10px;
}

      </style>
    
  
  <!-- Security Headers -->
  <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline' https://cdn.tailwindcss.com https://unpkg.com https://www.googletagmanager.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://unpkg.com; font-src 'self' https://fonts.gstatic.com; frame-src https://*.square.site/ https://*.squareup.com/; connect-src https://*.square.site/ https://*.squareup.com/; img-src 'self' https://*.square.site/ https://*.squareup.com/ data:; object-src 'none';">
  <meta http-equiv="X-Content-Type-Options" content="nosniff">
  <meta http-equiv="Referrer-Policy" content="no-referrer-when-downgrade">
  <!-- <meta http-equiv="X-Frame-Options" content="DENY"> -->
  <meta http-equiv="Permissions-Policy" content="geolocation=(), microphone=(), camera=()">
</head>
  <body>
    <h1 id="title">report_20250517_000256.html</h1>
    <p>Report generated on 17-May-2025 at 00:03:06 by <a href="https://pypi.python.org/pypi/pytest-html">pytest-html</a>
        v4.1.1</p>
    <div id="environment-header">
      <h2>Environment</h2>
    </div>
    <table id="environment"></table>
    <!-- TEMPLATES -->
      <template id="template_environment_row">
      <tr>
        <td></td>
        <td></td>
      </tr>
    </template>
    <template id="template_results-table__body--empty">
      <tbody class="results-table-row">
        <tr id="not-found-message">
          <td colspan="4">No results found. Check the filters.</th>
        </tr>
    </template>
    <template id="template_results-table__tbody">
      <tbody class="results-table-row">
        <tr class="collapsible">
        </tr>
        <tr class="extras-row">
          <td class="extra" colspan="4">
            <div class="extraHTML"></div>
            <div class="media">
              <div class="media-container">
                  <div class="media-container__nav--left"><</div>
                  <div class="media-container__viewport">
                    <img src="" />
                    <video controls>
                      <source src="" type="video/mp4">
                    </video>
                  </div>
                  <div class="media-container__nav--right">></div>
                </div>
                <div class="media__name"></div>
                <div class="media__counter"></div>
            </div>
            <div class="logwrapper">
              <div class="logexpander"></div>
              <div class="log"></div>
            </div>
          </td>
        </tr>
      </tbody>
    </template>
    <!-- END TEMPLATES -->
    <div class="summary">
      <div class="summary__data">
        <h2>Summary</h2>
        <div class="additional-summary prefix">
        </div>
        <p class="run-count">0 test took 00:00:08.</p>
        <p class="filter">(Un)check the boxes to filter the results.</p>
        <div class="summary__reload">
          <div class="summary__reload__button hidden" onclick="location.reload()">
            <div>There are still tests running. <br />Reload this page to get the latest results!</div>
          </div>
        </div>
        <div class="summary__spacer"></div>
        <div class="controls">
          <div class="filters">
            <input checked="true" class="filter" name="filter_checkbox" type="checkbox" data-test-result="failed" disabled/>
            <span class="failed">0 Failed,</span>
            <input checked="true" class="filter" name="filter_checkbox" type="checkbox" data-test-result="passed" disabled/>
            <span class="passed">0 Passed,</span>
            <input checked="true" class="filter" name="filter_checkbox" type="checkbox" data-test-result="skipped" disabled/>
            <span class="skipped">0 Skipped,</span>
            <input checked="true" class="filter" name="filter_checkbox" type="checkbox" data-test-result="xfailed" disabled/>
            <span class="xfailed">0 Expected failures,</span>
            <input checked="true" class="filter" name="filter_checkbox" type="checkbox" data-test-result="xpassed" disabled/>
            <span class="xpassed">0 Unexpected passes,</span>
            <input checked="true" class="filter" name="filter_checkbox" type="checkbox" data-test-result="error" />
            <span class="error">5 Errors,</span>
            <input checked="true" class="filter" name="filter_checkbox" type="checkbox" data-test-result="rerun" disabled/>
            <span class="rerun">0 Reruns</span>
          </div>
          <div class="collapse">
            <button id="show_all_details">Show all details</button>&nbsp;/&nbsp;<button id="hide_all_details">Hide all details</button>
          </div>
        </div>
      </div>
      <div class="additional-summary summary">
      </div>
      <div class="additional-summary postfix">
      </div>
    </div>
    <table id="results-table">
      <thead id="results-table-head">
        <tr>
          <th class="sortable" data-column-type="result">Result</th>
          <th class="sortable" data-column-type="testId">Test</th>
          <th class="sortable" data-column-type="duration">Duration</th>
          <th>Links</th>
        </tr>
      </thead>
    </table>
  </body>
  <footer>
    <div id="data-container" data-jsonblob="{&#34;environment&#34;: {&#34;Python&#34;: &#34;3.9.13&#34;, &#34;Platform&#34;: &#34;Windows-10-10.0.26100-SP0&#34;, &#34;Packages&#34;: {&#34;pytest&#34;: &#34;7.4.3&#34;, &#34;pluggy&#34;: &#34;1.5.0&#34;}, &#34;Plugins&#34;: {&#34;anyio&#34;: &#34;4.9.0&#34;, &#34;dash&#34;: &#34;3.0.4&#34;, &#34;html&#34;: &#34;4.1.1&#34;, &#34;metadata&#34;: &#34;3.1.1&#34;}}, &#34;tests&#34;: {&#34;test_gallery.py::test_gallery_page_loads&#34;: [{&#34;extras&#34;: [], &#34;result&#34;: &#34;Error&#34;, &#34;testId&#34;: &#34;test_gallery.py::test_gallery_page_loads::setup&#34;, &#34;duration&#34;: &#34;00:00:02&#34;, &#34;resultsTableRow&#34;: [&#34;&lt;td class=\&#34;col-result\&#34;&gt;Error&lt;/td&gt;&#34;, &#34;&lt;td class=\&#34;col-testId\&#34;&gt;test_gallery.py::test_gallery_page_loads::setup&lt;/td&gt;&#34;, &#34;&lt;td class=\&#34;col-duration\&#34;&gt;00:00:02&lt;/td&gt;&#34;, &#34;&lt;td class=\&#34;col-links\&#34;&gt;&lt;/td&gt;&#34;], &#34;log&#34;: &#34;@pytest.fixture(scope=&amp;quot;function&amp;quot;)\n    def driver():\n        &amp;quot;&amp;quot;&amp;quot;\n        Fixture to create and return a Chrome WebDriver instance.\n        The driver is automatically closed after the test.\n        &amp;quot;&amp;quot;&amp;quot;\n        chrome_options = Options()\n        # Uncomment the line below to run tests in headless mode\n        # chrome_options.add_argument(&amp;quot;--headless&amp;quot;)\n        chrome_options.add_argument(&amp;quot;--window-size=1920,1080&amp;quot;)\n    \n        # Create the WebDriver instance\n&amp;gt;       driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=chrome_options)\n\nconftest.py:31: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n..\\..\\..\\..\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\selenium\\webdriver\\chrome\\webdriver.py:45: in __init__\n    super().__init__(\n..\\..\\..\\..\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\selenium\\webdriver\\chromium\\webdriver.py:53: in __init__\n    self.service.start()\n..\\..\\..\\..\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\selenium\\webdriver\\common\\service.py:98: in start\n    self._start_process(self._path)\n..\\..\\..\\..\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\selenium\\webdriver\\common\\service.py:204: in _start_process\n    self.process = subprocess.Popen(\n..\\..\\..\\..\\AppData\\Local\\Programs\\Python\\Python39\\lib\\subprocess.py:951: in __init__\n    self._execute_child(args, executable, preexec_fn, close_fds,\n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n\nself = &amp;lt;Popen: returncode: None args: [&amp;#x27;C:\\\\Users\\\\<USER>\\\\.wdm\\\\drivers\\\\chromedriv...&amp;gt;\nargs = &amp;#x27;C:\\\\Users\\\\<USER>\\\\.wdm\\\\drivers\\\\chromedriver\\\\win64\\\\136.0.7103.94\\\\chromedriver-win32/THIRD_PARTY_NOTICES.chromedriver --port=62359&amp;#x27;\nexecutable = None, preexec_fn = None, close_fds = False, pass_fds = (), cwd = None\nenv = environ({&amp;#x27;ALLUSERSPROFILE&amp;#x27;: &amp;#x27;C:\\\\ProgramData&amp;#x27;, &amp;#x27;APPDATA&amp;#x27;: &amp;#x27;C:\\\\Users\\\\<USER>\\\\AppData\\\\Roaming&amp;#x27;, &amp;#x27;CHROME_CRASHPAD_PIPE_...d\\\\libs\\\\debugpy&amp;#x27;, &amp;#x27;VSCODE_INJECTION&amp;#x27;: &amp;#x27;1&amp;#x27;, &amp;#x27;PYTEST_CURRENT_TEST&amp;#x27;: &amp;#x27;test_gallery.py::test_gallery_page_loads (setup)&amp;#x27;})\nstartupinfo = &amp;lt;subprocess.STARTUPINFO object at 0x000001EF341E42B0&amp;gt;, creationflags = 0, shell = False\np2cread = Handle(8), p2cwrite = 15, c2pread = -1, c2pwrite = Handle(440), errread = -1, errwrite = Handle(492)\nunused_restore_signals = True, unused_gid = None, unused_gids = None, unused_uid = None, unused_umask = -1\nunused_start_new_session = False\n\n    def _execute_child(self, args, executable, preexec_fn, close_fds,\n                       pass_fds, cwd, env,\n                       startupinfo, creationflags, shell,\n                       p2cread, p2cwrite,\n                       c2pread, c2pwrite,\n                       errread, errwrite,\n                       unused_restore_signals,\n                       unused_gid, unused_gids, unused_uid,\n                       unused_umask,\n                       unused_start_new_session):\n        &amp;quot;&amp;quot;&amp;quot;Execute program (MS Windows version)&amp;quot;&amp;quot;&amp;quot;\n    \n        assert not pass_fds, &amp;quot;pass_fds not supported on Windows.&amp;quot;\n    \n        if isinstance(args, str):\n            pass\n        elif isinstance(args, bytes):\n            if shell:\n                raise TypeError(&amp;#x27;bytes args is not allowed on Windows&amp;#x27;)\n            args = list2cmdline([args])\n        elif isinstance(args, os.PathLike):\n            if shell:\n                raise TypeError(&amp;#x27;path-like args is not allowed when &amp;#x27;\n                                &amp;#x27;shell is true&amp;#x27;)\n            args = list2cmdline([args])\n        else:\n            args = list2cmdline(args)\n    \n        if executable is not None:\n            executable = os.fsdecode(executable)\n    \n        # Process startup details\n        if startupinfo is None:\n            startupinfo = STARTUPINFO()\n        else:\n            # bpo-34044: Copy STARTUPINFO since it is modified above,\n            # so the caller can reuse it multiple times.\n            startupinfo = startupinfo.copy()\n    \n        use_std_handles = -1 not in (p2cread, c2pwrite, errwrite)\n        if use_std_handles:\n            startupinfo.dwFlags |= _winapi.STARTF_USESTDHANDLES\n            startupinfo.hStdInput = p2cread\n            startupinfo.hStdOutput = c2pwrite\n            startupinfo.hStdError = errwrite\n    \n        attribute_list = startupinfo.lpAttributeList\n        have_handle_list = bool(attribute_list and\n                                &amp;quot;handle_list&amp;quot; in attribute_list and\n                                attribute_list[&amp;quot;handle_list&amp;quot;])\n    \n        # If we were given an handle_list or need to create one\n        if have_handle_list or (use_std_handles and close_fds):\n            if attribute_list is None:\n                attribute_list = startupinfo.lpAttributeList = {}\n            handle_list = attribute_list[&amp;quot;handle_list&amp;quot;] = \\\n                list(attribute_list.get(&amp;quot;handle_list&amp;quot;, []))\n    \n            if use_std_handles:\n                handle_list += [int(p2cread), int(c2pwrite), int(errwrite)]\n    \n            handle_list[:] = self._filter_handle_list(handle_list)\n    \n            if handle_list:\n                if not close_fds:\n                    warnings.warn(&amp;quot;startupinfo.lpAttributeList[&amp;#x27;handle_list&amp;#x27;] &amp;quot;\n                                  &amp;quot;overriding close_fds&amp;quot;, RuntimeWarning)\n    \n                # When using the handle_list we always request to inherit\n                # handles but the only handles that will be inherited are\n                # the ones in the handle_list\n                close_fds = False\n    \n        if shell:\n            startupinfo.dwFlags |= _winapi.STARTF_USESHOWWINDOW\n            startupinfo.wShowWindow = _winapi.SW_HIDE\n            comspec = os.environ.get(&amp;quot;COMSPEC&amp;quot;, &amp;quot;cmd.exe&amp;quot;)\n            args = &amp;#x27;{} /c &amp;quot;{}&amp;quot;&amp;#x27;.format (comspec, args)\n    \n        if cwd is not None:\n            cwd = os.fsdecode(cwd)\n    \n        sys.audit(&amp;quot;subprocess.Popen&amp;quot;, executable, args, cwd, env)\n    \n        # Start the process\n        try:\n&amp;gt;           hp, ht, pid, tid = _winapi.CreateProcess(executable, args,\n                                     # no special security\n                                     None, None,\n                                     int(not close_fds),\n                                     creationflags,\n                                     env,\n                                     cwd,\n                                     startupinfo)\nE                                    OSError: [WinError 193] %1 is not a valid Win32 application\n\n..\\..\\..\\..\\AppData\\Local\\Programs\\Python\\Python39\\lib\\subprocess.py:1420: OSError\n&#34;}], &#34;test_gallery.py::test_style_filter_buttons&#34;: [{&#34;extras&#34;: [], &#34;result&#34;: &#34;Error&#34;, &#34;testId&#34;: &#34;test_gallery.py::test_style_filter_buttons::setup&#34;, &#34;duration&#34;: &#34;00:00:02&#34;, &#34;resultsTableRow&#34;: [&#34;&lt;td class=\&#34;col-result\&#34;&gt;Error&lt;/td&gt;&#34;, &#34;&lt;td class=\&#34;col-testId\&#34;&gt;test_gallery.py::test_style_filter_buttons::setup&lt;/td&gt;&#34;, &#34;&lt;td class=\&#34;col-duration\&#34;&gt;00:00:02&lt;/td&gt;&#34;, &#34;&lt;td class=\&#34;col-links\&#34;&gt;&lt;/td&gt;&#34;], &#34;log&#34;: &#34;@pytest.fixture(scope=&amp;quot;function&amp;quot;)\n    def driver():\n        &amp;quot;&amp;quot;&amp;quot;\n        Fixture to create and return a Chrome WebDriver instance.\n        The driver is automatically closed after the test.\n        &amp;quot;&amp;quot;&amp;quot;\n        chrome_options = Options()\n        # Uncomment the line below to run tests in headless mode\n        # chrome_options.add_argument(&amp;quot;--headless&amp;quot;)\n        chrome_options.add_argument(&amp;quot;--window-size=1920,1080&amp;quot;)\n    \n        # Create the WebDriver instance\n&amp;gt;       driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=chrome_options)\n\nconftest.py:31: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n..\\..\\..\\..\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\selenium\\webdriver\\chrome\\webdriver.py:45: in __init__\n    super().__init__(\n..\\..\\..\\..\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\selenium\\webdriver\\chromium\\webdriver.py:53: in __init__\n    self.service.start()\n..\\..\\..\\..\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\selenium\\webdriver\\common\\service.py:98: in start\n    self._start_process(self._path)\n..\\..\\..\\..\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\selenium\\webdriver\\common\\service.py:204: in _start_process\n    self.process = subprocess.Popen(\n..\\..\\..\\..\\AppData\\Local\\Programs\\Python\\Python39\\lib\\subprocess.py:951: in __init__\n    self._execute_child(args, executable, preexec_fn, close_fds,\n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n\nself = &amp;lt;Popen: returncode: None args: [&amp;#x27;C:\\\\Users\\\\<USER>\\\\.wdm\\\\drivers\\\\chromedriv...&amp;gt;\nargs = &amp;#x27;C:\\\\Users\\\\<USER>\\\\.wdm\\\\drivers\\\\chromedriver\\\\win64\\\\136.0.7103.94\\\\chromedriver-win32/THIRD_PARTY_NOTICES.chromedriver --port=62363&amp;#x27;\nexecutable = None, preexec_fn = None, close_fds = False, pass_fds = (), cwd = None\nenv = environ({&amp;#x27;ALLUSERSPROFILE&amp;#x27;: &amp;#x27;C:\\\\ProgramData&amp;#x27;, &amp;#x27;APPDATA&amp;#x27;: &amp;#x27;C:\\\\Users\\\\<USER>\\\\AppData\\\\Roaming&amp;#x27;, &amp;#x27;CHROME_CRASHPAD_PIPE_...\\libs\\\\debugpy&amp;#x27;, &amp;#x27;VSCODE_INJECTION&amp;#x27;: &amp;#x27;1&amp;#x27;, &amp;#x27;PYTEST_CURRENT_TEST&amp;#x27;: &amp;#x27;test_gallery.py::test_style_filter_buttons (setup)&amp;#x27;})\nstartupinfo = &amp;lt;subprocess.STARTUPINFO object at 0x000001EF342D2F10&amp;gt;, creationflags = 0, shell = False\np2cread = Handle(452), p2cwrite = 16, c2pread = -1, c2pwrite = Handle(980), errread = -1, errwrite = Handle(896)\nunused_restore_signals = True, unused_gid = None, unused_gids = None, unused_uid = None, unused_umask = -1\nunused_start_new_session = False\n\n    def _execute_child(self, args, executable, preexec_fn, close_fds,\n                       pass_fds, cwd, env,\n                       startupinfo, creationflags, shell,\n                       p2cread, p2cwrite,\n                       c2pread, c2pwrite,\n                       errread, errwrite,\n                       unused_restore_signals,\n                       unused_gid, unused_gids, unused_uid,\n                       unused_umask,\n                       unused_start_new_session):\n        &amp;quot;&amp;quot;&amp;quot;Execute program (MS Windows version)&amp;quot;&amp;quot;&amp;quot;\n    \n        assert not pass_fds, &amp;quot;pass_fds not supported on Windows.&amp;quot;\n    \n        if isinstance(args, str):\n            pass\n        elif isinstance(args, bytes):\n            if shell:\n                raise TypeError(&amp;#x27;bytes args is not allowed on Windows&amp;#x27;)\n            args = list2cmdline([args])\n        elif isinstance(args, os.PathLike):\n            if shell:\n                raise TypeError(&amp;#x27;path-like args is not allowed when &amp;#x27;\n                                &amp;#x27;shell is true&amp;#x27;)\n            args = list2cmdline([args])\n        else:\n            args = list2cmdline(args)\n    \n        if executable is not None:\n            executable = os.fsdecode(executable)\n    \n        # Process startup details\n        if startupinfo is None:\n            startupinfo = STARTUPINFO()\n        else:\n            # bpo-34044: Copy STARTUPINFO since it is modified above,\n            # so the caller can reuse it multiple times.\n            startupinfo = startupinfo.copy()\n    \n        use_std_handles = -1 not in (p2cread, c2pwrite, errwrite)\n        if use_std_handles:\n            startupinfo.dwFlags |= _winapi.STARTF_USESTDHANDLES\n            startupinfo.hStdInput = p2cread\n            startupinfo.hStdOutput = c2pwrite\n            startupinfo.hStdError = errwrite\n    \n        attribute_list = startupinfo.lpAttributeList\n        have_handle_list = bool(attribute_list and\n                                &amp;quot;handle_list&amp;quot; in attribute_list and\n                                attribute_list[&amp;quot;handle_list&amp;quot;])\n    \n        # If we were given an handle_list or need to create one\n        if have_handle_list or (use_std_handles and close_fds):\n            if attribute_list is None:\n                attribute_list = startupinfo.lpAttributeList = {}\n            handle_list = attribute_list[&amp;quot;handle_list&amp;quot;] = \\\n                list(attribute_list.get(&amp;quot;handle_list&amp;quot;, []))\n    \n            if use_std_handles:\n                handle_list += [int(p2cread), int(c2pwrite), int(errwrite)]\n    \n            handle_list[:] = self._filter_handle_list(handle_list)\n    \n            if handle_list:\n                if not close_fds:\n                    warnings.warn(&amp;quot;startupinfo.lpAttributeList[&amp;#x27;handle_list&amp;#x27;] &amp;quot;\n                                  &amp;quot;overriding close_fds&amp;quot;, RuntimeWarning)\n    \n                # When using the handle_list we always request to inherit\n                # handles but the only handles that will be inherited are\n                # the ones in the handle_list\n                close_fds = False\n    \n        if shell:\n            startupinfo.dwFlags |= _winapi.STARTF_USESHOWWINDOW\n            startupinfo.wShowWindow = _winapi.SW_HIDE\n            comspec = os.environ.get(&amp;quot;COMSPEC&amp;quot;, &amp;quot;cmd.exe&amp;quot;)\n            args = &amp;#x27;{} /c &amp;quot;{}&amp;quot;&amp;#x27;.format (comspec, args)\n    \n        if cwd is not None:\n            cwd = os.fsdecode(cwd)\n    \n        sys.audit(&amp;quot;subprocess.Popen&amp;quot;, executable, args, cwd, env)\n    \n        # Start the process\n        try:\n&amp;gt;           hp, ht, pid, tid = _winapi.CreateProcess(executable, args,\n                                     # no special security\n                                     None, None,\n                                     int(not close_fds),\n                                     creationflags,\n                                     env,\n                                     cwd,\n                                     startupinfo)\nE                                    OSError: [WinError 193] %1 is not a valid Win32 application\n\n..\\..\\..\\..\\AppData\\Local\\Programs\\Python\\Python39\\lib\\subprocess.py:1420: OSError\n&#34;}], &#34;test_gallery.py::test_stylist_filter_buttons&#34;: [{&#34;extras&#34;: [], &#34;result&#34;: &#34;Error&#34;, &#34;testId&#34;: &#34;test_gallery.py::test_stylist_filter_buttons::setup&#34;, &#34;duration&#34;: &#34;00:00:02&#34;, &#34;resultsTableRow&#34;: [&#34;&lt;td class=\&#34;col-result\&#34;&gt;Error&lt;/td&gt;&#34;, &#34;&lt;td class=\&#34;col-testId\&#34;&gt;test_gallery.py::test_stylist_filter_buttons::setup&lt;/td&gt;&#34;, &#34;&lt;td class=\&#34;col-duration\&#34;&gt;00:00:02&lt;/td&gt;&#34;, &#34;&lt;td class=\&#34;col-links\&#34;&gt;&lt;/td&gt;&#34;], &#34;log&#34;: &#34;@pytest.fixture(scope=&amp;quot;function&amp;quot;)\n    def driver():\n        &amp;quot;&amp;quot;&amp;quot;\n        Fixture to create and return a Chrome WebDriver instance.\n        The driver is automatically closed after the test.\n        &amp;quot;&amp;quot;&amp;quot;\n        chrome_options = Options()\n        # Uncomment the line below to run tests in headless mode\n        # chrome_options.add_argument(&amp;quot;--headless&amp;quot;)\n        chrome_options.add_argument(&amp;quot;--window-size=1920,1080&amp;quot;)\n    \n        # Create the WebDriver instance\n&amp;gt;       driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=chrome_options)\n\nconftest.py:31: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n..\\..\\..\\..\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\selenium\\webdriver\\chrome\\webdriver.py:45: in __init__\n    super().__init__(\n..\\..\\..\\..\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\selenium\\webdriver\\chromium\\webdriver.py:53: in __init__\n    self.service.start()\n..\\..\\..\\..\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\selenium\\webdriver\\common\\service.py:98: in start\n    self._start_process(self._path)\n..\\..\\..\\..\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\selenium\\webdriver\\common\\service.py:204: in _start_process\n    self.process = subprocess.Popen(\n..\\..\\..\\..\\AppData\\Local\\Programs\\Python\\Python39\\lib\\subprocess.py:951: in __init__\n    self._execute_child(args, executable, preexec_fn, close_fds,\n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n\nself = &amp;lt;Popen: returncode: None args: [&amp;#x27;C:\\\\Users\\\\<USER>\\\\.wdm\\\\drivers\\\\chromedriv...&amp;gt;\nargs = &amp;#x27;C:\\\\Users\\\\<USER>\\\\.wdm\\\\drivers\\\\chromedriver\\\\win64\\\\136.0.7103.94\\\\chromedriver-win32/THIRD_PARTY_NOTICES.chromedriver --port=62368&amp;#x27;\nexecutable = None, preexec_fn = None, close_fds = False, pass_fds = (), cwd = None\nenv = environ({&amp;#x27;ALLUSERSPROFILE&amp;#x27;: &amp;#x27;C:\\\\ProgramData&amp;#x27;, &amp;#x27;APPDATA&amp;#x27;: &amp;#x27;C:\\\\Users\\\\<USER>\\\\AppData\\\\Roaming&amp;#x27;, &amp;#x27;CHROME_CRASHPAD_PIPE_...ibs\\\\debugpy&amp;#x27;, &amp;#x27;VSCODE_INJECTION&amp;#x27;: &amp;#x27;1&amp;#x27;, &amp;#x27;PYTEST_CURRENT_TEST&amp;#x27;: &amp;#x27;test_gallery.py::test_stylist_filter_buttons (setup)&amp;#x27;})\nstartupinfo = &amp;lt;subprocess.STARTUPINFO object at 0x000001EF34148F40&amp;gt;, creationflags = 0, shell = False\np2cread = Handle(896), p2cwrite = 16, c2pread = -1, c2pwrite = Handle(412), errread = -1, errwrite = Handle(880)\nunused_restore_signals = True, unused_gid = None, unused_gids = None, unused_uid = None, unused_umask = -1\nunused_start_new_session = False\n\n    def _execute_child(self, args, executable, preexec_fn, close_fds,\n                       pass_fds, cwd, env,\n                       startupinfo, creationflags, shell,\n                       p2cread, p2cwrite,\n                       c2pread, c2pwrite,\n                       errread, errwrite,\n                       unused_restore_signals,\n                       unused_gid, unused_gids, unused_uid,\n                       unused_umask,\n                       unused_start_new_session):\n        &amp;quot;&amp;quot;&amp;quot;Execute program (MS Windows version)&amp;quot;&amp;quot;&amp;quot;\n    \n        assert not pass_fds, &amp;quot;pass_fds not supported on Windows.&amp;quot;\n    \n        if isinstance(args, str):\n            pass\n        elif isinstance(args, bytes):\n            if shell:\n                raise TypeError(&amp;#x27;bytes args is not allowed on Windows&amp;#x27;)\n            args = list2cmdline([args])\n        elif isinstance(args, os.PathLike):\n            if shell:\n                raise TypeError(&amp;#x27;path-like args is not allowed when &amp;#x27;\n                                &amp;#x27;shell is true&amp;#x27;)\n            args = list2cmdline([args])\n        else:\n            args = list2cmdline(args)\n    \n        if executable is not None:\n            executable = os.fsdecode(executable)\n    \n        # Process startup details\n        if startupinfo is None:\n            startupinfo = STARTUPINFO()\n        else:\n            # bpo-34044: Copy STARTUPINFO since it is modified above,\n            # so the caller can reuse it multiple times.\n            startupinfo = startupinfo.copy()\n    \n        use_std_handles = -1 not in (p2cread, c2pwrite, errwrite)\n        if use_std_handles:\n            startupinfo.dwFlags |= _winapi.STARTF_USESTDHANDLES\n            startupinfo.hStdInput = p2cread\n            startupinfo.hStdOutput = c2pwrite\n            startupinfo.hStdError = errwrite\n    \n        attribute_list = startupinfo.lpAttributeList\n        have_handle_list = bool(attribute_list and\n                                &amp;quot;handle_list&amp;quot; in attribute_list and\n                                attribute_list[&amp;quot;handle_list&amp;quot;])\n    \n        # If we were given an handle_list or need to create one\n        if have_handle_list or (use_std_handles and close_fds):\n            if attribute_list is None:\n                attribute_list = startupinfo.lpAttributeList = {}\n            handle_list = attribute_list[&amp;quot;handle_list&amp;quot;] = \\\n                list(attribute_list.get(&amp;quot;handle_list&amp;quot;, []))\n    \n            if use_std_handles:\n                handle_list += [int(p2cread), int(c2pwrite), int(errwrite)]\n    \n            handle_list[:] = self._filter_handle_list(handle_list)\n    \n            if handle_list:\n                if not close_fds:\n                    warnings.warn(&amp;quot;startupinfo.lpAttributeList[&amp;#x27;handle_list&amp;#x27;] &amp;quot;\n                                  &amp;quot;overriding close_fds&amp;quot;, RuntimeWarning)\n    \n                # When using the handle_list we always request to inherit\n                # handles but the only handles that will be inherited are\n                # the ones in the handle_list\n                close_fds = False\n    \n        if shell:\n            startupinfo.dwFlags |= _winapi.STARTF_USESHOWWINDOW\n            startupinfo.wShowWindow = _winapi.SW_HIDE\n            comspec = os.environ.get(&amp;quot;COMSPEC&amp;quot;, &amp;quot;cmd.exe&amp;quot;)\n            args = &amp;#x27;{} /c &amp;quot;{}&amp;quot;&amp;#x27;.format (comspec, args)\n    \n        if cwd is not None:\n            cwd = os.fsdecode(cwd)\n    \n        sys.audit(&amp;quot;subprocess.Popen&amp;quot;, executable, args, cwd, env)\n    \n        # Start the process\n        try:\n&amp;gt;           hp, ht, pid, tid = _winapi.CreateProcess(executable, args,\n                                     # no special security\n                                     None, None,\n                                     int(not close_fds),\n                                     creationflags,\n                                     env,\n                                     cwd,\n                                     startupinfo)\nE                                    OSError: [WinError 193] %1 is not a valid Win32 application\n\n..\\..\\..\\..\\AppData\\Local\\Programs\\Python\\Python39\\lib\\subprocess.py:1420: OSError\n&#34;}], &#34;test_gallery.py::test_lightbox_functionality&#34;: [{&#34;extras&#34;: [], &#34;result&#34;: &#34;Error&#34;, &#34;testId&#34;: &#34;test_gallery.py::test_lightbox_functionality::setup&#34;, &#34;duration&#34;: &#34;00:00:02&#34;, &#34;resultsTableRow&#34;: [&#34;&lt;td class=\&#34;col-result\&#34;&gt;Error&lt;/td&gt;&#34;, &#34;&lt;td class=\&#34;col-testId\&#34;&gt;test_gallery.py::test_lightbox_functionality::setup&lt;/td&gt;&#34;, &#34;&lt;td class=\&#34;col-duration\&#34;&gt;00:00:02&lt;/td&gt;&#34;, &#34;&lt;td class=\&#34;col-links\&#34;&gt;&lt;/td&gt;&#34;], &#34;log&#34;: &#34;@pytest.fixture(scope=&amp;quot;function&amp;quot;)\n    def driver():\n        &amp;quot;&amp;quot;&amp;quot;\n        Fixture to create and return a Chrome WebDriver instance.\n        The driver is automatically closed after the test.\n        &amp;quot;&amp;quot;&amp;quot;\n        chrome_options = Options()\n        # Uncomment the line below to run tests in headless mode\n        # chrome_options.add_argument(&amp;quot;--headless&amp;quot;)\n        chrome_options.add_argument(&amp;quot;--window-size=1920,1080&amp;quot;)\n    \n        # Create the WebDriver instance\n&amp;gt;       driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=chrome_options)\n\nconftest.py:31: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n..\\..\\..\\..\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\selenium\\webdriver\\chrome\\webdriver.py:45: in __init__\n    super().__init__(\n..\\..\\..\\..\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\selenium\\webdriver\\chromium\\webdriver.py:53: in __init__\n    self.service.start()\n..\\..\\..\\..\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\selenium\\webdriver\\common\\service.py:98: in start\n    self._start_process(self._path)\n..\\..\\..\\..\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\selenium\\webdriver\\common\\service.py:204: in _start_process\n    self.process = subprocess.Popen(\n..\\..\\..\\..\\AppData\\Local\\Programs\\Python\\Python39\\lib\\subprocess.py:951: in __init__\n    self._execute_child(args, executable, preexec_fn, close_fds,\n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n\nself = &amp;lt;Popen: returncode: None args: [&amp;#x27;C:\\\\Users\\\\<USER>\\\\.wdm\\\\drivers\\\\chromedriv...&amp;gt;\nargs = &amp;#x27;C:\\\\Users\\\\<USER>\\\\.wdm\\\\drivers\\\\chromedriver\\\\win64\\\\136.0.7103.94\\\\chromedriver-win32/THIRD_PARTY_NOTICES.chromedriver --port=62372&amp;#x27;\nexecutable = None, preexec_fn = None, close_fds = False, pass_fds = (), cwd = None\nenv = environ({&amp;#x27;ALLUSERSPROFILE&amp;#x27;: &amp;#x27;C:\\\\ProgramData&amp;#x27;, &amp;#x27;APPDATA&amp;#x27;: &amp;#x27;C:\\\\Users\\\\<USER>\\\\AppData\\\\Roaming&amp;#x27;, &amp;#x27;CHROME_CRASHPAD_PIPE_...ibs\\\\debugpy&amp;#x27;, &amp;#x27;VSCODE_INJECTION&amp;#x27;: &amp;#x27;1&amp;#x27;, &amp;#x27;PYTEST_CURRENT_TEST&amp;#x27;: &amp;#x27;test_gallery.py::test_lightbox_functionality (setup)&amp;#x27;})\nstartupinfo = &amp;lt;subprocess.STARTUPINFO object at 0x000001EF34372D90&amp;gt;, creationflags = 0, shell = False\np2cread = Handle(400), p2cwrite = 17, c2pread = -1, c2pwrite = Handle(944), errread = -1, errwrite = Handle(440)\nunused_restore_signals = True, unused_gid = None, unused_gids = None, unused_uid = None, unused_umask = -1\nunused_start_new_session = False\n\n    def _execute_child(self, args, executable, preexec_fn, close_fds,\n                       pass_fds, cwd, env,\n                       startupinfo, creationflags, shell,\n                       p2cread, p2cwrite,\n                       c2pread, c2pwrite,\n                       errread, errwrite,\n                       unused_restore_signals,\n                       unused_gid, unused_gids, unused_uid,\n                       unused_umask,\n                       unused_start_new_session):\n        &amp;quot;&amp;quot;&amp;quot;Execute program (MS Windows version)&amp;quot;&amp;quot;&amp;quot;\n    \n        assert not pass_fds, &amp;quot;pass_fds not supported on Windows.&amp;quot;\n    \n        if isinstance(args, str):\n            pass\n        elif isinstance(args, bytes):\n            if shell:\n                raise TypeError(&amp;#x27;bytes args is not allowed on Windows&amp;#x27;)\n            args = list2cmdline([args])\n        elif isinstance(args, os.PathLike):\n            if shell:\n                raise TypeError(&amp;#x27;path-like args is not allowed when &amp;#x27;\n                                &amp;#x27;shell is true&amp;#x27;)\n            args = list2cmdline([args])\n        else:\n            args = list2cmdline(args)\n    \n        if executable is not None:\n            executable = os.fsdecode(executable)\n    \n        # Process startup details\n        if startupinfo is None:\n            startupinfo = STARTUPINFO()\n        else:\n            # bpo-34044: Copy STARTUPINFO since it is modified above,\n            # so the caller can reuse it multiple times.\n            startupinfo = startupinfo.copy()\n    \n        use_std_handles = -1 not in (p2cread, c2pwrite, errwrite)\n        if use_std_handles:\n            startupinfo.dwFlags |= _winapi.STARTF_USESTDHANDLES\n            startupinfo.hStdInput = p2cread\n            startupinfo.hStdOutput = c2pwrite\n            startupinfo.hStdError = errwrite\n    \n        attribute_list = startupinfo.lpAttributeList\n        have_handle_list = bool(attribute_list and\n                                &amp;quot;handle_list&amp;quot; in attribute_list and\n                                attribute_list[&amp;quot;handle_list&amp;quot;])\n    \n        # If we were given an handle_list or need to create one\n        if have_handle_list or (use_std_handles and close_fds):\n            if attribute_list is None:\n                attribute_list = startupinfo.lpAttributeList = {}\n            handle_list = attribute_list[&amp;quot;handle_list&amp;quot;] = \\\n                list(attribute_list.get(&amp;quot;handle_list&amp;quot;, []))\n    \n            if use_std_handles:\n                handle_list += [int(p2cread), int(c2pwrite), int(errwrite)]\n    \n            handle_list[:] = self._filter_handle_list(handle_list)\n    \n            if handle_list:\n                if not close_fds:\n                    warnings.warn(&amp;quot;startupinfo.lpAttributeList[&amp;#x27;handle_list&amp;#x27;] &amp;quot;\n                                  &amp;quot;overriding close_fds&amp;quot;, RuntimeWarning)\n    \n                # When using the handle_list we always request to inherit\n                # handles but the only handles that will be inherited are\n                # the ones in the handle_list\n                close_fds = False\n    \n        if shell:\n            startupinfo.dwFlags |= _winapi.STARTF_USESHOWWINDOW\n            startupinfo.wShowWindow = _winapi.SW_HIDE\n            comspec = os.environ.get(&amp;quot;COMSPEC&amp;quot;, &amp;quot;cmd.exe&amp;quot;)\n            args = &amp;#x27;{} /c &amp;quot;{}&amp;quot;&amp;#x27;.format (comspec, args)\n    \n        if cwd is not None:\n            cwd = os.fsdecode(cwd)\n    \n        sys.audit(&amp;quot;subprocess.Popen&amp;quot;, executable, args, cwd, env)\n    \n        # Start the process\n        try:\n&amp;gt;           hp, ht, pid, tid = _winapi.CreateProcess(executable, args,\n                                     # no special security\n                                     None, None,\n                                     int(not close_fds),\n                                     creationflags,\n                                     env,\n                                     cwd,\n                                     startupinfo)\nE                                    OSError: [WinError 193] %1 is not a valid Win32 application\n\n..\\..\\..\\..\\AppData\\Local\\Programs\\Python\\Python39\\lib\\subprocess.py:1420: OSError\n&#34;}], &#34;test_gallery.py::test_combined_filters&#34;: [{&#34;extras&#34;: [], &#34;result&#34;: &#34;Error&#34;, &#34;testId&#34;: &#34;test_gallery.py::test_combined_filters::setup&#34;, &#34;duration&#34;: &#34;00:00:02&#34;, &#34;resultsTableRow&#34;: [&#34;&lt;td class=\&#34;col-result\&#34;&gt;Error&lt;/td&gt;&#34;, &#34;&lt;td class=\&#34;col-testId\&#34;&gt;test_gallery.py::test_combined_filters::setup&lt;/td&gt;&#34;, &#34;&lt;td class=\&#34;col-duration\&#34;&gt;00:00:02&lt;/td&gt;&#34;, &#34;&lt;td class=\&#34;col-links\&#34;&gt;&lt;/td&gt;&#34;], &#34;log&#34;: &#34;@pytest.fixture(scope=&amp;quot;function&amp;quot;)\n    def driver():\n        &amp;quot;&amp;quot;&amp;quot;\n        Fixture to create and return a Chrome WebDriver instance.\n        The driver is automatically closed after the test.\n        &amp;quot;&amp;quot;&amp;quot;\n        chrome_options = Options()\n        # Uncomment the line below to run tests in headless mode\n        # chrome_options.add_argument(&amp;quot;--headless&amp;quot;)\n        chrome_options.add_argument(&amp;quot;--window-size=1920,1080&amp;quot;)\n    \n        # Create the WebDriver instance\n&amp;gt;       driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=chrome_options)\n\nconftest.py:31: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n..\\..\\..\\..\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\selenium\\webdriver\\chrome\\webdriver.py:45: in __init__\n    super().__init__(\n..\\..\\..\\..\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\selenium\\webdriver\\chromium\\webdriver.py:53: in __init__\n    self.service.start()\n..\\..\\..\\..\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\selenium\\webdriver\\common\\service.py:98: in start\n    self._start_process(self._path)\n..\\..\\..\\..\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\selenium\\webdriver\\common\\service.py:204: in _start_process\n    self.process = subprocess.Popen(\n..\\..\\..\\..\\AppData\\Local\\Programs\\Python\\Python39\\lib\\subprocess.py:951: in __init__\n    self._execute_child(args, executable, preexec_fn, close_fds,\n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n\nself = &amp;lt;Popen: returncode: None args: [&amp;#x27;C:\\\\Users\\\\<USER>\\\\.wdm\\\\drivers\\\\chromedriv...&amp;gt;\nargs = &amp;#x27;C:\\\\Users\\\\<USER>\\\\.wdm\\\\drivers\\\\chromedriver\\\\win64\\\\136.0.7103.94\\\\chromedriver-win32/THIRD_PARTY_NOTICES.chromedriver --port=62376&amp;#x27;\nexecutable = None, preexec_fn = None, close_fds = False, pass_fds = (), cwd = None\nenv = environ({&amp;#x27;ALLUSERSPROFILE&amp;#x27;: &amp;#x27;C:\\\\ProgramData&amp;#x27;, &amp;#x27;APPDATA&amp;#x27;: &amp;#x27;C:\\\\Users\\\\<USER>\\\\AppData\\\\Roaming&amp;#x27;, &amp;#x27;CHROME_CRASHPAD_PIPE_...led\\\\libs\\\\debugpy&amp;#x27;, &amp;#x27;VSCODE_INJECTION&amp;#x27;: &amp;#x27;1&amp;#x27;, &amp;#x27;PYTEST_CURRENT_TEST&amp;#x27;: &amp;#x27;test_gallery.py::test_combined_filters (setup)&amp;#x27;})\nstartupinfo = &amp;lt;subprocess.STARTUPINFO object at 0x000001EF342ADC40&amp;gt;, creationflags = 0, shell = False\np2cread = Handle(528), p2cwrite = 18, c2pread = -1, c2pwrite = Handle(456), errread = -1, errwrite = Handle(412)\nunused_restore_signals = True, unused_gid = None, unused_gids = None, unused_uid = None, unused_umask = -1\nunused_start_new_session = False\n\n    def _execute_child(self, args, executable, preexec_fn, close_fds,\n                       pass_fds, cwd, env,\n                       startupinfo, creationflags, shell,\n                       p2cread, p2cwrite,\n                       c2pread, c2pwrite,\n                       errread, errwrite,\n                       unused_restore_signals,\n                       unused_gid, unused_gids, unused_uid,\n                       unused_umask,\n                       unused_start_new_session):\n        &amp;quot;&amp;quot;&amp;quot;Execute program (MS Windows version)&amp;quot;&amp;quot;&amp;quot;\n    \n        assert not pass_fds, &amp;quot;pass_fds not supported on Windows.&amp;quot;\n    \n        if isinstance(args, str):\n            pass\n        elif isinstance(args, bytes):\n            if shell:\n                raise TypeError(&amp;#x27;bytes args is not allowed on Windows&amp;#x27;)\n            args = list2cmdline([args])\n        elif isinstance(args, os.PathLike):\n            if shell:\n                raise TypeError(&amp;#x27;path-like args is not allowed when &amp;#x27;\n                                &amp;#x27;shell is true&amp;#x27;)\n            args = list2cmdline([args])\n        else:\n            args = list2cmdline(args)\n    \n        if executable is not None:\n            executable = os.fsdecode(executable)\n    \n        # Process startup details\n        if startupinfo is None:\n            startupinfo = STARTUPINFO()\n        else:\n            # bpo-34044: Copy STARTUPINFO since it is modified above,\n            # so the caller can reuse it multiple times.\n            startupinfo = startupinfo.copy()\n    \n        use_std_handles = -1 not in (p2cread, c2pwrite, errwrite)\n        if use_std_handles:\n            startupinfo.dwFlags |= _winapi.STARTF_USESTDHANDLES\n            startupinfo.hStdInput = p2cread\n            startupinfo.hStdOutput = c2pwrite\n            startupinfo.hStdError = errwrite\n    \n        attribute_list = startupinfo.lpAttributeList\n        have_handle_list = bool(attribute_list and\n                                &amp;quot;handle_list&amp;quot; in attribute_list and\n                                attribute_list[&amp;quot;handle_list&amp;quot;])\n    \n        # If we were given an handle_list or need to create one\n        if have_handle_list or (use_std_handles and close_fds):\n            if attribute_list is None:\n                attribute_list = startupinfo.lpAttributeList = {}\n            handle_list = attribute_list[&amp;quot;handle_list&amp;quot;] = \\\n                list(attribute_list.get(&amp;quot;handle_list&amp;quot;, []))\n    \n            if use_std_handles:\n                handle_list += [int(p2cread), int(c2pwrite), int(errwrite)]\n    \n            handle_list[:] = self._filter_handle_list(handle_list)\n    \n            if handle_list:\n                if not close_fds:\n                    warnings.warn(&amp;quot;startupinfo.lpAttributeList[&amp;#x27;handle_list&amp;#x27;] &amp;quot;\n                                  &amp;quot;overriding close_fds&amp;quot;, RuntimeWarning)\n    \n                # When using the handle_list we always request to inherit\n                # handles but the only handles that will be inherited are\n                # the ones in the handle_list\n                close_fds = False\n    \n        if shell:\n            startupinfo.dwFlags |= _winapi.STARTF_USESHOWWINDOW\n            startupinfo.wShowWindow = _winapi.SW_HIDE\n            comspec = os.environ.get(&amp;quot;COMSPEC&amp;quot;, &amp;quot;cmd.exe&amp;quot;)\n            args = &amp;#x27;{} /c &amp;quot;{}&amp;quot;&amp;#x27;.format (comspec, args)\n    \n        if cwd is not None:\n            cwd = os.fsdecode(cwd)\n    \n        sys.audit(&amp;quot;subprocess.Popen&amp;quot;, executable, args, cwd, env)\n    \n        # Start the process\n        try:\n&amp;gt;           hp, ht, pid, tid = _winapi.CreateProcess(executable, args,\n                                     # no special security\n                                     None, None,\n                                     int(not close_fds),\n                                     creationflags,\n                                     env,\n                                     cwd,\n                                     startupinfo)\nE                                    OSError: [WinError 193] %1 is not a valid Win32 application\n\n..\\..\\..\\..\\AppData\\Local\\Programs\\Python\\Python39\\lib\\subprocess.py:1420: OSError\n&#34;}]}, &#34;renderCollapsed&#34;: [&#34;passed&#34;], &#34;initialSort&#34;: &#34;result&#34;, &#34;title&#34;: &#34;report_20250517_000256.html&#34;}"></div>
    <script>
      (function(){function r(e,n,t){function o(i,f){if(!n[i]){if(!e[i]){var c="function"==typeof require&&require;if(!f&&c)return c(i,!0);if(u)return u(i,!0);var a=new Error("Cannot find module '"+i+"'");throw a.code="MODULE_NOT_FOUND",a}var p=n[i]={exports:{}};e[i][0].call(p.exports,function(r){var n=e[i][1][r];return o(n||r)},p,p.exports,r,e,n,t)}return n[i].exports}for(var u="function"==typeof require&&require,i=0;i<t.length;i++)o(t[i]);return o}return r})()({1:[function(require,module,exports){
const { getCollapsedCategory, setCollapsedIds } = require('./storage.js')

class DataManager {
    setManager(data) {
        const collapsedCategories = [...getCollapsedCategory(data.renderCollapsed)]
        const collapsedIds = []
        const tests = Object.values(data.tests).flat().map((test, index) => {
            const collapsed = collapsedCategories.includes(test.result.toLowerCase())
            const id = `test_${index}`
            if (collapsed) {
                collapsedIds.push(id)
            }
            return {
                ...test,
                id,
                collapsed,
            }
        })
        const dataBlob = { ...data, tests }
        this.data = { ...dataBlob }
        this.renderData = { ...dataBlob }
        setCollapsedIds(collapsedIds)
    }

    get allData() {
        return { ...this.data }
    }

    resetRender() {
        this.renderData = { ...this.data }
    }

    setRender(data) {
        this.renderData.tests = [...data]
    }

    toggleCollapsedItem(id) {
        this.renderData.tests = this.renderData.tests.map((test) =>
            test.id === id ? { ...test, collapsed: !test.collapsed } : test,
        )
    }

    set allCollapsed(collapsed) {
        this.renderData = { ...this.renderData, tests: [...this.renderData.tests.map((test) => (
            { ...test, collapsed }
        ))] }
    }

    get testSubset() {
        return [...this.renderData.tests]
    }

    get environment() {
        return this.renderData.environment
    }

    get initialSort() {
        return this.data.initialSort
    }
}

module.exports = {
    manager: new DataManager(),
}

},{"./storage.js":8}],2:[function(require,module,exports){
const mediaViewer = require('./mediaviewer.js')
const templateEnvRow = document.getElementById('template_environment_row')
const templateResult = document.getElementById('template_results-table__tbody')

function htmlToElements(html) {
    const temp = document.createElement('template')
    temp.innerHTML = html
    return temp.content.childNodes
}

const find = (selector, elem) => {
    if (!elem) {
        elem = document
    }
    return elem.querySelector(selector)
}

const findAll = (selector, elem) => {
    if (!elem) {
        elem = document
    }
    return [...elem.querySelectorAll(selector)]
}

const dom = {
    getStaticRow: (key, value) => {
        const envRow = templateEnvRow.content.cloneNode(true)
        const isObj = typeof value === 'object' && value !== null
        const values = isObj ? Object.keys(value).map((k) => `${k}: ${value[k]}`) : null

        const valuesElement = htmlToElements(
            values ? `<ul>${values.map((val) => `<li>${val}</li>`).join('')}<ul>` : `<div>${value}</div>`)[0]
        const td = findAll('td', envRow)
        td[0].textContent = key
        td[1].appendChild(valuesElement)

        return envRow
    },
    getResultTBody: ({ testId, id, log, extras, resultsTableRow, tableHtml, result, collapsed }) => {
        const resultBody = templateResult.content.cloneNode(true)
        resultBody.querySelector('tbody').classList.add(result.toLowerCase())
        resultBody.querySelector('tbody').id = testId
        resultBody.querySelector('.collapsible').dataset.id = id

        resultsTableRow.forEach((html) => {
            const t = document.createElement('template')
            t.innerHTML = html
            resultBody.querySelector('.collapsible').appendChild(t.content)
        })

        if (log) {
            // Wrap lines starting with "E" with span.error to color those lines red
            const wrappedLog = log.replace(/^E.*$/gm, (match) => `<span class="error">${match}</span>`)
            resultBody.querySelector('.log').innerHTML = wrappedLog
        } else {
            resultBody.querySelector('.log').remove()
        }

        if (collapsed) {
            resultBody.querySelector('.collapsible > td')?.classList.add('collapsed')
            resultBody.querySelector('.extras-row').classList.add('hidden')
        } else {
            resultBody.querySelector('.collapsible > td')?.classList.remove('collapsed')
        }

        const media = []
        extras?.forEach(({ name, format_type, content }) => {
            if (['image', 'video'].includes(format_type)) {
                media.push({ path: content, name, format_type })
            }

            if (format_type === 'html') {
                resultBody.querySelector('.extraHTML').insertAdjacentHTML('beforeend', `<div>${content}</div>`)
            }
        })
        mediaViewer.setup(resultBody, media)

        // Add custom html from the pytest_html_results_table_html hook
        tableHtml?.forEach((item) => {
            resultBody.querySelector('td[class="extra"]').insertAdjacentHTML('beforeend', item)
        })

        return resultBody
    },
}

module.exports = {
    dom,
    htmlToElements,
    find,
    findAll,
}

},{"./mediaviewer.js":6}],3:[function(require,module,exports){
const { manager } = require('./datamanager.js')
const { doSort } = require('./sort.js')
const storageModule = require('./storage.js')

const getFilteredSubSet = (filter) =>
    manager.allData.tests.filter(({ result }) => filter.includes(result.toLowerCase()))

const doInitFilter = () => {
    const currentFilter = storageModule.getVisible()
    const filteredSubset = getFilteredSubSet(currentFilter)
    manager.setRender(filteredSubset)
}

const doFilter = (type, show) => {
    if (show) {
        storageModule.showCategory(type)
    } else {
        storageModule.hideCategory(type)
    }

    const currentFilter = storageModule.getVisible()
    const filteredSubset = getFilteredSubSet(currentFilter)
    manager.setRender(filteredSubset)

    const sortColumn = storageModule.getSort()
    doSort(sortColumn, true)
}

module.exports = {
    doFilter,
    doInitFilter,
}

},{"./datamanager.js":1,"./sort.js":7,"./storage.js":8}],4:[function(require,module,exports){
const { redraw, bindEvents, renderStatic } = require('./main.js')
const { doInitFilter } = require('./filter.js')
const { doInitSort } = require('./sort.js')
const { manager } = require('./datamanager.js')
const data = JSON.parse(document.getElementById('data-container').dataset.jsonblob)

function init() {
    manager.setManager(data)
    doInitFilter()
    doInitSort()
    renderStatic()
    redraw()
    bindEvents()
}

init()

},{"./datamanager.js":1,"./filter.js":3,"./main.js":5,"./sort.js":7}],5:[function(require,module,exports){
const { dom, find, findAll } = require('./dom.js')
const { manager } = require('./datamanager.js')
const { doSort } = require('./sort.js')
const { doFilter } = require('./filter.js')
const {
    getVisible,
    getCollapsedIds,
    setCollapsedIds,
    getSort,
    getSortDirection,
    possibleFilters,
} = require('./storage.js')

const removeChildren = (node) => {
    while (node.firstChild) {
        node.removeChild(node.firstChild)
    }
}

const renderStatic = () => {
    const renderEnvironmentTable = () => {
        const environment = manager.environment
        const rows = Object.keys(environment).map((key) => dom.getStaticRow(key, environment[key]))
        const table = document.getElementById('environment')
        removeChildren(table)
        rows.forEach((row) => table.appendChild(row))
    }
    renderEnvironmentTable()
}

const addItemToggleListener = (elem) => {
    elem.addEventListener('click', ({ target }) => {
        const id = target.parentElement.dataset.id
        manager.toggleCollapsedItem(id)

        const collapsedIds = getCollapsedIds()
        if (collapsedIds.includes(id)) {
            const updated = collapsedIds.filter((item) => item !== id)
            setCollapsedIds(updated)
        } else {
            collapsedIds.push(id)
            setCollapsedIds(collapsedIds)
        }
        redraw()
    })
}

const renderContent = (tests) => {
    const sortAttr = getSort(manager.initialSort)
    const sortAsc = JSON.parse(getSortDirection())
    const rows = tests.map(dom.getResultTBody)
    const table = document.getElementById('results-table')
    const tableHeader = document.getElementById('results-table-head')

    const newTable = document.createElement('table')
    newTable.id = 'results-table'

    // remove all sorting classes and set the relevant
    findAll('.sortable', tableHeader).forEach((elem) => elem.classList.remove('asc', 'desc'))
    tableHeader.querySelector(`.sortable[data-column-type="${sortAttr}"]`)?.classList.add(sortAsc ? 'desc' : 'asc')
    newTable.appendChild(tableHeader)

    if (!rows.length) {
        const emptyTable = document.getElementById('template_results-table__body--empty').content.cloneNode(true)
        newTable.appendChild(emptyTable)
    } else {
        rows.forEach((row) => {
            if (!!row) {
                findAll('.collapsible td:not(.col-links', row).forEach(addItemToggleListener)
                find('.logexpander', row).addEventListener('click',
                    (evt) => evt.target.parentNode.classList.toggle('expanded'),
                )
                newTable.appendChild(row)
            }
        })
    }

    table.replaceWith(newTable)
}

const renderDerived = () => {
    const currentFilter = getVisible()
    possibleFilters.forEach((result) => {
        const input = document.querySelector(`input[data-test-result="${result}"]`)
        input.checked = currentFilter.includes(result)
    })
}

const bindEvents = () => {
    const filterColumn = (evt) => {
        const { target: element } = evt
        const { testResult } = element.dataset

        doFilter(testResult, element.checked)
        const collapsedIds = getCollapsedIds()
        const updated = manager.renderData.tests.map((test) => {
            return {
                ...test,
                collapsed: collapsedIds.includes(test.id),
            }
        })
        manager.setRender(updated)
        redraw()
    }

    const header = document.getElementById('environment-header')
    header.addEventListener('click', () => {
        const table = document.getElementById('environment')
        table.classList.toggle('hidden')
        header.classList.toggle('collapsed')
    })

    findAll('input[name="filter_checkbox"]').forEach((elem) => {
        elem.addEventListener('click', filterColumn)
    })

    findAll('.sortable').forEach((elem) => {
        elem.addEventListener('click', (evt) => {
            const { target: element } = evt
            const { columnType } = element.dataset
            doSort(columnType)
            redraw()
        })
    })

    document.getElementById('show_all_details').addEventListener('click', () => {
        manager.allCollapsed = false
        setCollapsedIds([])
        redraw()
    })
    document.getElementById('hide_all_details').addEventListener('click', () => {
        manager.allCollapsed = true
        const allIds = manager.renderData.tests.map((test) => test.id)
        setCollapsedIds(allIds)
        redraw()
    })
}

const redraw = () => {
    const { testSubset } = manager

    renderContent(testSubset)
    renderDerived()
}

module.exports = {
    redraw,
    bindEvents,
    renderStatic,
}

},{"./datamanager.js":1,"./dom.js":2,"./filter.js":3,"./sort.js":7,"./storage.js":8}],6:[function(require,module,exports){
class MediaViewer {
    constructor(assets) {
        this.assets = assets
        this.index = 0
    }

    nextActive() {
        this.index = this.index === this.assets.length - 1 ? 0 : this.index + 1
        return [this.activeFile, this.index]
    }

    prevActive() {
        this.index = this.index === 0 ? this.assets.length - 1 : this.index -1
        return [this.activeFile, this.index]
    }

    get currentIndex() {
        return this.index
    }

    get activeFile() {
        return this.assets[this.index]
    }
}


const setup = (resultBody, assets) => {
    if (!assets.length) {
        resultBody.querySelector('.media').classList.add('hidden')
        return
    }

    const mediaViewer = new MediaViewer(assets)
    const container = resultBody.querySelector('.media-container')
    const leftArrow = resultBody.querySelector('.media-container__nav--left')
    const rightArrow = resultBody.querySelector('.media-container__nav--right')
    const mediaName = resultBody.querySelector('.media__name')
    const counter = resultBody.querySelector('.media__counter')
    const imageEl = resultBody.querySelector('img')
    const sourceEl = resultBody.querySelector('source')
    const videoEl = resultBody.querySelector('video')

    const setImg = (media, index) => {
        if (media?.format_type === 'image') {
            imageEl.src = media.path

            imageEl.classList.remove('hidden')
            videoEl.classList.add('hidden')
        } else if (media?.format_type === 'video') {
            sourceEl.src = media.path

            videoEl.classList.remove('hidden')
            imageEl.classList.add('hidden')
        }

        mediaName.innerText = media?.name
        counter.innerText = `${index + 1} / ${assets.length}`
    }
    setImg(mediaViewer.activeFile, mediaViewer.currentIndex)

    const moveLeft = () => {
        const [media, index] = mediaViewer.prevActive()
        setImg(media, index)
    }
    const doRight = () => {
        const [media, index] = mediaViewer.nextActive()
        setImg(media, index)
    }
    const openImg = () => {
        window.open(mediaViewer.activeFile.path, '_blank')
    }
    if (assets.length === 1) {
        container.classList.add('media-container--fullscreen')
    } else {
        leftArrow.addEventListener('click', moveLeft)
        rightArrow.addEventListener('click', doRight)
    }
    imageEl.addEventListener('click', openImg)
}

module.exports = {
    setup,
}

},{}],7:[function(require,module,exports){
const { manager } = require('./datamanager.js')
const storageModule = require('./storage.js')

const genericSort = (list, key, ascending, customOrder) => {
    let sorted
    if (customOrder) {
        sorted = list.sort((a, b) => {
            const aValue = a.result.toLowerCase()
            const bValue = b.result.toLowerCase()

            const aIndex = customOrder.findIndex((item) => item.toLowerCase() === aValue)
            const bIndex = customOrder.findIndex((item) => item.toLowerCase() === bValue)

            // Compare the indices to determine the sort order
            return aIndex - bIndex
        })
    } else {
        sorted = list.sort((a, b) => a[key] === b[key] ? 0 : a[key] > b[key] ? 1 : -1)
    }

    if (ascending) {
        sorted.reverse()
    }
    return sorted
}

const durationSort = (list, ascending) => {
    const parseDuration = (duration) => {
        if (duration.includes(':')) {
            // If it's in the format "HH:mm:ss"
            const [hours, minutes, seconds] = duration.split(':').map(Number)
            return (hours * 3600 + minutes * 60 + seconds) * 1000
        } else {
            // If it's in the format "nnn ms"
            return parseInt(duration)
        }
    }
    const sorted = list.sort((a, b) => parseDuration(a['duration']) - parseDuration(b['duration']))
    if (ascending) {
        sorted.reverse()
    }
    return sorted
}

const doInitSort = () => {
    const type = storageModule.getSort(manager.initialSort)
    const ascending = storageModule.getSortDirection()
    const list = manager.testSubset
    const initialOrder = ['Error', 'Failed', 'Rerun', 'XFailed', 'XPassed', 'Skipped', 'Passed']

    storageModule.setSort(type)
    storageModule.setSortDirection(ascending)

    if (type?.toLowerCase() === 'original') {
        manager.setRender(list)
    } else {
        let sortedList
        switch (type) {
        case 'duration':
            sortedList = durationSort(list, ascending)
            break
        case 'result':
            sortedList = genericSort(list, type, ascending, initialOrder)
            break
        default:
            sortedList = genericSort(list, type, ascending)
            break
        }
        manager.setRender(sortedList)
    }
}

const doSort = (type, skipDirection) => {
    const newSortType = storageModule.getSort(manager.initialSort) !== type
    const currentAsc = storageModule.getSortDirection()
    let ascending
    if (skipDirection) {
        ascending = currentAsc
    } else {
        ascending = newSortType ? false : !currentAsc
    }
    storageModule.setSort(type)
    storageModule.setSortDirection(ascending)

    const list = manager.testSubset
    const sortedList = type === 'duration' ? durationSort(list, ascending) : genericSort(list, type, ascending)
    manager.setRender(sortedList)
}

module.exports = {
    doInitSort,
    doSort,
}

},{"./datamanager.js":1,"./storage.js":8}],8:[function(require,module,exports){
const possibleFilters = [
    'passed',
    'skipped',
    'failed',
    'error',
    'xfailed',
    'xpassed',
    'rerun',
]

const getVisible = () => {
    const url = new URL(window.location.href)
    const settings = new URLSearchParams(url.search).get('visible')
    const lower = (item) => {
        const lowerItem = item.toLowerCase()
        if (possibleFilters.includes(lowerItem)) {
            return lowerItem
        }
        return null
    }
    return settings === null ?
        possibleFilters :
        [...new Set(settings?.split(',').map(lower).filter((item) => item))]
}

const hideCategory = (categoryToHide) => {
    const url = new URL(window.location.href)
    const visibleParams = new URLSearchParams(url.search).get('visible')
    const currentVisible = visibleParams ? visibleParams.split(',') : [...possibleFilters]
    const settings = [...new Set(currentVisible)].filter((f) => f !== categoryToHide).join(',')

    url.searchParams.set('visible', settings)
    window.history.pushState({}, null, unescape(url.href))
}

const showCategory = (categoryToShow) => {
    if (typeof window === 'undefined') {
        return
    }
    const url = new URL(window.location.href)
    const currentVisible = new URLSearchParams(url.search).get('visible')?.split(',').filter(Boolean) ||
        [...possibleFilters]
    const settings = [...new Set([categoryToShow, ...currentVisible])]
    const noFilter = possibleFilters.length === settings.length || !settings.length

    noFilter ? url.searchParams.delete('visible') : url.searchParams.set('visible', settings.join(','))
    window.history.pushState({}, null, unescape(url.href))
}

const getSort = (initialSort) => {
    const url = new URL(window.location.href)
    let sort = new URLSearchParams(url.search).get('sort')
    if (!sort) {
        sort = initialSort || 'result'
    }
    return sort
}

const setSort = (type) => {
    const url = new URL(window.location.href)
    url.searchParams.set('sort', type)
    window.history.pushState({}, null, unescape(url.href))
}

const getCollapsedCategory = (renderCollapsed) => {
    let categories
    if (typeof window !== 'undefined') {
        const url = new URL(window.location.href)
        const collapsedItems = new URLSearchParams(url.search).get('collapsed')
        switch (true) {
        case !renderCollapsed && collapsedItems === null:
            categories = ['passed']
            break
        case collapsedItems?.length === 0 || /^["']{2}$/.test(collapsedItems):
            categories = []
            break
        case /^all$/.test(collapsedItems) || collapsedItems === null && /^all$/.test(renderCollapsed):
            categories = [...possibleFilters]
            break
        default:
            categories = collapsedItems?.split(',').map((item) => item.toLowerCase()) || renderCollapsed
            break
        }
    } else {
        categories = []
    }
    return categories
}

const getSortDirection = () => JSON.parse(sessionStorage.getItem('sortAsc')) || false
const setSortDirection = (ascending) => sessionStorage.setItem('sortAsc', ascending)

const getCollapsedIds = () => JSON.parse(sessionStorage.getItem('collapsedIds')) || []
const setCollapsedIds = (list) => sessionStorage.setItem('collapsedIds', JSON.stringify(list))

module.exports = {
    getVisible,
    hideCategory,
    showCategory,
    getCollapsedIds,
    setCollapsedIds,
    getSort,
    setSort,
    getSortDirection,
    setSortDirection,
    getCollapsedCategory,
    possibleFilters,
}

},{}]},{},[4]);
    </script>
  </footer>
</html>