/* App layout */
.app {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

/* Main content area */
.main-content {
  flex: 1;
  padding: 1rem;
  transition: margin-left 0.3s ease;
}

.main-content.authenticated {
  margin-top: 60px; /* Height of navbar */
}

.main-content.sidebar-open {
  margin-left: 250px; /* Width of sidebar */
}

@media (max-width: 768px) {
  .main-content.sidebar-open {
    margin-left: 0;
  }
}

/* Navbar styles */
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 60px;
  background-color: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  padding: 0 1.5rem;
  z-index: 100;
}

.navbar-brand {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--primary);
  text-decoration: none;
}

.navbar-menu {
  margin-left: auto;
  display: flex;
  align-items: center;
}

.navbar-item {
  margin-left: 1rem;
}

.navbar-toggle {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 1.5rem;
  color: var(--dark);
  margin-right: 1rem;
}

/* Sidebar styles */
.sidebar {
  position: fixed;
  top: 60px; /* Below navbar */
  left: 0;
  bottom: 0;
  width: 250px;
  background-color: white;
  box-shadow: 1px 0 3px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
  z-index: 90;
  overflow-y: auto;
}

.sidebar.closed {
  transform: translateX(-100%);
}

.sidebar-menu {
  padding: 1rem 0;
}

.sidebar-item {
  display: block;
  padding: 0.75rem 1.5rem;
  color: var(--dark);
  text-decoration: none;
  transition: background-color 0.2s ease;
}

.sidebar-item:hover {
  background-color: #f3f4f6;
}

.sidebar-item.active {
  background-color: #f3f4f6;
  border-left: 3px solid var(--primary);
  font-weight: 500;
}

.sidebar-item-icon {
  margin-right: 0.75rem;
}

/* Dashboard cards */
.dashboard-stats {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
}

.stat-title {
  font-size: 0.875rem;
  color: #6b7280;
  margin-bottom: 0.5rem;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.stat-icon {
  margin-left: auto;
  font-size: 2rem;
  color: var(--primary-light);
}

/* Login page */
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.login-card {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  padding: 2rem;
  width: 100%;
  max-width: 400px;
}

.login-logo {
  text-align: center;
  margin-bottom: 2rem;
}

.login-title {
  font-size: 1.5rem;
  font-weight: 600;
  text-align: center;
  margin-bottom: 1.5rem;
}

.login-form {
  display: flex;
  flex-direction: column;
}

.login-button {
  margin-top: 1rem;
  padding: 0.75rem;
  background-color: var(--primary);
  color: white;
  border: none;
  border-radius: 0.375rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.login-button:hover {
  background-color: var(--primary-light);
}
