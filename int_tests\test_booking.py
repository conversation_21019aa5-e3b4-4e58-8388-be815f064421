"""
Integration tests for booking functionality.
"""
import pytest
import time
from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from conftest import navigate_to_page, wait_for_element, wait_for_element_clickable, check_element_exists

def test_booking_buttons_exist(driver, wait):
    """Test that booking buttons exist on all relevant pages."""
    pages = ["index.html", "services.html", "stylists.html"]
    
    for page in pages:
        navigate_to_page(driver, page)
        
        # Find all booking buttons
        booking_buttons = driver.find_elements(By.XPATH, "//a[contains(text(), 'Book') or contains(text(), 'Appointment')] | //button[contains(text(), 'Book') or contains(text(), 'Appointment')]")
        
        # Check that at least one booking button exists
        assert len(booking_buttons) > 0, f"No booking buttons found on {page}"
        
        # Check that the first booking button is displayed
        assert booking_buttons[0].is_displayed(), f"Booking button not displayed on {page}"

def test_booking_links_valid(driver, wait):
    """Test that booking links have valid URLs."""
    pages = ["index.html", "services.html", "stylists.html"]
    
    for page in pages:
        navigate_to_page(driver, page)
        
        # Find all booking links
        booking_links = driver.find_elements(By.XPATH, "//a[contains(text(), 'Book') or contains(text(), 'Appointment')]")
        
        for link in booking_links:
            # Check that the link has a valid href
            href = link.get_attribute("href")
            assert href and href != "#", f"Booking link on {page} does not have a valid href"
            
            # Check that the href is a Square URL or another valid booking URL
            assert "square.site" in href or "squareup.com" in href or "booking" in href, f"Booking link on {page} does not point to a valid booking URL: {href}"

def test_booking_button_styling(driver, wait):
    """Test that booking buttons have consistent styling."""
    navigate_to_page(driver, "index.html")
    
    # Find all booking buttons
    booking_buttons = driver.find_elements(By.XPATH, "//a[contains(text(), 'Book') or contains(text(), 'Appointment')] | //button[contains(text(), 'Book') or contains(text(), 'Appointment')]")
    
    # Check that at least one booking button exists
    assert len(booking_buttons) > 0, "No booking buttons found on the home page"
    
    # Get the first booking button
    first_button = booking_buttons[0]
    
    # Check that the button has the expected styling classes
    class_attr = first_button.get_attribute("class")
    assert "btn" in class_attr or "action-btn" in class_attr or "book-btn" in class_attr, "Booking button does not have expected styling classes"
    
    # Check that the button has a background color
    bg_color = first_button.value_of_css_property("background-color")
    assert bg_color and bg_color != "rgba(0, 0, 0, 0)", "Booking button does not have a background color"
    
    # Check that the button has a text color
    text_color = first_button.value_of_css_property("color")
    assert text_color and text_color != "rgba(0, 0, 0, 0)", "Booking button does not have a text color"
    
    # Check that the button has padding
    padding = first_button.value_of_css_property("padding")
    assert padding and padding != "0px", "Booking button does not have padding"

def test_booking_popup_modal(driver, wait):
    """Test that booking links open in a popup modal if implemented that way."""
    navigate_to_page(driver, "index.html")
    
    # Find all booking buttons that might open a modal
    booking_buttons = driver.find_elements(By.XPATH, "//button[contains(text(), 'Book') or contains(text(), 'Appointment')] | //a[contains(@onclick, 'openBookingModal') or contains(@data-toggle, 'modal')]")
    
    # If no buttons that open a modal are found, skip the test
    if len(booking_buttons) == 0:
        # Check if there are regular booking links
        booking_links = driver.find_elements(By.XPATH, "//a[contains(text(), 'Book') or contains(text(), 'Appointment')]")
        
        if len(booking_links) > 0:
            # The booking functionality might be implemented with direct links rather than modals
            pytest.skip("Booking functionality appears to use direct links rather than modals")
        else:
            pytest.fail("No booking buttons or links found on the home page")
    
    # Click the first button that might open a modal
    first_button = booking_buttons[0]
    driver.execute_script("arguments[0].scrollIntoView(true);", first_button)
    time.sleep(1)  # Allow scroll to complete
    first_button.click()
    time.sleep(1)  # Allow modal to open
    
    # Check if a modal appears
    modals = driver.find_elements(By.CSS_SELECTOR, ".modal, #bookingModal, [role='dialog']")
    
    # If no modal appears, the button might be a regular link
    if len(modals) == 0:
        # Check if the button is a link
        if first_button.tag_name.lower() == "a":
            # Check that the link has a valid href
            href = first_button.get_attribute("href")
            assert href and href != "#", "Booking button link does not have a valid href"
        else:
            # The booking functionality might be implemented differently
            pytest.skip("No booking modal found after clicking button")
    else:
        # Check that the modal is displayed
        modal = modals[0]
        assert modal.is_displayed(), "Booking modal is not displayed after clicking button"
        
        # Check that the modal contains an iframe or booking content
        iframe = modal.find_elements(By.TAG_NAME, "iframe")
        booking_content = modal.find_elements(By.CSS_SELECTOR, ".booking-content, .square-booking")
        
        assert len(iframe) > 0 or len(booking_content) > 0, "Booking modal does not contain iframe or booking content"

def test_booking_links_open_square(driver, wait):
    """Test that booking links open Square booking pages."""
    navigate_to_page(driver, "index.html")
    
    # Find all booking links
    booking_links = driver.find_elements(By.XPATH, "//a[contains(text(), 'Book') or contains(text(), 'Appointment')]")
    
    # If no booking links are found, skip the test
    if len(booking_links) == 0:
        pytest.skip("No booking links found on the home page")
    
    # Get the first booking link
    first_link = booking_links[0]
    
    # Check that the link has a valid href
    href = first_link.get_attribute("href")
    assert href and href != "#", "Booking link does not have a valid href"
    
    # Check that the href is a Square URL
    assert "square.site" in href or "squareup.com" in href, f"Booking link does not point to a Square URL: {href}"
    
    # We won't actually click the link and navigate away from the site
    # as that would disrupt the test flow
