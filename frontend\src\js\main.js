// Main JavaScript for GetTwisted Hair Studios

// Initialize AOS (Animate on Scroll)
document.addEventListener('DOMContentLoaded', () => {
  AOS.init();
  
  // Theme initialization
  const savedTheme = localStorage.getItem('selectedTheme') || 'vibrant';
  document.body.classList.add(`theme-${savedTheme}`);
  
  // Mobile menu functionality
  const mobileMenuBtn = document.getElementById('mobileMenuBtn');
  const mobileMenu = document.getElementById('mobileMenu');
  
  if (mobileMenuBtn && mobileMenu) {
    mobileMenuBtn.addEventListener('click', () => {
      mobileMenu.classList.toggle('hidden');
    });
  }
});

// Theme toggle functionality
const themes = ['vibrant', 'black-white', 'matrix'];

function toggleTheme() {
  let currentTheme = themes.findIndex(t => document.body.classList.contains(`theme-${t}`));
  document.body.classList.remove(`theme-${themes[currentTheme]}`);
  currentTheme = (currentTheme + 1) % themes.length;
  document.body.classList.add(`theme-${themes[currentTheme]}`);
  localStorage.setItem('selectedTheme', themes[currentTheme]);
}

// Chatbot functionality
function toggleChat() {
  const chat = document.getElementById('chatbot');
  if (chat) {
    chat.classList.toggle('hidden');
    chat.classList.toggle('animate-slide-in');
  }
}

// Booking modal functionality
function toggleBookingModal() {
  const modal = document.getElementById('bookingModal');
  if (modal) {
    modal.classList.toggle('hidden');
  }
}
