import React, { useState } from 'react';

const AIMarketAnalysis = ({ liveAnalysis, simAnalysis }) => {
  const [analysisType, setAnalysisType] = useState('live'); // 'live' or 'sim'
  
  // Get the current analysis based on selected type
  const currentAnalysis = analysisType === 'live' ? liveAnalysis : simAnalysis;
  
  return (
    <div className="ai-market-analysis">
      <div className="analysis-header">
        <h3 className="analysis-title">AI Market Analysis</h3>
        <div className="analysis-toggle">
          <button 
            className={`toggle-btn ${analysisType === 'live' ? 'active' : ''}`}
            onClick={() => setAnalysisType('live')}
          >
            Live Market
          </button>
          <button 
            className={`toggle-btn ${analysisType === 'sim' ? 'active' : ''}`}
            onClick={() => setAnalysisType('sim')}
          >
            Simulation
          </button>
        </div>
      </div>
      
      <div className="analysis-content">
        <div className="analysis-source">
          <span className={`source-badge ${analysisType}`}>
            {analysisType === 'live' ? 'LIVE DATA' : 'SIMULATION DATA'}
          </span>
          <span className="analysis-timestamp">
            Updated: {currentAnalysis.timestamp}
          </span>
        </div>
        
        <div className="market-sentiment">
          <div className="sentiment-label">Market Sentiment:</div>
          <div className={`sentiment-value ${currentAnalysis.sentiment.toLowerCase()}`}>
            {currentAnalysis.sentiment}
          </div>
        </div>
        
        <div className="market-trends">
          {currentAnalysis.trends.map((trend, index) => (
            <div key={index} className="trend-item">
              <div className="trend-symbol">{trend.symbol}</div>
              <div className={`trend-direction ${trend.direction.toLowerCase()}`}>
                {trend.direction === 'UP' ? '↗️' : '↘️'} {trend.direction}
              </div>
              <div className="trend-strength">
                Strength: {trend.strength}/10
              </div>
            </div>
          ))}
        </div>
        
        <div className="ai-recommendation">
          <div className="recommendation-label">AI Recommendation:</div>
          <div className="recommendation-value">
            {currentAnalysis.recommendation}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AIMarketAnalysis;
