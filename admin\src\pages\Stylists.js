import React, { useState, useEffect } from 'react';
import StylistService from '../services/stylist.service';

const Stylists = () => {
  const [stylists, setStylists] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showModal, setShowModal] = useState(false);
  const [currentStylist, setCurrentStylist] = useState(null);

  // Fetch stylists on component mount
  useEffect(() => {
    fetchStylists();
  }, []);

  // Fetch stylists from API
  const fetchStylists = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await StylistService.getStylists();
      setStylists(response.data || []);
    } catch (err) {
      setError('Failed to fetch stylists. Please try again.');
      console.error('Error fetching stylists:', err);
    } finally {
      setLoading(false);
    }
  };

  // Handle stylist deletion
  const handleDelete = async (id) => {
    if (window.confirm('Are you sure you want to delete this stylist?')) {
      try {
        await StylistService.deleteStylist(id);
        // Refresh the list after deletion
        fetchStylists();
      } catch (err) {
        setError('Failed to delete stylist. Please try again.');
        console.error('Error deleting stylist:', err);
      }
    }
  };

  // Open modal for adding/editing stylist
  const openModal = (stylist = null) => {
    setCurrentStylist(stylist);
    setShowModal(true);
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-4">
        <h1>Stylists</h1>
        <button className="btn btn-primary" onClick={() => openModal()}>Add New Stylist</button>
      </div>

      {error && (
        <div className="card bg-danger text-white p-4 mb-4">
          {error}
        </div>
      )}

      <div className="card">
        {loading ? (
          <div className="text-center p-4">
            <div className="spinner"></div>
            <p>Loading stylists...</p>
          </div>
        ) : stylists.length === 0 ? (
          <div className="text-center p-4">
            <p>No stylists found. Add your first stylist to get started.</p>
          </div>
        ) : (
          <table className="table">
            <thead>
              <tr>
                <th>Name</th>
                <th>Specialties</th>
                <th>Status</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {stylists.map(stylist => (
                <tr key={stylist._id}>
                  <td>
                    <div className="flex items-center gap-4">
                      <img
                        src={stylist.image ? `/assets/images/${stylist.image}` : '/assets/images/stylist-icon.jpg'}
                        alt={stylist.name}
                        className="rounded-full"
                        width="40"
                        height="40"
                      />
                      {stylist.name}
                    </div>
                  </td>
                  <td>{stylist.specialties ? stylist.specialties.join(', ') : 'N/A'}</td>
                  <td>
                    <span className={`p-4 rounded ${stylist.isActive ? 'bg-success' : 'bg-danger'}`}>
                      {stylist.isActive ? 'Active' : 'Inactive'}
                    </span>
                  </td>
                  <td>
                    <div className="flex gap-4">
                      <button
                        className="btn btn-secondary"
                        onClick={() => openModal(stylist)}
                      >
                        Edit
                      </button>
                      <button
                        className="btn btn-danger"
                        onClick={() => handleDelete(stylist._id)}
                      >
                        Delete
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        )}
      </div>

      {/* Stylist Modal - Would be implemented as a separate component */}
      {showModal && (
        <div className="modal-backdrop">
          <div className="modal-content">
            <h2>{currentStylist ? 'Edit Stylist' : 'Add New Stylist'}</h2>
            <p>Form would go here</p>
            <div className="modal-actions">
              <button className="btn btn-secondary" onClick={() => setShowModal(false)}>Cancel</button>
              <button className="btn btn-primary">Save</button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Stylists;
