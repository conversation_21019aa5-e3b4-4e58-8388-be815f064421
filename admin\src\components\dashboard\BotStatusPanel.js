import React from 'react';

const BotStatusPanel = ({ liveBots, simBots }) => {
  // Count bots by status
  const runningLiveBots = liveBots.filter(bot => bot.status === 'running').length;
  const runningSimbots = simBots.filter(bot => bot.status === 'running').length;
  const pausedBots = [...liveBots, ...simBots].filter(bot => bot.status === 'paused').length;
  const stoppedBots = [...liveBots, ...simBots].filter(bot => bot.status === 'stopped').length;
  
  return (
    <div className="bot-status-panel">
      <h3 className="panel-title">Bot Status Overview</h3>
      <div className="bot-status-grid">
        <div className="bot-status-item">
          <div className="status-label">Running Live Bots</div>
          <div className="status-value live">{runningLiveBots}</div>
        </div>
        <div className="bot-status-item">
          <div className="status-label">Running Sim Bots</div>
          <div className="status-value sim">{runningSimbots}</div>
        </div>
        <div className="bot-status-item">
          <div className="status-label">Paused Bots</div>
          <div className="status-value paused">{pausedBots}</div>
        </div>
        <div className="bot-status-item">
          <div className="status-label">Stopped Bots</div>
          <div className="status-value stopped">{stoppedBots}</div>
        </div>
      </div>
    </div>
  );
};

export default BotStatusPanel;
