const mongoose = require('mongoose');

const ApplicantSchema = new mongoose.Schema({
  // Personal Information
  firstName: {
    type: String,
    required: [true, 'Please provide your first name']
  },
  lastName: {
    type: String,
    required: [true, 'Please provide your last name']
  },
  email: {
    type: String,
    required: [true, 'Please provide an email'],
    match: [
      /^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/,
      'Please provide a valid email'
    ]
  },
  phone: {
    type: String,
    required: [true, 'Please provide a phone number']
  },
  address: {
    street: String,
    city: String,
    state: String,
    zipCode: String
  },
  
  // Position Information
  positionAppliedFor: {
    type: String,
    required: [true, 'Please specify the position you are applying for'],
    enum: ['Loctician', 'Braider', 'Barber', 'Manicurist/Pedicurist', 'Esthetician', 'Other']
  },
  otherPosition: {
    type: String
  },
  
  // Experience & Qualifications
  yearsOfExperience: {
    type: Number,
    required: [true, 'Please specify your years of experience']
  },
  isCertified: {
    type: Boolean,
    default: false
  },
  certifications: [{
    name: String,
    issuingAuthority: String,
    dateObtained: Date,
    expirationDate: Date
  }],
  isLicensed: {
    type: Boolean,
    default: false
  },
  licenses: [{
    licenseType: String,
    licenseNumber: String,
    state: String,
    expirationDate: Date
  }],
  
  // Work History
  workHistory: [{
    employerName: String,
    position: String,
    startDate: Date,
    endDate: Date,
    responsibilities: String,
    reasonForLeaving: String
  }],
  
  // Skills & Portfolio
  specialties: [String],
  portfolioLinks: [String],
  
  // Availability
  availability: {
    fullTime: Boolean,
    partTime: Boolean,
    weekends: Boolean,
    evenings: Boolean,
    specificDays: [String]
  },
  
  // Additional Information
  additionalInformation: String,
  
  // Application Status
  status: {
    type: String,
    enum: ['Submitted', 'Under Review', 'Interview Scheduled', 'Hired', 'Rejected'],
    default: 'Submitted'
  },
  
  // Timestamps
  createdAt: {
    type: Date,
    default: Date.now
  }
});

module.exports = mongoose.model('Applicant', ApplicantSchema);
