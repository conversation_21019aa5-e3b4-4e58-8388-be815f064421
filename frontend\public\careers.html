<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Careers – GetTwisted Hair Studios</title>

  <!-- SEO & Social Meta -->
  <meta name="description" content="GetTwisted Hair Studios – Join our team of professional stylists specializing in locs, braids, and natural hair care.">
  <meta property="og:title" content="Careers – GetTwisted Hair Studios" />
  <meta property="og:description" content="Join our team of natural hair specialists." />
  <meta property="og:image" content="https://www.gettwistedloc.com/assets/images/hero.jpg" />
  <meta property="og:url" content="https://www.gettwistedloc.com/careers.html" />

  <!-- Tailwind CSS -->
  <script src="https://cdn.tailwindcss.com"></script>
  <link href="https://fonts.googleapis.com/css2?family=Fredoka+One&display=swap" rel="stylesheet">
  <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">

  <!-- Custom CSS -->
  <link href="css/main.css" rel="stylesheet">

  <!-- Google Analytics -->
  <script async src="https://www.googletagmanager.com/gtag/js?id=G-92MSJDQS52"></script>
  <script>
    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);}
    gtag('js', new Date());
    gtag('config', 'G-92MSJDQS52');
  </script>

  <!-- Security Headers -->
  <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline' https://cdn.tailwindcss.com https://unpkg.com https://www.googletagmanager.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://unpkg.com; font-src 'self' https://fonts.gstatic.com; frame-src https://*.square.site/ https://*.squareup.com/; connect-src https://*.square.site/ https://*.squareup.com/; img-src 'self' https://*.square.site/ https://*.squareup.com/ data:; object-src 'none';">
  <meta http-equiv="X-Content-Type-Options" content="nosniff">
  <meta http-equiv="Referrer-Policy" content="no-referrer-when-downgrade">
  <!-- Removed X-Frame-Options to allow iframe embedding -->
  <meta http-equiv="Permissions-Policy" content="geolocation=(), microphone=(), camera=()">
</head>

<body>
<!-- Navbar -->
<header class="sticky top-0 z-50 p-6 shadow-md flex flex-wrap items-center bg-pink-600">
  <!-- Logo -->
  <a href="index.html" class="text-3xl font-extrabold tracking-wide flex-shrink-0 mr-auto text-yellow-300">
    GETTWISTED
    <span class="block text-sm md:text-lg text-cyan-300">HAIR STUDIOS</span>
  </a>

  <!-- Desktop Nav -->
  <nav id="navMenu" class="hidden md:flex space-x-4 text-sm md:text-base items-center">
    <a href="index.html" class="hover:underline">Home</a>
    <a href="stylists.html" class="hover:underline">Stylists</a>
    <a href="services.html" class="hover:underline">Services</a>
    <a href="careers.html" class="hover:underline">Careers</a>
    <a href="help.html" class="hover:underline">Help</a>
    <button onclick="toggleTheme()" id="themeToggleBtn" class="action-btn theme-btn" title="Change Theme">🎨</button>
    <button onclick="toggleChat()" class="action-btn chat-btn" title="Chat with Us">💬</button>
    <a href="sms:+16102880343" class="action-btn sms-btn" title="Text Us">📱</a>
  </nav>

  <!-- Mobile Toggle -->
  <button id="mobileMenuBtn" onclick="document.getElementById('mobileMenu').classList.toggle('hidden');" class="md:hidden bg-white text-black px-4 py-2 rounded-full shadow-lg font-bold ml-4 text-xl">
   ☰
  </button>

  <!-- Mobile Dropdown -->
  <div id="mobileMenu" class="w-full mt-4 hidden md:hidden bg-pink-600 py-3 px-2 rounded-lg shadow-lg">
    <nav class="flex flex-wrap justify-center items-center gap-4 text-sm text-white">
      <a href="index.html" class="hover:underline">Home</a>
      <a href="stylists.html" class="hover:underline">Stylists</a>
      <a href="services.html" class="hover:underline">Services</a>
      <a href="careers.html" class="hover:underline">Careers</a>
      <a href="help.html" class="hover:underline">Help</a>
      <div class="flex gap-3 mt-3 sm:mt-0">
        <button onclick="toggleTheme()" class="action-btn mobile-action-btn theme-btn" title="Change Theme">🎨</button>
        <button onclick="toggleChat()" class="action-btn mobile-action-btn chat-btn" title="Chat with Us">💬</button>
        <a href="sms:+16102880343" class="action-btn mobile-action-btn sms-btn" title="Text Us">📱</a>
      </div>
    </nav>
  </div>
</header>

<!-- Careers Banner -->
<section class="text-center py-16 bg-yellow-300 text-black" data-aos="fade-down">
  <h2 class="text-4xl md:text-5xl font-bold mb-4 theme-text">Join Our Team</h2>
  <p class="text-lg max-w-xl mx-auto theme-subtext">Be part of a creative, passionate team dedicated to natural hair care and styling excellence.</p>
</section>

<!-- Current Openings -->
<section class="p-6 max-w-6xl mx-auto">
  <h3 class="text-2xl font-bold mb-6 text-center">Current Openings</h3>

  <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
    <!-- Job Card 1 -->
    <div class="job-card bg-white p-6 rounded-xl shadow-lg" data-aos="fade-up">
      <h4 class="text-xl font-bold mb-2 text-pink-600">Loctician</h4>
      <p class="mb-4">We're looking for an experienced loctician to join our team. Must have 2+ years of experience with all stages of loc care.</p>
      <ul class="list-disc pl-5 mb-4 text-sm">
        <li>Create and maintain all types of locs</li>
        <li>Provide consultations for new clients</li>
        <li>Recommend products and maintenance routines</li>
      </ul>
      <button onclick="showJobForm('Loctician')" class="book-btn">Apply Now</button>
    </div>

    <!-- Job Card 2 -->
    <div class="job-card bg-white p-6 rounded-xl shadow-lg" data-aos="fade-up" data-aos-delay="100">
      <h4 class="text-xl font-bold mb-2 text-pink-600">Braider</h4>
      <p class="mb-4">Seeking a skilled braider with expertise in various braiding techniques and styles.</p>
      <ul class="list-disc pl-5 mb-4 text-sm">
        <li>Create box braids, knotless braids, and other styles</li>
        <li>Work efficiently while maintaining quality</li>
        <li>Provide excellent customer service</li>
      </ul>
      <button onclick="showJobForm('Braider')" class="book-btn">Apply Now</button>
    </div>

    <!-- Job Card 3 -->
    <div class="job-card bg-white p-6 rounded-xl shadow-lg" data-aos="fade-up" data-aos-delay="200">
      <h4 class="text-xl font-bold mb-2 text-pink-600">Barber</h4>
      <p class="mb-4">Looking for a licensed barber with experience in cutting and styling all hair types.</p>
      <ul class="list-disc pl-5 mb-4 text-sm">
        <li>Perform precision cuts, fades, and designs</li>
        <li>Maintain a clean and organized station</li>
        <li>Build and maintain a client base</li>
      </ul>
      <button onclick="showJobForm('Barber')" class="book-btn">Apply Now</button>
    </div>

    <!-- Job Card 4 -->
    <div class="job-card bg-white p-6 rounded-xl shadow-lg" data-aos="fade-up" data-aos-delay="300">
      <h4 class="text-xl font-bold mb-2 text-pink-600">Receptionist</h4>
      <p class="mb-4">Seeking a friendly, organized receptionist to manage our front desk operations.</p>
      <ul class="list-disc pl-5 mb-4 text-sm">
        <li>Schedule appointments and manage the booking system</li>
        <li>Greet clients and provide exceptional service</li>
        <li>Handle phone calls and inquiries</li>
      </ul>
      <button onclick="showJobForm('Receptionist')" class="book-btn">Apply Now</button>
    </div>
  </div>
</section>

<!-- Application Form -->
<section id="applicationForm" class="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 hidden">
  <div class="bg-white rounded-xl p-6 max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
    <div class="flex justify-between items-center mb-4">
      <h3 class="text-xl font-bold" id="formTitle">Apply for Position</h3>
      <button onclick="hideJobForm()" class="text-2xl">&times;</button>
    </div>

    <form id="careerForm" action="https://formspree.io/f/xnndngng" method="POST">
      <input type="hidden" id="position" name="position">

      <div class="mb-4">
        <label for="name" class="form-label">Full Name</label>
        <input type="text" id="name" name="name" class="form-input" required>
      </div>

      <div class="mb-4">
        <label for="email" class="form-label">Email</label>
        <input type="email" id="email" name="email" class="form-input" required>
      </div>

      <div class="mb-4">
        <label for="phone" class="form-label">Phone</label>
        <input type="tel" id="phone" name="phone" class="form-input" required>
      </div>

      <div class="mb-4">
        <label for="experience" class="form-label">Years of Experience</label>
        <select id="experience" name="experience" class="form-select" required>
          <option value="">Select</option>
          <option value="0-1">Less than 1 year</option>
          <option value="1-3">1-3 years</option>
          <option value="3-5">3-5 years</option>
          <option value="5+">5+ years</option>
        </select>
      </div>

      <div class="mb-4">
        <label for="certifications" class="form-label">Licenses & Certifications</label>
        <input type="text" id="certifications" name="certifications" class="form-input" placeholder="List any relevant licenses or certifications">
      </div>

      <div class="mb-4">
        <label for="portfolio" class="form-label">Portfolio Link (Optional)</label>
        <input type="url" id="portfolio" name="portfolio" class="form-input" placeholder="Link to your Instagram, website, etc.">
      </div>

      <div class="mb-4">
        <label for="message" class="form-label">Why do you want to join our team?</label>
        <textarea id="message" name="message" rows="4" class="form-input" required></textarea>
      </div>

      <div class="text-center">
        <button type="submit" class="submit-btn">Submit Application</button>
      </div>
    </form>
  </div>
</section>

<!-- Why Join Us -->
<section class="bg-pink-600 text-white py-16 px-6 mt-12">
  <div class="max-w-4xl mx-auto text-center">
    <h3 class="text-3xl font-bold mb-6">Why Join GetTwisted?</h3>
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
      <div data-aos="fade-up">
        <div class="bg-white text-pink-600 w-16 h-16 rounded-full flex items-center justify-center text-2xl mx-auto mb-4">💰</div>
        <h4 class="text-xl font-bold mb-2">Competitive Pay</h4>
        <p class="text-sm">Earn what you're worth with our competitive commission structure.</p>
      </div>
      <div data-aos="fade-up" data-aos-delay="100">
        <div class="bg-white text-pink-600 w-16 h-16 rounded-full flex items-center justify-center text-2xl mx-auto mb-4">🌱</div>
        <h4 class="text-xl font-bold mb-2">Growth Opportunities</h4>
        <p class="text-sm">Continuous education and advancement within our growing brand.</p>
      </div>
      <div data-aos="fade-up" data-aos-delay="200">
        <div class="bg-white text-pink-600 w-16 h-16 rounded-full flex items-center justify-center text-2xl mx-auto mb-4">🤝</div>
        <h4 class="text-xl font-bold mb-2">Supportive Team</h4>
        <p class="text-sm">Join a collaborative environment where creativity thrives.</p>
      </div>
    </div>
  </div>
</section>

<!-- Chatbot -->
<div id="chatbot" class="fixed bottom-28 right-5 bg-white w-80 rounded-xl shadow-lg overflow-hidden text-black z-50 hidden">
  <div class="bg-pink-600 text-white p-4 flex justify-between items-center">
    <h4 class="font-bold text-sm">💬 TwistyBot</h4>
    <button onclick="toggleChat()" class="text-white text-lg font-bold">&times;</button>
  </div>
  <div class="p-4 h-60 overflow-y-auto text-sm space-y-3" id="chatMessages">
    <div class="bg-pink-100 text-black p-3 rounded-lg w-fit max-w-[80%]">Hi! I'm TwistyBot 💖</div>
    <div class="bg-pink-100 text-black p-3 rounded-lg w-fit max-w-[80%]">How can I help you today?</div>
  </div>
  <div class="flex p-2 border-t">
    <input id="chatInput" type="text" placeholder="Type your question..." class="flex-1 text-sm p-2 outline-none" />
    <button id="sendBtn" class="bg-pink-600 text-white px-3 ml-2 rounded">Send</button>
  </div>
</div>

<!-- Footer -->
<footer class="px-4 sm:px-6 py-10 mt-20 text-center md:text-left">
  <div class="max-w-6xl mx-auto px-4 sm:px-6 grid grid-cols-1 md:grid-cols-3 gap-10 text-sm md:text-base text-center md:text-left">

    <!-- Brand Info -->
    <div>
      <h3 class="text-xl font-bold mb-2">GetTwisted Hair Studios</h3>
      <p>Where natural beauty meets expert care.</p>
      <p class="mt-2 italic">"Twist. Slay. Repeat."</p>
    </div>

    <!-- Contact Info with Multiple Locations -->
    <div>
      <h4 class="font-semibold mb-1">Contact Us</h4>
      <p>📍 Teaneck, NJ</p>
      <p>📍 Pottstown, PA</p>
      <p>📞 (*************</p>
      <p>📧 <EMAIL></p>
    </div>

    <!-- Quick Links -->
    <div class="space-y-2">
      <h4 class="font-semibold mb-1">Quick Links</h4>
      <a href="https://get-twisted-hair-studio.square.site/" target="_blank" class="block hover:underline">Book Appointment</a>
      <a href="stylists.html" class="block hover:underline">Meet Our Stylists</a>
      <a href="services.html" class="block hover:underline">Our Services</a>
    </div>
  </div>

  <p class="text-center text-xs footer-copy mt-8">&copy; 2024 GetTwisted Hair Studios. All rights reserved.</p>
</footer>

<!-- Scripts -->
<script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
<script src="js/main.js"></script>
<script>
  AOS.init();

  function toggleChat() {
    const chat = document.getElementById('chatbot');
    chat.classList.toggle('hidden');
    chat.classList.toggle('animate-slide-in');
  }

  function toggleTheme() {
    const themes = ['vibrant', 'black-white', 'matrix'];
    let currentTheme = themes.findIndex(t => document.body.classList.contains(`theme-${t}`));
    document.body.classList.remove(`theme-${themes[currentTheme]}`);
    currentTheme = (currentTheme + 1) % themes.length;
    document.body.classList.add(`theme-${themes[currentTheme]}`);
    localStorage.setItem('selectedTheme', themes[currentTheme]);
  }

  document.addEventListener("DOMContentLoaded", () => {
    const savedTheme = localStorage.getItem('selectedTheme') || 'vibrant';
    document.body.classList.add(`theme-${savedTheme}`);
  });

  // Mobile Menu Toggle is now handled by inline onclick attribute

  // Job Application Form
  function showJobForm(position) {
    document.getElementById('position').value = position;
    document.getElementById('formTitle').textContent = `Apply for ${position} Position`;
    document.getElementById('applicationForm').classList.remove('hidden');
  }

  function hideJobForm() {
    document.getElementById('applicationForm').classList.add('hidden');
    document.getElementById('careerForm').reset();
  }

  // FormSpree will handle the form submission
  document.getElementById('careerForm').addEventListener('submit', function(event) {
    // FormSpree will handle the form submission, but we'll show a thank you message
    setTimeout(function() {
      hideJobForm();
    }, 1000);
  });
</script>

<!-- Chatbot Script -->
<script>
  let chatbotKnowledge = [];

  fetch('assets/data/chatbot-knowledge.json')
    .then(response => response.json())
    .then(data => {
      chatbotKnowledge = data;
      console.log("Chatbot knowledge loaded:", chatbotKnowledge);
    })
    .catch(err => console.error("Error loading chatbot knowledge:", err));

  function sanitize(text) {
    return text.toLowerCase().replace(/[^\w\s]/gi, '').trim();
  }

  function handleChatInput(inputText) {
    const cleanInput = sanitize(inputText);

    let match = chatbotKnowledge.find(item => sanitize(item.question) === cleanInput);
    if (!match) {
      match = chatbotKnowledge.find(item => cleanInput.includes(sanitize(item.question)));
    }

    return match
      ? match.answer
      : "I'm here to help, but I don't have that answer yet. You can text us directly at (************* or visit our booking page to learn more.";
  }

  document.addEventListener("DOMContentLoaded", () => {
    const input = document.getElementById('chatInput');
    const sendBtn = document.getElementById('sendBtn');
    const chatBox = document.getElementById('chatMessages');

    function sendMessage() {
      const text = input.value.trim();
      if (!text) return;

      const userBubble = `<div class="bg-yellow-100 text-black p-3 rounded-lg w-fit max-w-[80%] ml-auto">${text}</div>`;
      const botReply = handleChatInput(text);
      const botBubble = `<div class="bg-pink-100 text-black p-3 rounded-lg w-fit max-w-[80%]">${botReply}</div>`;

      chatBox.innerHTML += userBubble + botBubble;
      input.value = '';
      chatBox.scrollTop = chatBox.scrollHeight;
    }

    sendBtn.addEventListener('click', sendMessage);
    input.addEventListener('keydown', e => { if (e.key === 'Enter') sendMessage(); });
  });
</script>

</body>
</html>
