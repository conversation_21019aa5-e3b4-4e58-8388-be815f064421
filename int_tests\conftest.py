"""
Common fixtures and utilities for integration tests.
"""
import os
import pytest
import time
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException

# Base URL for the website
BASE_URL = "file://" + os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))

@pytest.fixture(scope="function")
def driver():
    """
    Fixture to create and return a Chrome WebDriver instance.
    The driver is automatically closed after the test.
    """
    chrome_options = Options()
    # Run tests in headless mode to avoid browser window popping up
    chrome_options.add_argument("--headless")
    chrome_options.add_argument("--window-size=1920,1080")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--no-sandbox")

    try:
        # Try using the simple approach first
        driver = webdriver.Chrome(options=chrome_options)
    except Exception as e:
        print(f"First attempt failed: {e}")
        try:
            # Try with webdriver manager
            driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=chrome_options)
        except Exception as e:
            print(f"Second attempt failed: {e}")
            # Fall back to the most basic approach
            driver = webdriver.Chrome()

    driver.maximize_window()

    # Return the driver to the test
    yield driver

    # Close the driver after the test
    driver.quit()

@pytest.fixture
def wait(driver):
    """
    Fixture to create and return a WebDriverWait instance.
    """
    return WebDriverWait(driver, 10)

def navigate_to_page(driver, page):
    """
    Navigate to a specific page of the website.

    Args:
        driver: WebDriver instance
        page: Page to navigate to (e.g., "index.html", "gallery.html")
    """
    url = f"{BASE_URL}/{page}"
    driver.get(url)
    time.sleep(1)  # Allow page to load

def wait_for_element(driver, by, value, timeout=10):
    """
    Wait for an element to be present on the page.

    Args:
        driver: WebDriver instance
        by: By locator strategy (e.g., By.ID, By.CSS_SELECTOR)
        value: Locator value
        timeout: Maximum time to wait in seconds

    Returns:
        The found element
    """
    try:
        element = WebDriverWait(driver, timeout).until(
            EC.presence_of_element_located((by, value))
        )
        return element
    except TimeoutException:
        pytest.fail(f"Element not found: {by}={value}")

def wait_for_element_clickable(driver, by, value, timeout=10):
    """
    Wait for an element to be clickable on the page.

    Args:
        driver: WebDriver instance
        by: By locator strategy (e.g., By.ID, By.CSS_SELECTOR)
        value: Locator value
        timeout: Maximum time to wait in seconds

    Returns:
        The found element
    """
    try:
        element = WebDriverWait(driver, timeout).until(
            EC.element_to_be_clickable((by, value))
        )
        return element
    except TimeoutException:
        pytest.fail(f"Element not clickable: {by}={value}")

def check_element_exists(driver, by, value):
    """
    Check if an element exists on the page.

    Args:
        driver: WebDriver instance
        by: By locator strategy (e.g., By.ID, By.CSS_SELECTOR)
        value: Locator value

    Returns:
        True if the element exists, False otherwise
    """
    try:
        driver.find_element(by, value)
        return True
    except:
        return False
