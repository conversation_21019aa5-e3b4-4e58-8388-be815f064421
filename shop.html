<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>GetTwisted Hair Studios</title>

  <!-- SEO & Social Meta -->
  <meta name="description" content="GetTwisted Hair Studios – Locs, Braids & Natural Hair Specialists in Philly.">
  <meta property="og:title" content="GetTwisted Hair Studios" />
  <meta property="og:description" content="Book your natural hair appointment today." />
  <meta property="og:image" content="https://www.gettwistedloc.com/assets/images/hero.jpg" />
  <meta property="og:url" content="https://www.gettwistedloc.com" />

  <!-- Security Headers -->
  <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline' https://cdn.tailwindcss.com https://unpkg.com https://www.googletagmanager.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://unpkg.com; font-src 'self' https://fonts.gstatic.com; frame-src https://*.square.site/ https://*.squareup.com/; connect-src https://*.square.site/ https://*.squareup.com/; img-src 'self' https://*.square.site/ https://*.squareup.com/ data:; object-src 'none';">
  <meta http-equiv="X-Content-Type-Options" content="nosniff">
  <meta http-equiv="Referrer-Policy" content="no-referrer-when-downgrade">
  <!-- Removed X-Frame-Options to allow iframe embedding -->
  <meta http-equiv="Permissions-Policy" content="geolocation=(), microphone=(), camera=()">

  <!-- Google tag (gtag.js) -->
  <script async src="https://www.googletagmanager.com/gtag/js?id=G-QMXCEDX3LX"></script>
  <script>
    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);}
    gtag('js', new Date());

    gtag('config', 'G-QMXCEDX3LX');
  </script>

  <!-- Tailwind CSS -->
  <script src="https://cdn.tailwindcss.com"></script>
  <!-- Fonts + AOS -->
  <link href="https://fonts.googleapis.com/css2?family=Fredoka+One&display=swap" rel="stylesheet">
  <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">

  <!-- Custom CSS -->
  <link href="css/main.css" rel="stylesheet">

  <style>
    body { font-family: 'Fredoka One', cursive; }

    @keyframes slideIn {
      0% { transform: translateY(100%); opacity: 0; }
      100% { transform: translateY(0); opacity: 1; }
    }
    .animate-slide-in { animation: slideIn 0.3s ease-out forwards; }

    /* Themes */
    body.theme-vibrant { background-color: #f472b6; color: white; }
    body.theme-black-white { background-color: #000; color: #fff; }
    body.theme-matrix { background-color: #000; color: #00ff00; }
    body.theme-black-white header, body.theme-black-white section {
      background-color: #000 !important; color: #fff !important;
    }
    body.theme-matrix header, body.theme-matrix section {
      background-color: #000 !important; color: #00ff00 !important;
    }
    body.theme-vibrant header, body.theme-vibrant section {
      background-color: #ec4899 !important; color: white !important;
    }
    body.theme-black-white .footer-copy,
    body.theme-matrix .footer-copy { color: #aaa; }
    body.theme-vibrant .footer-copy { color: #f9a8d4; }

    .hero-section {
  transition: background-color 0.3s ease, color 0.3s ease;
  }
  body.theme-vibrant .hero-section {
    background: linear-gradient(to right, #ec4899, #fbbf24);
    color: white;
  }
  body.theme-black-white .hero-section {
    background-color: #111 !important;
    color: white !important;
  }
  body.theme-matrix .hero-section {
    background-color: #000 !important;
    color: #00ff00 !important;
  }

  /* Product card theme styles */
  body.theme-black-white .bg-white {
    background-color: #222 !important;
    color: white !important;
    border: 1px solid #444;
  }

  body.theme-matrix .bg-white {
    background-color: #001100 !important;
    color: #00ff00 !important;
    border: 1px solid #00aa00;
  }

  </style>
</head>

<!-- Navbar -->
<header class="sticky top-0 z-50 p-6 shadow-md flex flex-wrap items-center bg-pink-600">
  <!-- Logo -->
  <a href="index.html" class="text-3xl font-extrabold tracking-wide flex-shrink-0 mr-auto text-yellow-300">
    GETTWISTED
    <span class="block text-sm md:text-lg text-cyan-300">HAIR STUDIOS</span>
  </a>

  <!-- Desktop Nav -->
  <nav id="navMenu" class="hidden md:flex space-x-4 text-sm md:text-base items-center">
    <a href="index.html" class="hover:underline">Home</a>
    <a href="stylists.html" class="hover:underline">Stylists</a>
    <a href="shop.html" class="hover:underline">Shop</a>
    <a href="careers.html" class="hover:underline">Careers</a>
    <a href="help.html" class="hover:underline">Help</a>
    <button onclick="toggleTheme()" id="themeToggleBtn" class="action-btn theme-btn" title="Change Theme">🎨</button>
    <button onclick="toggleChat()" class="action-btn chat-btn" title="Chat with Us">💬</button>
    <a href="sms:+16102880343" class="action-btn sms-btn" title="Text Us">📱</a>
  </nav>

  <!-- Mobile Toggle -->
  <button id="mobileMenuBtn" class="md:hidden bg-white text-black px-4 py-2 rounded-full shadow-lg font-bold ml-4 text-xl">
   ☰
  </button>

  <!-- Mobile Dropdown -->
  <div id="mobileMenu" class="w-full mt-4 hidden md:hidden bg-pink-600 py-3 px-2 rounded-lg shadow-lg">
    <nav class="flex flex-wrap justify-center items-center gap-4 text-sm text-white">
      <a href="index.html" class="hover:underline font-medium">Home</a>
      <a href="stylists.html" class="hover:underline font-medium">Stylists</a>
      <a href="shop.html" class="hover:underline font-medium">Shop</a>
      <a href="careers.html" class="hover:underline font-medium">Careers</a>
      <a href="help.html" class="hover:underline font-medium">Help</a>
      <div class="flex gap-3 mt-3 sm:mt-0">
        <button onclick="toggleTheme()" class="action-btn mobile-action-btn theme-btn" title="Change Theme">🎨</button>
        <button onclick="toggleChat()" class="action-btn mobile-action-btn chat-btn" title="Chat with Us">💬</button>
        <a href="sms:+16102880343" class="action-btn mobile-action-btn sms-btn" title="Text Us">📱</a>
      </div>
    </nav>
  </div>

</header>

<!-- Hero -->
  <section class="hero-section text-center py-16" data-aos="fade-down">
    <h2 class="text-4xl md:text-5xl font-bold mb-4">Shop Our Essentials</h2>
    <p class="text-lg max-w-xl mx-auto">Products crafted for curls, coils, and crowns.</p>
  </section>

  <!-- Product Grid -->
  <section class="p-6 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6 max-w-6xl mx-auto">
    <div class="bg-white rounded-lg p-4 text-center shadow" data-aos="zoom-in">
      <img src="assets/images/product1.jpg" alt="Twist Cream" class="w-full h-48 object-cover mb-4 rounded">
      <h3 class="text-pink-600 text-lg">Twist & Define Cream</h3>
      <p class="mb-2">$18</p>
      <button class="bg-pink-500 text-white px-4 py-2 rounded hover:bg-pink-600 transition">Buy Now</button>
    </div>
    <div class="bg-white rounded-lg p-4 text-center shadow" data-aos="zoom-in" data-aos-delay="100">
      <img src="assets/images/product2.jpg" alt="Loc Gel" class="w-full h-48 object-cover mb-4 rounded">
      <h3 class="text-pink-600 text-lg">Loc Retwist Gel</h3>
      <p class="mb-2">$14</p>
      <button class="bg-pink-500 text-white px-4 py-2 rounded hover:bg-pink-600 transition">Buy Now</button>
    </div>
    <div class="bg-white rounded-lg p-4 text-center shadow" data-aos="zoom-in" data-aos-delay="200">
      <img src="assets/images/product3.jpg" alt="Edge Control" class="w-full h-48 object-cover mb-4 rounded">
      <h3 class="text-pink-600 text-lg">Edge Control</h3>
      <p class="mb-2">$10</p>
      <button class="bg-pink-500 text-white px-4 py-2 rounded hover:bg-pink-600 transition">Buy Now</button>
    </div>
  </section>

  <!-- Booking Modal -->
  <div id="bookingModal" class="fixed inset-0 z-50 hidden bg-black bg-opacity-60 flex items-center justify-center">
    <div class="bg-white rounded-xl max-w-3xl w-full mx-4 md:mx-0 shadow-lg relative">
      <div class="flex justify-between items-center px-6 py-4 bg-pink-600 text-white rounded-t-xl">
        <h3 class="text-lg font-bold">Book Your Appointment</h3>
        <button onclick="toggleBookingModal()" class="text-2xl leading-none hover:text-yellow-300">&times;</button>
      </div>
      <div class="p-4">
        <iframe src="https://get-twisted-hair-studio.square.site/" width="100%" height="600" frameborder="0" class="w-full rounded-md shadow-md"></iframe>
      </div>
    </div>
  </div>

  <!-- Chatbot -->
  <div id="chatbot" class="fixed bottom-28 right-5 bg-white w-80 rounded-xl shadow-lg overflow-hidden text-black z-50 hidden">
    <div class="bg-pink-600 text-white p-4 flex justify-between items-center">
      <h4 class="font-bold text-sm">💬 TwistyBot</h4>
      <button onclick="toggleChat()" class="text-white text-lg font-bold">&times;</button>
    </div>
    <div class="p-4 h-60 overflow-y-auto text-sm space-y-3" id="chatMessages">
      <div class="bg-pink-100 text-black p-3 rounded-lg w-fit max-w-[80%]">Hi! I’m TwistyBot 💖</div>
      <div class="bg-pink-100 text-black p-3 rounded-lg w-fit max-w-[80%]">How can I help you today?</div>
    </div>
    <div class="flex p-2 border-t">
      <input id="chatInput" type="text" placeholder="Type your question..." class="flex-1 text-sm p-2 outline-none" />
      <button id="sendBtn" class="bg-pink-600 text-white px-3 ml-2 rounded">Send</button>
    </div>
  </div>

<!-- Footer -->
<footer class="px-4 sm:px-6 py-10 mt-20 text-center md:text-left">
  <div class="max-w-6xl mx-auto px-4 sm:px-6 grid grid-cols-1 md:grid-cols-3 gap-10 text-sm md:text-base text-center md:text-left">

    <!-- Brand Info -->
    <div>
      <h3 class="text-xl font-bold mb-2">GetTwisted Hair Studios</h3>
      <p>Where natural beauty meets expert care.</p>
      <p class="mt-2 italic">"Twist. Slay. Repeat."</p>
    </div>

    <!-- Contact Info with Multiple Locations -->
    <div>
      <h4 class="font-semibold mb-1">Contact Us</h4>
      <p>📍 Teaneck, NJ</p>
      <p>📍 Pottstown, PA</p>
      <p>📞 (610) 288-0343</p>
      <p>📧 <EMAIL></p>
    </div>

    <!-- Quick Links -->
    <div class="space-y-2">
      <h4 class="font-semibold mb-1">Quick Links</h4>
      <a href="https://get-twisted-hair-studio.square.site/" target="_blank" class="block hover:underline">Book Appointment</a>
      <a href="stylists.html" class="block hover:underline">Meet Our Stylists</a>
      <a href="shop.html" class="block hover:underline">Shop Products</a>
    </div>
  </div>

  <p class="text-center text-xs footer-copy mt-8">&copy; 2024 GetTwisted Hair Studios. All rights reserved.</p>
</footer>

  <!-- Scripts -->
  <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
  <script>
    AOS.init();

    function toggleChat() {
      const chat = document.getElementById('chatbot');
      chat.classList.toggle('hidden');
      chat.classList.toggle('animate-slide-in');
    }

    function toggleTheme() {
      const themes = ['vibrant', 'black-white', 'matrix'];
      let currentTheme = themes.findIndex(t => document.body.classList.contains(`theme-${t}`));
      document.body.classList.remove(`theme-${themes[currentTheme]}`);
      currentTheme = (currentTheme + 1) % themes.length;
      document.body.classList.add(`theme-${themes[currentTheme]}`);
      localStorage.setItem('selectedTheme', themes[currentTheme]);
    }

    document.addEventListener("DOMContentLoaded", () => {
      const savedTheme = localStorage.getItem('selectedTheme') || 'vibrant';
      document.body.classList.add(`theme-${savedTheme}`);
    });

    // Booking modal functionality
    function toggleBookingModal() {
      const modal = document.getElementById('bookingModal');
      if (modal) {
        modal.classList.toggle('hidden');
        if (!modal.classList.contains('hidden')) {
          document.body.style.overflow = 'hidden'; // Prevent scrolling behind modal
        } else {
          document.body.style.overflow = ''; // Restore scrolling
        }
      }
    }
  </script>

  <script>
    let chatbotKnowledge = [];

    fetch('assets/data/chatbot-knowledge.json')
      .then(response => response.json())
      .then(data => {
        chatbotKnowledge = data;
        console.log("Chatbot knowledge loaded:", chatbotKnowledge);
      })
      .catch(err => console.error("Error loading chatbot knowledge:", err));

    function sanitize(text) {
      return text.toLowerCase().replace(/[^\w\s]/gi, '').trim();
    }

    function handleChatInput(inputText) {
      const cleanInput = sanitize(inputText);

      let match = chatbotKnowledge.find(item => sanitize(item.question) === cleanInput);
      if (!match) {
        match = chatbotKnowledge.find(item => cleanInput.includes(sanitize(item.question)));
      }

      return match
        ? match.answer
        : "I'm here to help, but I don’t have that answer yet. You can text us directly at (************* or visit our booking page to learn more.";
    }

    document.addEventListener("DOMContentLoaded", () => {
      const input = document.getElementById('chatInput');
      const sendBtn = document.getElementById('sendBtn');
      const chatBox = document.getElementById('chatMessages');

      function sendMessage() {
        const text = input.value.trim();
        if (!text) return;

        const userBubble = `<div class="bg-yellow-100 text-black p-3 rounded-lg w-fit max-w-[80%] ml-auto">${text}</div>`;
        const botReply = handleChatInput(text);
        const botBubble = `<div class="bg-pink-100 text-black p-3 rounded-lg w-fit max-w-[80%]">${botReply}</div>`;

        chatBox.innerHTML += userBubble + botBubble;
        input.value = '';
        chatBox.scrollTop = chatBox.scrollHeight;
      }

      sendBtn.addEventListener('click', sendMessage);
      input.addEventListener('keydown', e => { if (e.key === 'Enter') sendMessage(); });
    });
  </script>

<!-- Scripts -->
  <script src="js/main.js"></script>
  <script src="js/chatbot.js"></script>
  <!-- Fallback script for mobile menu -->
  <script>
    // Direct mobile menu toggle as fallback
    document.getElementById('mobileMenuBtn').addEventListener('click', function() {
      var mobileMenu = document.getElementById('mobileMenu');
      if (mobileMenu) {
        if (mobileMenu.classList.contains('hidden')) {
          mobileMenu.classList.remove('hidden');
        } else {
          mobileMenu.classList.add('hidden');
        }
      }
    });
  </script>

</body>
</html>
