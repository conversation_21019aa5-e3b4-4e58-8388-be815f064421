import React from 'react';

const Services = () => {
  // In a real app, this would fetch data from the API
  const services = [
    {
      id: 1,
      name: 'Starter Locs',
      category: 'Locs',
      price: 150,
      duration: 120,
      isActive: true
    },
    {
      id: 2,
      name: '<PERSON><PERSON>twist',
      category: 'Locs',
      price: 85,
      duration: 90,
      isActive: true
    },
    {
      id: 3,
      name: 'Box Braids',
      category: 'Braids',
      price: 200,
      duration: 240,
      isActive: true
    },
    {
      id: 4,
      name: 'Fade & Shape Up',
      category: 'Barber',
      price: 35,
      duration: 45,
      isActive: true
    },
    {
      id: 5,
      name: 'Natural Hair Styling',
      category: 'Natural Hair',
      price: 65,
      duration: 60,
      isActive: true
    }
  ];

  return (
    <div>
      <div className="flex justify-between items-center mb-4">
        <h1>Services</h1>
        <button className="btn btn-primary">Add New Service</button>
      </div>
      
      <div className="card">
        <table className="table">
          <thead>
            <tr>
              <th>Name</th>
              <th>Category</th>
              <th>Price</th>
              <th>Duration</th>
              <th>Status</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {services.map(service => (
              <tr key={service.id}>
                <td>{service.name}</td>
                <td>{service.category}</td>
                <td>${service.price}</td>
                <td>{service.duration} min</td>
                <td>
                  <span className={`p-4 rounded ${service.isActive ? 'bg-success' : 'bg-danger'}`}>
                    {service.isActive ? 'Active' : 'Inactive'}
                  </span>
                </td>
                <td>
                  <div className="flex gap-4">
                    <button className="btn btn-secondary">Edit</button>
                    <button className="btn btn-danger">Delete</button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default Services;
