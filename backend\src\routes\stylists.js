const express = require('express');
const router = express.Router();
const {
  getStylists,
  getStylist,
  createStylist,
  updateStylist,
  deleteStylist
} = require('../controllers/stylistController');

// Import middleware
const { protect, authorize } = require('../middleware/auth');

// Public routes
router.route('/').get(getStylists);
router.route('/:id').get(getStylist);

// Protected routes (admin only)
router
  .route('/')
  .post(protect, authorize('admin', 'super-admin'), createStylist);

router
  .route('/:id')
  .put(protect, authorize('admin', 'super-admin'), updateStylist)
  .delete(protect, authorize('admin', 'super-admin'), deleteStylist);

module.exports = router;
