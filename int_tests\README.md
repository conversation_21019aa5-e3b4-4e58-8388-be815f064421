# Integration Tests for GetTwisted Hair Studios Website

This folder contains automated integration tests for the GetTwisted Hair Studios website using Selenium WebDriver with Python.

## Setup

1. Install the required dependencies:
```bash
pip install -r requirements.txt
```

2. The tests will automatically download and use ChromeDriver, so no manual setup is required.

## Running Tests

### Using the Comprehensive Test Runner

The simplest way to run the tests is using the provided comprehensive test runner:

```bash
# Run all tests
python run_all_tests.py

# Run all tests in verbose mode
python run_all_tests.py --verbose

# Run a specific test category
python run_all_tests.py --category pages

# Run a specific test file
python run_all_tests.py --file test_gallery.py

# List all available test categories
python run_all_tests.py --list-categories

# List all available test files
python run_all_tests.py --list-files

# Run tests without generating an HTML report
python run_all_tests.py --no-html
```

### Using the Simple Test Runner

You can also use the simple test runner:

```bash
# Run all tests
python run_tests.py

# Run all tests in verbose mode
python run_tests.py --verbose

# Run a specific test file
python run_tests.py --file test_gallery.py
```

### Using pytest Directly

You can also use pytest directly:

```bash
# Run all tests
python -m pytest

# Run all tests in verbose mode
python -m pytest -v

# Run a specific test file
python -m pytest test_gallery.py

# Generate HTML report
python -m pytest --html=report.html --self-contained-html
```

## Test Categories

The tests are organized into the following categories:

- **pages**: Tests for individual pages (home, gallery, services, stylists, careers, help)
- **navigation**: Tests for navigation between pages
- **theme**: Tests for theme switching functionality
- **mobile**: Tests for mobile responsiveness
- **forms**: Tests for form functionality
- **booking**: Tests for booking functionality
- **chatbot**: Tests for chatbot functionality
- **gallery**: Tests specifically for the gallery page

## Test Files

- `test_home.py`: Tests for the home page
- `test_gallery.py`: Tests for the gallery page
- `test_services.py`: Tests for the services page
- `test_stylists.py`: Tests for the stylists page
- `test_careers.py`: Tests for the careers page
- `test_help.py`: Tests for the help page
- `test_navigation.py`: Tests for navigation between pages
- `test_theme.py`: Tests for theme switching functionality
- `test_mobile.py`: Tests for mobile responsiveness
- `test_forms.py`: Tests for form functionality
- `test_booking.py`: Tests for booking functionality
- `test_chatbot.py`: Tests for chatbot functionality

## Test Reports

When you run the tests using the `run_all_tests.py` script, an HTML report is automatically generated in the `int_tests` directory with a timestamp in the filename (e.g., `report_20240601_120000.html`). You can open this file in a web browser to view detailed test results.

## Troubleshooting

1. **WebDriver Issues**: If you encounter issues with ChromeDriver, you can manually download the appropriate version from [ChromeDriver Downloads](https://sites.google.com/a/chromium.org/chromedriver/downloads) and specify its path in `conftest.py`.

2. **Test Failures**: If tests fail, check the HTML report for detailed error messages and screenshots.

3. **Path Issues**: Make sure you're running the tests from the `int_tests` directory.

4. **Browser Compatibility**: The tests are designed to work with Chrome. If you want to use a different browser, modify the `driver` fixture in `conftest.py`.

## Adding New Tests

To add new tests:

1. Create a new test file following the naming convention `test_*.py`
2. Import the necessary utilities from `conftest.py`
3. Write your test functions using the `driver` and `wait` fixtures

Example:
```python
def test_new_feature(driver, wait):
    navigate_to_page(driver, "page.html")
    element = wait_for_element(driver, By.ID, "element-id")
    assert element.is_displayed()
```

## Common Test Utilities

Common test utilities and fixtures are defined in `conftest.py`. These include:

- `driver`: WebDriver instance for browser automation
- `wait`: WebDriverWait instance for waiting for elements
- `navigate_to_page`: Function to navigate to a specific page
- `wait_for_element`: Function to wait for an element to be present
- `wait_for_element_clickable`: Function to wait for an element to be clickable
- `check_element_exists`: Function to check if an element exists
