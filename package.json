{"name": "gettwisted<PERSON>s", "version": "1.0.0", "description": "GetTwisted Hair Studios - Professional hair styling services", "main": "backend/src/server.js", "scripts": {"start": "node backend/src/server.js", "dev:backend": "nodemon backend/src/server.js", "dev:frontend": "cd frontend && npm start", "dev:admin": "cd admin && npm start", "install:all": "npm install && cd frontend && npm install && cd ../admin && npm install", "test": "echo \"Error: no test specified\" && exit 1", "build": "node scripts/secure-build.js", "security:check": "npm audit && cd frontend && npm audit && cd ../admin && npm audit", "predeploy": "npm run security:check && npm run build", "deploy": "git push origin main"}, "keywords": ["hair", "salon", "locs", "braids", "styling"], "author": "GetTwisted Hair Studios", "license": "ISC", "dependencies": {"bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.21.2", "jsonwebtoken": "^9.0.2", "mongoose": "^7.8.6"}, "devDependencies": {"fs-extra": "^11.3.0", "glob": "^10.3.10", "http-server": "^14.1.1", "husky": "^8.0.3", "lint-staged": "^15.2.0", "nodemon": "^3.0.1"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.html": ["node scripts/secure-build.js"], "*.js": ["node scripts/check-security.js"]}}