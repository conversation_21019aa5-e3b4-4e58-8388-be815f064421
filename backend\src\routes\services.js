const express = require('express');
const router = express.Router();
const {
  getServices,
  getServicesByCategory,
  getService,
  createService,
  updateService,
  deleteService
} = require('../controllers/serviceController');

// Import middleware
const { protect, authorize } = require('../middleware/auth');

// Public routes
router.route('/').get(getServices);
router.route('/category/:categoryName').get(getServicesByCategory);
router.route('/:id').get(getService);

// Protected routes (admin only)
router
  .route('/')
  .post(protect, authorize('admin', 'super-admin'), createService);

router
  .route('/:id')
  .put(protect, authorize('admin', 'super-admin'), updateService)
  .delete(protect, authorize('admin', 'super-admin'), deleteService);

module.exports = router;
