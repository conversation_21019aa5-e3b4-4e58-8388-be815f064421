/**
 * Home Page Slider for GetTwisted Hair Studios
 * This script handles the horizontal slider functionality for the Recent Styles section
 */

// Gallery data for the slider
const galleryItems = [
  {
    id: 1,
    title: "Goddess Locs",
    description: "Long, flowing goddess locs with gold accessories",
    thumbnail: "assets/images/rs1.jpg",
    stylist: "Sheryl"
  },
  {
    id: 2,
    title: "Box Braids",
    description: "Medium-sized box braids with beads",
    thumbnail: "assets/images/rs3.jpg",
    stylist: "<PERSON>"
  },
  {
    id: 3,
    title: "Starter Locs",
    description: "Fresh starter locs with neat parts",
    thumbnail: "assets/images/rs4.jpg",
    stylist: "<PERSON>"
  },
  {
    id: 4,
    title: "Tapered Cut & Loc",
    description: "Tapered cut with maintained locs",
    thumbnail: "assets/images/rs5.jpg",
    stylist: "<PERSON>"
  },
  {
    id: 5,
    title: "Flat Twists",
    description: "Elegant flat twists with natural hair",
    thumbnail: "assets/images/rs7.jpg",
    stylist: "<PERSON>"
  },
  {
    id: 6,
    title: "Loc Retwist & Style",
    description: "Freshly retwisted and styled locs",
    thumbnail: "assets/images/rs9.jpg",
    stylist: "Sheryl"
  },
  {
    id: 7,
    title: "Natural Curls",
    description: "Defined natural curls with shine",
    thumbnail: "assets/images/rs2.jpg",
    stylist: "Shantell"
  },
  {
    id: 8,
    title: "Braided Updo",
    description: "Elegant braided updo for special occasions",
    thumbnail: "assets/images/rs8.jpg",
    stylist: "Shantell"
  },
  {
    id: 9,
    title: "Precision Cut",
    description: "Clean precision cut with shape up",
    thumbnail: "assets/images/rs10.jpg",
    stylist: "Marcus"
  }
];

// DOM elements
const sliderTrack = document.getElementById('slider-track');
const sliderPrev = document.getElementById('slider-prev');
const sliderNext = document.getElementById('slider-next');
const sliderIndicators = document.getElementById('slider-indicators');

// Current state
let currentSlide = 0;
let slidesPerView = 4; // Default, will be updated based on screen size

// Initialize slider
document.addEventListener('DOMContentLoaded', () => {
  // Update slides per view based on screen size
  updateSlidesPerView();
  
  // Initialize slider
  initSlider();
  
  // Handle window resize for responsive slider
  window.addEventListener('resize', () => {
    updateSlidesPerView();
    updateSlider();
  });
});

// Update slides per view based on screen width
function updateSlidesPerView() {
  if (window.innerWidth < 640) {
    slidesPerView = 1; // Mobile
  } else if (window.innerWidth < 768) {
    slidesPerView = 2; // Small tablets
  } else if (window.innerWidth < 1024) {
    slidesPerView = 3; // Tablets
  } else {
    slidesPerView = 4; // Desktop
  }
}

// Initialize the slider
function initSlider() {
  // Clear slider track and indicators
  sliderTrack.innerHTML = '';
  sliderIndicators.innerHTML = '';
  
  // Create slider items
  galleryItems.forEach((item) => {
    const sliderItem = document.createElement('div');
    sliderItem.className = 'slider-item flex-shrink-0 px-2';
    sliderItem.style.width = `calc(100% / ${slidesPerView})`;
    
    sliderItem.innerHTML = `
      <div class="relative group overflow-hidden rounded-lg shadow-md cursor-pointer h-64">
        <img src="${item.thumbnail}" alt="${item.title}" class="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110">
        <div class="absolute inset-0 bg-black bg-opacity-60 opacity-0 group-hover:opacity-100 transition duration-300 flex flex-col justify-end p-4 text-white">
          <h3 class="font-bold text-lg">${item.title}</h3>
          <p class="text-sm">${item.description}</p>
          <p class="text-xs mt-1">Stylist: ${item.stylist}</p>
        </div>
      </div>
    `;
    
    // Add click event to open gallery page
    sliderItem.addEventListener('click', () => {
      window.location.href = 'gallery.html';
    });
    
    sliderTrack.appendChild(sliderItem);
  });
  
  // Create indicators
  const totalSlides = Math.ceil(galleryItems.length / slidesPerView);
  for (let i = 0; i < totalSlides; i++) {
    const indicator = document.createElement('button');
    indicator.className = i === 0 ? 'w-3 h-3 rounded-full bg-pink-600' : 'w-3 h-3 rounded-full bg-gray-300';
    indicator.setAttribute('data-slide', i);
    indicator.addEventListener('click', () => {
      goToSlide(i);
    });
    sliderIndicators.appendChild(indicator);
  }
  
  // Set up slider navigation
  sliderPrev.addEventListener('click', prevSlide);
  sliderNext.addEventListener('click', nextSlide);
  
  // Initial position
  updateSlider();
}

// Update slider position
function updateSlider() {
  // Calculate total number of slides
  const totalSlides = Math.ceil(galleryItems.length / slidesPerView);
  
  // Make sure current slide is valid
  if (currentSlide >= totalSlides) {
    currentSlide = totalSlides - 1;
  }
  
  // Update slider track position
  sliderTrack.style.transform = `translateX(-${currentSlide * 100}%)`;
  
  // Update indicators
  const indicators = sliderIndicators.querySelectorAll('button');
  indicators.forEach((indicator, index) => {
    if (index === currentSlide) {
      indicator.classList.remove('bg-gray-300');
      indicator.classList.add('bg-pink-600');
    } else {
      indicator.classList.remove('bg-pink-600');
      indicator.classList.add('bg-gray-300');
    }
  });
  
  // Update slider items width
  const sliderItems = sliderTrack.querySelectorAll('.slider-item');
  sliderItems.forEach(item => {
    item.style.width = `calc(100% / ${slidesPerView})`;
  });
}

// Go to previous slide
function prevSlide() {
  const totalSlides = Math.ceil(galleryItems.length / slidesPerView);
  currentSlide = (currentSlide - 1 + totalSlides) % totalSlides;
  updateSlider();
}

// Go to next slide
function nextSlide() {
  const totalSlides = Math.ceil(galleryItems.length / slidesPerView);
  currentSlide = (currentSlide + 1) % totalSlides;
  updateSlider();
}

// Go to specific slide
function goToSlide(slideIndex) {
  currentSlide = slideIndex;
  updateSlider();
}
