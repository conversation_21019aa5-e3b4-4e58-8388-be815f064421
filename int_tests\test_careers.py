"""
Integration tests for the careers page.
"""
import pytest
import time
from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.ui import Select
from conftest import navigate_to_page, wait_for_element, wait_for_element_clickable, check_element_exists

def test_careers_page_loads(driver, wait):
    """Test that the careers page loads successfully."""
    navigate_to_page(driver, "careers.html")
    
    # Check that the page title is correct
    assert "Careers - GetTwisted Hair Studios" in driver.title
    
    # Check that the careers banner is displayed
    banner = wait_for_element(driver, By.CSS_SELECTOR, "section h2")
    assert "Join Our Team" in banner.text or "Careers" in banner.text
    
    # Check that the careers section is present
    careers_section = wait_for_element(driver, By.CSS_SELECTOR, "section.py-16")
    assert careers_section.is_displayed()

def test_job_listings(driver, wait):
    """Test that job listings are displayed correctly."""
    navigate_to_page(driver, "careers.html")
    
    # Check that job cards are present
    job_cards = driver.find_elements(By.CSS_SELECTOR, ".job-card")
    
    # Check that at least one job card exists
    assert len(job_cards) > 0
    
    # Check the first job card
    first_card = job_cards[0]
    assert first_card.is_displayed()
    
    # Check that the card contains a job title
    job_title = first_card.find_element(By.CSS_SELECTOR, "h3")
    assert job_title.text.strip() != ""
    
    # Check that the card contains job details
    job_details = first_card.find_elements(By.CSS_SELECTOR, "p, ul li")
    assert len(job_details) > 0
    
    # Check that the card contains an apply button
    apply_buttons = first_card.find_elements(By.XPATH, ".//button[contains(text(), 'Apply')] | .//a[contains(text(), 'Apply')]")
    assert len(apply_buttons) > 0

def test_application_form_modal(driver, wait):
    """Test that the application form modal opens and contains the correct fields."""
    navigate_to_page(driver, "careers.html")
    
    # Find all apply buttons
    apply_buttons = driver.find_elements(By.XPATH, "//button[contains(text(), 'Apply')] | //a[contains(text(), 'Apply')]")
    
    # Check that at least one apply button exists
    assert len(apply_buttons) > 0
    
    # Click the first apply button
    apply_button = apply_buttons[0]
    driver.execute_script("arguments[0].scrollIntoView(true);", apply_button)
    time.sleep(1)  # Allow scroll to complete
    apply_button.click()
    time.sleep(1)  # Allow modal to open
    
    # Check if a modal or form appears
    form_elements = driver.find_elements(By.CSS_SELECTOR, "form, #applicationForm, .modal")
    
    # If no form is found, the apply button might be a link to an external site
    if len(form_elements) == 0:
        # Check if the apply button is a link
        if apply_button.tag_name.lower() == "a":
            # Skip the rest of the test
            return
        else:
            pytest.fail("No application form found after clicking apply button")
    
    # Check that the form contains the necessary fields
    form = form_elements[0]
    
    # Look for common form fields
    name_field = form.find_elements(By.XPATH, ".//input[@name='name' or @id='name' or @placeholder='Name']")
    email_field = form.find_elements(By.XPATH, ".//input[@name='email' or @id='email' or @placeholder='Email']")
    phone_field = form.find_elements(By.XPATH, ".//input[@name='phone' or @id='phone' or @placeholder='Phone']")
    
    # Check that at least some of the expected fields are present
    assert len(name_field) > 0 or len(email_field) > 0 or len(phone_field) > 0
    
    # Check for a submit button
    submit_buttons = form.find_elements(By.XPATH, ".//button[@type='submit'] | .//input[@type='submit'] | .//button[contains(text(), 'Submit')] | .//button[contains(text(), 'Apply')]")
    assert len(submit_buttons) > 0

def test_form_validation(driver, wait):
    """Test that the application form validates input correctly."""
    navigate_to_page(driver, "careers.html")
    
    # Find all apply buttons
    apply_buttons = driver.find_elements(By.XPATH, "//button[contains(text(), 'Apply')] | //a[contains(text(), 'Apply')]")
    
    # Check that at least one apply button exists
    if len(apply_buttons) == 0:
        pytest.skip("No apply buttons found")
    
    # Click the first apply button
    apply_button = apply_buttons[0]
    driver.execute_script("arguments[0].scrollIntoView(true);", apply_button)
    time.sleep(1)  # Allow scroll to complete
    apply_button.click()
    time.sleep(1)  # Allow modal to open
    
    # Check if a modal or form appears
    form_elements = driver.find_elements(By.CSS_SELECTOR, "form, #applicationForm, .modal form")
    
    # If no form is found, the apply button might be a link to an external site
    if len(form_elements) == 0:
        # Skip the rest of the test
        pytest.skip("No application form found after clicking apply button")
    
    # Get the form
    form = form_elements[0]
    
    # Find the submit button
    submit_buttons = form.find_elements(By.XPATH, ".//button[@type='submit'] | .//input[@type='submit'] | .//button[contains(text(), 'Submit')] | .//button[contains(text(), 'Apply')]")
    
    if len(submit_buttons) == 0:
        pytest.skip("No submit button found in the form")
    
    submit_button = submit_buttons[0]
    
    # Try to submit the form without filling in required fields
    submit_button.click()
    time.sleep(1)  # Allow validation to trigger
    
    # Check if validation errors appear
    # This could be browser's built-in validation or custom validation
    invalid_fields = driver.find_elements(By.CSS_SELECTOR, ":invalid")
    error_messages = driver.find_elements(By.CSS_SELECTOR, ".error, .text-red-500, .text-red-600, .invalid-feedback")
    
    # Check that either invalid fields or error messages are present
    assert len(invalid_fields) > 0 or len(error_messages) > 0, "No validation errors found after submitting empty form"

def test_theme_compatibility(driver, wait):
    """Test that the careers page works correctly with different themes."""
    navigate_to_page(driver, "careers.html")
    
    # Get the initial theme
    body = driver.find_element(By.TAG_NAME, "body")
    initial_theme = body.get_attribute("class")
    
    # Click the theme toggle button
    theme_button = wait_for_element_clickable(driver, By.ID, "themeToggleBtn")
    theme_button.click()
    time.sleep(1)  # Allow theme to change
    
    # Check that the theme has changed
    new_theme = body.get_attribute("class")
    assert initial_theme != new_theme
    
    # Check that the job cards are still displayed
    job_cards = driver.find_elements(By.CSS_SELECTOR, ".job-card")
    if len(job_cards) > 0:
        assert job_cards[0].is_displayed()
    
    # Reset the theme
    theme_button.click()
    time.sleep(1)  # Allow theme to change
