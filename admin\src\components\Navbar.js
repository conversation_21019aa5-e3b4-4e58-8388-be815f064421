import React from 'react';
import { Link } from 'react-router-dom';

const Navbar = ({ sidebarOpen, setSidebarOpen, onLogout }) => {
  return (
    <nav className="navbar">
      <button 
        className="navbar-toggle" 
        onClick={() => setSidebarOpen(!sidebarOpen)}
      >
        ☰
      </button>
      <Link to="/" className="navbar-brand">
        GetTwisted Admin
      </Link>
      <div className="navbar-menu">
        <div className="navbar-item">
          <button 
            className="btn btn-danger"
            onClick={onLogout}
          >
            Logout
          </button>
        </div>
      </div>
    </nav>
  );
};

export default Navbar;
