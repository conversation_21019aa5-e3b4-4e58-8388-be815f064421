/* Trading Dashboard Styles */

/* Main Dashboard Layout */
.trading-dashboard {
  display: grid;
  grid-template-columns: 1fr 350px;
  grid-template-rows: auto auto 1fr;
  gap: 1.5rem;
  padding: 1rem;
}

.dashboard-header {
  grid-column: 1 / -1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.dashboard-title {
  font-size: 1.75rem;
  font-weight: 600;
  color: var(--dark);
}

/* Metrics Section */
.metrics-section {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1rem;
  grid-column: 1 / -1;
}

.metric-card {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 1.25rem;
  position: relative;
}

.metric-card.live {
  border-top: 3px solid var(--success);
}

.metric-card.sim {
  border-top: 3px solid var(--warning);
}

.metric-title {
  font-size: 0.875rem;
  color: #6b7280;
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
}

.metric-value {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.metric-value.positive {
  color: var(--success);
}

.metric-value.negative {
  color: var(--danger);
}

.metric-badge {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  font-weight: 600;
}

.metric-badge.live {
  background-color: var(--success);
  color: white;
}

.metric-badge.sim {
  background-color: var(--warning);
  color: var(--dark);
}

/* Bot Status Panel */
.bot-status-panel {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 1.25rem;
  grid-column: 2;
  grid-row: 2;
}

.panel-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--dark);
}

.bot-status-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.bot-status-item {
  padding: 0.75rem;
  border-radius: 0.375rem;
  background-color: #f9fafb;
}

.status-label {
  font-size: 0.875rem;
  color: #6b7280;
  margin-bottom: 0.5rem;
}

.status-value {
  font-size: 1.5rem;
  font-weight: 600;
  text-align: center;
}

.status-value.live {
  color: var(--success);
}

.status-value.sim {
  color: var(--warning);
}

.status-value.paused {
  color: var(--warning);
}

.status-value.stopped {
  color: var(--danger);
}

/* Bot Tables */
.bot-table-container {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 1.25rem;
  overflow-x: auto;
  grid-column: 1;
  grid-row: 2 / span 2;
}

.bot-table-container.live {
  border-left: 4px solid var(--success);
}

.bot-table-container.sim {
  border-left: 4px solid var(--warning);
  margin-top: 1.5rem;
}

.table-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--dark);
  display: flex;
  align-items: center;
}

.table-title::before {
  content: '';
  display: inline-block;
  width: 0.5rem;
  height: 0.5rem;
  border-radius: 50%;
  margin-right: 0.5rem;
}

.live .table-title::before {
  background-color: var(--success);
}

.sim .table-title::before {
  background-color: var(--warning);
}

.bot-table {
  width: 100%;
  border-collapse: collapse;
}

.bot-table th {
  text-align: left;
  padding: 0.75rem 1rem;
  font-weight: 600;
  color: #6b7280;
  border-bottom: 2px solid #f3f4f6;
}

.bot-table td {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #f3f4f6;
}

.bot-name {
  font-weight: 500;
  display: flex;
  align-items: center;
}

.bot-type-badge {
  font-size: 0.625rem;
  padding: 0.125rem 0.375rem;
  border-radius: 0.25rem;
  margin-left: 0.5rem;
}

.bot-type-badge.live {
  background-color: var(--success);
  color: white;
}

.bot-type-badge.sim {
  background-color: var(--warning);
  color: var(--dark);
}

.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  font-weight: 500;
}

.status-badge.running {
  background-color: var(--success);
  color: white;
}

.status-badge.paused {
  background-color: var(--warning);
  color: var(--dark);
}

.status-badge.stopped {
  background-color: var(--danger);
  color: white;
}

.positive {
  color: var(--success);
}

.negative {
  color: var(--danger);
}

.bot-actions {
  display: flex;
  gap: 0.5rem;
}

.action-btn {
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  font-weight: 500;
  cursor: pointer;
  border: none;
}

.action-btn.start {
  background-color: var(--success);
  color: white;
}

.action-btn.pause {
  background-color: var(--warning);
  color: var(--dark);
}

.action-btn.stop {
  background-color: var(--danger);
  color: white;
}

/* Performance Chart */
.performance-chart-container {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 1.25rem;
  grid-column: 1;
  grid-row: 4;
  margin-top: 1.5rem;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.chart-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--dark);
}

.chart-controls {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.chart-display-mode {
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  border: 1px solid #e5e7eb;
  background-color: white;
  font-size: 0.875rem;
}

.chart-area {
  height: 250px;
  margin-bottom: 1rem;
}

.mock-chart {
  height: 100%;
  display: flex;
  position: relative;
  border-bottom: 1px solid #e5e7eb;
  border-left: 1px solid #e5e7eb;
}

.chart-y-axis {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding-right: 0.5rem;
  font-size: 0.75rem;
  color: #6b7280;
}

.chart-content {
  flex: 1;
  position: relative;
}

.chart-line {
  position: absolute;
  height: 2px;
  width: 100%;
}

.chart-line.live {
  background-color: var(--success);
  top: 30%;
}

.chart-line.sim {
  background-color: var(--warning);
  top: 20%;
}

.chart-line-label {
  position: absolute;
  right: 0;
  top: -1.25rem;
  font-size: 0.75rem;
  font-weight: 500;
}

.live .chart-line-label {
  color: var(--success);
}

.sim .chart-line-label {
  color: var(--warning);
}

.chart-x-axis {
  display: flex;
  justify-content: space-between;
  padding-top: 0.5rem;
  font-size: 0.75rem;
  color: #6b7280;
}

.chart-legend {
  display: flex;
  gap: 1.5rem;
  margin-top: 0.5rem;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
}

.legend-color {
  width: 1rem;
  height: 0.25rem;
}

.legend-color.live {
  background-color: var(--success);
}

.legend-color.sim {
  background-color: var(--warning);
}

/* System Resources Monitor */
.system-resources-monitor {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 1.25rem;
  grid-column: 2;
  grid-row: 3;
}

.monitor-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--dark);
}

.resource-item {
  margin-bottom: 1rem;
}

.resource-label {
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
  display: flex;
  justify-content: space-between;
}

.resource-bar-container {
  height: 0.5rem;
  background-color: #f3f4f6;
  border-radius: 0.25rem;
  overflow: hidden;
}

.resource-bar {
  height: 100%;
  transition: width 0.3s ease;
}

.resource-bar.cpu.low {
  background-color: var(--success);
}

.resource-bar.cpu.medium {
  background-color: var(--warning);
}

.resource-bar.cpu.high {
  background-color: var(--danger);
}

.resource-bar.memory.low {
  background-color: var(--info);
}

.resource-bar.memory.medium {
  background-color: var(--warning);
}

.resource-bar.memory.high {
  background-color: var(--danger);
}

/* AI Market Analysis */
.ai-market-analysis {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 1.25rem;
  grid-column: 2;
  grid-row: 4;
  margin-top: 1.5rem;
}

.analysis-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.analysis-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--dark);
}

.analysis-toggle {
  display: flex;
}

.toggle-btn {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  border: 1px solid #e5e7eb;
  background-color: white;
  cursor: pointer;
}

.toggle-btn:first-child {
  border-radius: 0.25rem 0 0 0.25rem;
}

.toggle-btn:last-child {
  border-radius: 0 0.25rem 0.25rem 0;
}

.toggle-btn.active {
  background-color: var(--primary);
  color: white;
  border-color: var(--primary);
}

.analysis-content {
  font-size: 0.875rem;
}

.analysis-source {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.source-badge {
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  font-weight: 500;
}

.source-badge.live {
  background-color: var(--success);
  color: white;
}

.source-badge.sim {
  background-color: var(--warning);
  color: var(--dark);
}

.analysis-timestamp {
  font-size: 0.75rem;
  color: #6b7280;
}

.market-sentiment {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.sentiment-label {
  font-weight: 500;
}

.sentiment-value {
  font-weight: 600;
}

.sentiment-value.bullish {
  color: var(--success);
}

.sentiment-value.bearish {
  color: var(--danger);
}

.sentiment-value.neutral {
  color: var(--warning);
}

.market-trends {
  margin-bottom: 1rem;
}

.trend-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem;
  background-color: #f9fafb;
  border-radius: 0.25rem;
  margin-bottom: 0.5rem;
}

.trend-symbol {
  font-weight: 500;
  min-width: 80px;
}

.trend-direction {
  font-weight: 500;
  min-width: 60px;
}

.trend-direction.up {
  color: var(--success);
}

.trend-direction.down {
  color: var(--danger);
}

.trend-strength {
  color: #6b7280;
  font-size: 0.75rem;
}

.ai-recommendation {
  background-color: #f9fafb;
  padding: 0.75rem;
  border-radius: 0.25rem;
  border-left: 3px solid var(--primary);
}

.recommendation-label {
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.recommendation-value {
  font-size: 0.875rem;
  line-height: 1.5;
}

/* Bot Activity Feed */
.bot-activity-feed {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 1.25rem;
  grid-column: 2;
  grid-row: 5;
  margin-top: 1.5rem;
}

.feed-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--dark);
}

.activity-list {
  max-height: 300px;
  overflow-y: auto;
  font-size: 0.875rem;
}

.activity-item {
  padding: 0.5rem;
  border-bottom: 1px solid #f3f4f6;
  display: flex;
  gap: 0.5rem;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-item.trade-open {
  border-left: 3px solid var(--success);
}

.activity-item.trade-close {
  border-left: 3px solid var(--info);
}

.activity-item.system {
  border-left: 3px solid var(--primary);
}

.activity-item.error {
  border-left: 3px solid var(--danger);
}

.activity-time {
  color: #6b7280;
  font-size: 0.75rem;
  white-space: nowrap;
}

.activity-content {
  flex: 1;
}

.activity-bot {
  font-weight: 500;
  margin-right: 0.5rem;
  display: inline-flex;
  align-items: center;
}

.activity-message {
  color: #374151;
}

/* Section Divider */
.section-divider {
  grid-column: 1 / -1;
  display: flex;
  align-items: center;
  margin: 1.5rem 0;
}

.divider-line {
  flex: 1;
  height: 1px;
  background-color: #e5e7eb;
}

.divider-text {
  padding: 0 1rem;
  font-weight: 600;
  color: #6b7280;
  font-size: 1rem;
}

/* Responsive Adjustments */
@media (max-width: 1200px) {
  .trading-dashboard {
    grid-template-columns: 1fr;
  }
  
  .bot-status-panel,
  .system-resources-monitor,
  .ai-market-analysis,
  .bot-activity-feed {
    grid-column: 1;
  }
  
  .metrics-section {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .metrics-section {
    grid-template-columns: 1fr;
  }
}
