// Mock data for the trading dashboard

// Live trading bots
export const liveBots = [
  {
    id: 1,
    name: 'BTC/USDT Bot',
    symbol: 'BTC/USDT',
    status: 'running',
    winRate: 68,
    pnl: 4.2,
    trades: 25,
    strategy: 'Trend Following',
    lastTrade: {
      type: 'LONG',
      entryPrice: 63450,
      time: '2025-04-18T14:30:00Z'
    }
  },
  {
    id: 2,
    name: 'ETH/USDT Bot',
    symbol: 'ETH/USDT',
    status: 'running',
    winRate: 72,
    pnl: 5.8,
    trades: 18,
    strategy: 'Mean Reversion',
    lastTrade: {
      type: 'SHORT',
      entryPrice: 3120,
      time: '2025-04-18T15:15:00Z'
    }
  },
  {
    id: 3,
    name: 'SOL/USDT Bot',
    symbol: 'SOL/USDT',
    status: 'paused',
    winRate: 55,
    pnl: -1.2,
    trades: 11,
    strategy: 'Breakout',
    lastTrade: {
      type: 'LONG',
      entryPrice: 142.5,
      time: '2025-04-18T12:45:00Z'
    }
  },
  {
    id: 4,
    name: 'DOGE/USDT Bot',
    symbol: 'DOGE/USDT',
    status: 'stopped',
    winRate: 48,
    pnl: -3.5,
    trades: 23,
    strategy: 'Momentum',
    lastTrade: {
      type: 'SHORT',
      entryPrice: 0.1235,
      time: '2025-04-18T10:20:00Z'
    }
  }
];

// Simulation bots
export const simBots = [
  {
    id: 101,
    name: 'BTC/USDT Sim Bot',
    symbol: 'BTC/USDT',
    status: 'running',
    winRate: 75,
    pnl: 8.3,
    trades: 32,
    strategy: 'AI Prediction',
    lastTrade: {
      type: 'LONG',
      entryPrice: 63200,
      time: '2025-04-18T15:10:00Z'
    }
  },
  {
    id: 102,
    name: 'ETH/USDT Sim Bot',
    symbol: 'ETH/USDT',
    status: 'stopped',
    winRate: 62,
    pnl: 3.1,
    trades: 16,
    strategy: 'Grid Trading',
    lastTrade: {
      type: 'LONG',
      entryPrice: 3080,
      time: '2025-04-18T11:30:00Z'
    }
  },
  {
    id: 103,
    name: 'XRP/USDT Sim Bot',
    symbol: 'XRP/USDT',
    status: 'running',
    winRate: 70,
    pnl: 6.5,
    trades: 20,
    strategy: 'Scalping',
    lastTrade: {
      type: 'SHORT',
      entryPrice: 0.5235,
      time: '2025-04-18T15:25:00Z'
    }
  }
];

// Performance data
export const performanceData = {
  live: {
    currentPnl: 4.8,
    winRate: 65,
    data: [
      { time: '2025-04-18T00:00:00Z', value: 0 },
      { time: '2025-04-18T06:00:00Z', value: 1.2 },
      { time: '2025-04-18T12:00:00Z', value: 3.5 },
      { time: '2025-04-18T18:00:00Z', value: 4.8 }
    ]
  },
  sim: {
    currentPnl: 7.2,
    winRate: 72,
    data: [
      { time: '2025-04-18T00:00:00Z', value: 0 },
      { time: '2025-04-18T06:00:00Z', value: 2.5 },
      { time: '2025-04-18T12:00:00Z', value: 5.8 },
      { time: '2025-04-18T18:00:00Z', value: 7.2 }
    ]
  }
};

// System resources data
export const systemResources = {
  cpu: 22,
  memory: {
    used: 250,
    total: 1024,
    percentage: 24
  }
};

// AI market analysis
export const marketAnalysis = {
  live: {
    timestamp: '2025-04-18T15:30:00Z',
    sentiment: 'BULLISH',
    trends: [
      { symbol: 'BTC/USDT', direction: 'UP', strength: 8 },
      { symbol: 'ETH/USDT', direction: 'UP', strength: 7 },
      { symbol: 'SOL/USDT', direction: 'DOWN', strength: 6 }
    ],
    recommendation: 'Consider opening LONG positions on BTC and ETH. Monitor SOL for potential reversal.'
  },
  sim: {
    timestamp: '2025-04-18T15:45:00Z',
    sentiment: 'NEUTRAL',
    trends: [
      { symbol: 'BTC/USDT', direction: 'UP', strength: 5 },
      { symbol: 'ETH/USDT', direction: 'DOWN', strength: 4 },
      { symbol: 'XRP/USDT', direction: 'UP', strength: 6 }
    ],
    recommendation: 'Market showing mixed signals in simulation. Consider reduced position sizes and tighter stop-losses.'
  }
};

// Bot activity feed
export const botActivities = [
  {
    timestamp: '2025-04-18T15:43:00Z',
    type: 'TRADE',
    action: 'OPEN',
    botName: 'DOGE/USDT Bot',
    botType: 'LIVE',
    message: 'Opened LONG @0.0995'
  },
  {
    timestamp: '2025-04-18T15:42:00Z',
    type: 'TRADE',
    action: 'CLOSE',
    botName: 'DOGE/USDT Bot',
    botType: 'SIM',
    message: 'Closed SHORT +0.50% PnL'
  },
  {
    timestamp: '2025-04-18T15:40:00Z',
    type: 'SYSTEM',
    message: 'CPU Spiked to 32% running 4 bots'
  },
  {
    timestamp: '2025-04-18T15:38:00Z',
    type: 'TRADE',
    action: 'OPEN',
    botName: 'BTC/USDT Bot',
    botType: 'LIVE',
    message: 'Opened LONG @63450'
  },
  {
    timestamp: '2025-04-18T15:35:00Z',
    type: 'TRADE',
    action: 'CLOSE',
    botName: 'ETH/USDT Bot',
    botType: 'SIM',
    message: 'Closed LONG +1.20% PnL'
  },
  {
    timestamp: '2025-04-18T15:32:00Z',
    type: 'ERROR',
    botName: 'SOL/USDT Bot',
    botType: 'LIVE',
    message: 'API connection timeout - retrying'
  },
  {
    timestamp: '2025-04-18T15:30:00Z',
    type: 'SYSTEM',
    message: 'AI Market Analysis updated'
  },
  {
    timestamp: '2025-04-18T15:28:00Z',
    type: 'TRADE',
    action: 'OPEN',
    botName: 'XRP/USDT Bot',
    botType: 'SIM',
    message: 'Opened SHORT @0.5235'
  }
];
