<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Our Services – GetTwisted Hair Studios</title>

  <!-- SEO & Social Meta -->
  <meta name="description" content="GetTwisted Hair Studios – Professional hair services including locs, braids, twists, barber services, and more.">
  <meta property="og:title" content="Our Services – GetTwisted Hair Studios" />
  <meta property="og:description" content="Explore our full range of professional hair services." />
  <meta property="og:image" content="https://www.gettwistedloc.com/assets/images/hero.jpg" />
  <meta property="og:url" content="https://www.gettwistedloc.com/services.html" />

  <!-- Tailwind CSS -->
  <script src="https://cdn.tailwindcss.com"></script>
  <link href="https://fonts.googleapis.com/css2?family=Fredoka+One&display=swap" rel="stylesheet">
  <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">

  <!-- Custom CSS -->
  <link href="css/main.css" rel="stylesheet">

  <!-- Google Analytics -->
  <script async src="https://www.googletagmanager.com/gtag/js?id=G-92MSJDQS52"></script>
  <script>
    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);}
    gtag('js', new Date());
    gtag('config', 'G-92MSJDQS52');
  </script>

  <!-- Security Headers -->
  <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline' https://cdn.tailwindcss.com https://unpkg.com https://www.googletagmanager.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://unpkg.com; font-src 'self' https://fonts.gstatic.com; frame-src https://*.square.site/ https://*.squareup.com/; connect-src https://*.square.site/ https://*.squareup.com/; img-src 'self' https://*.square.site/ https://*.squareup.com/ data:; object-src 'none';">
  <meta http-equiv="X-Content-Type-Options" content="nosniff">
  <meta http-equiv="Referrer-Policy" content="no-referrer-when-downgrade">
  <!-- Removed X-Frame-Options to allow iframe embedding -->
  <meta http-equiv="Permissions-Policy" content="geolocation=(), microphone=(), camera=()">
</head>

<!-- Navbar -->
<header class="sticky top-0 z-50 p-6 shadow-md flex flex-wrap items-center bg-pink-600">
  <!-- Logo -->
  <a href="index.html" class="text-3xl font-extrabold tracking-wide flex-shrink-0 mr-auto text-yellow-300">
    GETTWISTED
    <span class="block text-sm md:text-lg text-cyan-300">HAIR STUDIOS</span>
  </a>

  <!-- Desktop Nav -->
  <nav id="navMenu" class="hidden md:flex space-x-4 text-sm md:text-base items-center">
    <a href="index.html" class="hover:underline">Home</a>
    <a href="stylists.html" class="hover:underline">Stylists</a>
    <a href="services.html" class="hover:underline">Services</a>
    <a href="gallery.html" class="hover:underline">Gallery</a>
    <a href="careers.html" class="hover:underline">Careers</a>
    <a href="help.html" class="hover:underline">Help</a>
    <button onclick="toggleTheme()" id="themeToggleBtn" class="action-btn theme-btn" title="Change Theme">🎨</button>
    <button onclick="toggleChat()" class="action-btn chat-btn" title="Chat with Us">💬</button>
    <a href="sms:+16102880343" class="action-btn sms-btn" title="Text Us">📱</a>
  </nav>

  <!-- Mobile Toggle -->
  <button id="mobileMenuBtn" class="md:hidden bg-white text-black px-4 py-2 rounded-full shadow-lg font-bold ml-4 text-xl">
   ☰
  </button>

  <!-- Mobile Dropdown -->
  <div id="mobileMenu" class="w-full mt-4 hidden md:hidden bg-pink-600 py-3 px-2 rounded-lg shadow-lg">
    <nav class="flex flex-wrap justify-center items-center gap-4 text-sm text-white">
      <a href="index.html" class="hover:underline">Home</a>
      <a href="stylists.html" class="hover:underline">Stylists</a>
      <a href="services.html" class="hover:underline">Services</a>
      <a href="gallery.html" class="hover:underline">Gallery</a>
      <a href="careers.html" class="hover:underline">Careers</a>
      <a href="help.html" class="hover:underline">Help</a>
      <div class="flex gap-3 mt-3 sm:mt-0">
        <button onclick="toggleTheme()" class="action-btn mobile-action-btn theme-btn" title="Change Theme">🎨</button>
        <button onclick="toggleChat()" class="action-btn mobile-action-btn chat-btn" title="Chat with Us">💬</button>
        <a href="sms:+16102880343" class="action-btn mobile-action-btn sms-btn" title="Text Us">📱</a>
      </div>
    </nav>
  </div>
</header>

<!-- Services Banner -->
<section class="text-center py-16 bg-yellow-300 text-black" data-aos="fade-down">
  <h2 class="text-4xl md:text-5xl font-bold mb-4 theme-text">Our Services</h2>
  <p class="text-lg max-w-xl mx-auto theme-subtext">Professional hair services for all textures and styles</p>
  <a href="https://get-twisted-hair-studio.square.site/" target="_blank" class="mt-6 inline-block bg-pink-600 hover:bg-pink-700 text-white font-bold py-3 px-6 rounded-full shadow-lg transition">Book Appointment</a>
</section>

<!-- Services Categories -->
<section class="p-6 max-w-6xl mx-auto">
  <!-- Category Tabs -->
  <div class="mb-8">
    <div class="text-center mb-4">
      <h3 class="text-2xl font-bold theme-text">Browse Services by Category</h3>
    </div>
    <div id="category-tabs" class="flex flex-wrap justify-center gap-3 mb-8 px-4">
      <!-- Tabs will be loaded here via JavaScript -->
    </div>
  </div>

  <div id="services-container" class="space-y-12">
    <!-- Services will be loaded here via JavaScript -->
    <div class="text-center py-8">
      <div class="animate-spin inline-block w-8 h-8 border-4 border-pink-600 border-t-transparent rounded-full"></div>
      <p class="mt-4">Loading services...</p>
    </div>
  </div>
</section>

<!-- Chatbot -->
<div id="chatbot" class="fixed bottom-28 right-5 bg-white w-80 rounded-xl shadow-lg overflow-hidden text-black z-50 hidden">
  <div class="bg-pink-600 text-white p-4 flex justify-between items-center">
    <h4 class="font-bold text-sm">💬 TwistyBot</h4>
    <button onclick="toggleChat()" class="text-white text-lg font-bold">&times;</button>
  </div>
  <div class="p-4 h-60 overflow-y-auto text-sm space-y-3" id="chatMessages">
    <div class="bg-pink-100 text-black p-3 rounded-lg w-fit max-w-[80%]">Hi! I'm TwistyBot 💖</div>
    <div class="bg-pink-100 text-black p-3 rounded-lg w-fit max-w-[80%]">How can I help you today?</div>
  </div>
  <div class="flex p-2 border-t">
    <input id="chatInput" type="text" placeholder="Type your question..." class="flex-1 text-sm p-2 outline-none" />
    <button id="sendBtn" class="bg-pink-600 text-white px-3 ml-2 rounded">Send</button>
  </div>
</div>

<!-- Footer -->
<footer class="px-4 sm:px-6 py-10 mt-20 text-center md:text-left">
  <div class="max-w-6xl mx-auto px-4 sm:px-6 grid grid-cols-1 md:grid-cols-3 gap-10 text-sm md:text-base text-center md:text-left">

    <!-- Brand Info -->
    <div>
      <h3 class="text-xl font-bold mb-2">GetTwisted Hair Studios</h3>
      <p>Where natural beauty meets expert care.</p>
      <p class="mt-2 italic">"Twist. Slay. Repeat."</p>
    </div>

    <!-- Contact Info with Multiple Locations -->
    <div>
      <h4 class="font-semibold mb-1">Contact Us</h4>
      <p>📍 Teaneck, NJ</p>
      <p>📍 Pottstown, PA</p>
      <p>📞 (*************</p>
      <p>📧 <EMAIL></p>
    </div>

    <!-- Quick Links -->
    <div class="space-y-2">
      <h4 class="font-semibold mb-1">Quick Links</h4>
      <a href="https://get-twisted-hair-studio.square.site/" target="_blank" class="block hover:underline">Book Appointment</a>
      <a href="stylists.html" class="block hover:underline">Meet Our Stylists</a>
      <a href="services.html" class="block hover:underline">Our Services</a>
      <a href="gallery.html" class="block hover:underline">Our Gallery</a>
    </div>
  </div>

  <p class="text-center text-xs footer-copy mt-8">&copy; 2024 GetTwisted Hair Studios. All rights reserved.</p>
</footer>

<!-- Scripts -->
<script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
<script src="js/main.js"></script>
<script>
  AOS.init();

  // Legacy functions - now handled in main.js
  function toggleChat() {
    const chat = document.getElementById('chatbot');
    if (chat) {
      chat.classList.toggle('hidden');
      chat.classList.toggle('animate-slide-in');
    }
  }

  // Theme functionality moved to main.js

  document.addEventListener("DOMContentLoaded", () => {
    const savedTheme = localStorage.getItem('selectedTheme') || 'vibrant';
    document.body.classList.add(`theme-${savedTheme}`);

    // Load services data
    loadServices();
  });

  // Mobile Menu Toggle is now handled by inline onclick attribute

  // Load and display services
  async function loadServices() {
    try {
      const response = await fetch('assets/data/services-pricing.json');
      if (!response.ok) {
        throw new Error('Failed to load services data');
      }

      const data = await response.json();
      const servicesContainer = document.getElementById('services-container');
      const categoryTabs = document.getElementById('category-tabs');

      // Clear loading indicator
      servicesContainer.innerHTML = '';

      // Create tabs for each category
      data.categories.forEach((category, index) => {
        const tabButton = document.createElement('button');
        tabButton.className = index === 0 ?
          'px-5 py-3 rounded-full bg-pink-600 text-white font-bold transition shadow-md hover:shadow-lg text-sm md:text-base' :
          'px-5 py-3 rounded-full bg-gray-200 hover:bg-gray-300 text-gray-800 font-bold transition shadow hover:shadow-md text-sm md:text-base';
        tabButton.textContent = category.name;
        tabButton.setAttribute('data-category', index);
        tabButton.onclick = () => showCategory(index);
        categoryTabs.appendChild(tabButton);

        // Create category content div
        const categoryElement = document.createElement('div');
        categoryElement.className = index === 0 ? 'category-content' : 'category-content hidden';
        categoryElement.setAttribute('data-category', index);
        categoryElement.setAttribute('data-aos', 'fade-up');

        categoryElement.innerHTML = `
          <h3 class="text-3xl font-bold mb-4 text-center md:text-left">${category.name}</h3>
          <p class="text-lg mb-6 text-center md:text-left">${category.description}</p>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            ${category.services.map(service => `
              <div class="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
                <div class="bg-pink-600 text-white p-4">
                  <h4 class="font-bold text-xl">${service.name}</h4>
                </div>
                <div class="p-6">
                  <p class="mb-4">${service.description}</p>
                  <div class="flex justify-between items-center mb-2">
                    <span class="font-bold text-pink-600">${service.price_range}</span>
                    <span class="text-sm text-gray-600">${service.time_range}</span>
                  </div>
                  <p class="text-sm text-gray-500 italic">${service.notes}</p>
                  <a href="https://get-twisted-hair-studio.square.site/" target="_blank" class="mt-4 inline-block bg-yellow-300 hover:bg-yellow-400 text-black font-bold py-2 px-4 rounded-full shadow transition w-full text-center">Book Now</a>
                </div>
              </div>
            `).join('')}
          </div>
        `;

        servicesContainer.appendChild(categoryElement);
      });

      // Function to show a specific category
      window.showCategory = function(categoryIndex) {
        // Update tab buttons
        const tabs = categoryTabs.querySelectorAll('button');
        tabs.forEach((tab, index) => {
          if (index === categoryIndex) {
            tab.className = 'px-5 py-3 rounded-full bg-pink-600 text-white font-bold transition shadow-md hover:shadow-lg text-sm md:text-base';
          } else {
            tab.className = 'px-5 py-3 rounded-full bg-gray-200 hover:bg-gray-300 text-gray-800 font-bold transition shadow hover:shadow-md text-sm md:text-base';
          }
        });

        // Show selected category, hide others
        const categoryContents = document.querySelectorAll('.category-content');
        categoryContents.forEach((content, index) => {
          if (index === categoryIndex) {
            content.classList.remove('hidden');
          } else {
            content.classList.add('hidden');
          }
        });
      };

    } catch (error) {
      console.error('Error loading services:', error);
      const servicesContainer = document.getElementById('services-container');
      servicesContainer.innerHTML = `
        <div class="text-center py-8">
          <p class="text-red-600">Failed to load services. Please try again later.</p>
        </div>
      `;
    }
  }
</script>

<!-- Chatbot Script -->
<script>
  let chatbotKnowledge = [];

  fetch('assets/data/chatbot-knowledge.json')
    .then(response => response.json())
    .then(data => {
      chatbotKnowledge = data;
      console.log("Chatbot knowledge loaded:", chatbotKnowledge);
    })
    .catch(err => console.error("Error loading chatbot knowledge:", err));

  function sanitize(text) {
    return text.toLowerCase().replace(/[^\w\s]/gi, '').trim();
  }

  function handleChatInput(inputText) {
    const cleanInput = sanitize(inputText);

    let match = chatbotKnowledge.find(item => sanitize(item.question) === cleanInput);
    if (!match) {
      match = chatbotKnowledge.find(item => cleanInput.includes(sanitize(item.question)));
    }

    return match
      ? match.answer
      : "I'm here to help, but I don't have that answer yet. You can text us directly at (************* or visit our booking page to learn more.";
  }

  document.addEventListener("DOMContentLoaded", () => {
    const input = document.getElementById('chatInput');
    const sendBtn = document.getElementById('sendBtn');
    const chatBox = document.getElementById('chatMessages');

    function sendMessage() {
      const text = input.value.trim();
      if (!text) return;

      const userBubble = `<div class="bg-yellow-100 text-black p-3 rounded-lg w-fit max-w-[80%] ml-auto">${text}</div>`;
      const botReply = handleChatInput(text);
      const botBubble = `<div class="bg-pink-100 text-black p-3 rounded-lg w-fit max-w-[80%]">${botReply}</div>`;

      chatBox.innerHTML += userBubble + botBubble;
      input.value = '';
      chatBox.scrollTop = chatBox.scrollHeight;
    }

    sendBtn.addEventListener('click', sendMessage);
    input.addEventListener('keydown', e => { if (e.key === 'Enter') sendMessage(); });
  });
</script>

</body>
</html>
