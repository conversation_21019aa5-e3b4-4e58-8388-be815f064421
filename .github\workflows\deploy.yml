name: Deploy Website

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v2
    
    - name: Setup Node.js
      uses: actions/setup-node@v2
      with:
        node-version: '16'
        
    - name: Install Dependencies
      run: npm ci
      
    - name: Security Audit
      run: npm audit --production
      
    - name: Build Frontend
      run: npm run build
      
    - name: Deploy to GitHub Pages
      if: github.ref == 'refs/heads/main' && github.event_name == 'push'
      uses: JamesIves/github-pages-deploy-action@4.1.4
      with:
        branch: gh-pages
        folder: .
        clean: true
        
    - name: Security Headers Check
      run: |
        echo "Checking for security headers in HTML files..."
        grep -l "Content-Security-Policy" *.html || echo "Warning: Missing CSP headers in some files"
