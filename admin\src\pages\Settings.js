import React, { useState } from 'react';

const Settings = () => {
  const [settings, setSettings] = useState({
    businessName: 'GetTwisted Hair Studios',
    email: '<EMAIL>',
    phone: '(*************',
    address: 'Philadelphia, PA',
    openingHours: '9:00 AM - 7:00 PM',
    squareLocationId: 'L8KQXF9S5FHFK',
    enableNotifications: true,
    enableBookingReminders: true,
    enableMarketingEmails: false
  });

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setSettings({
      ...settings,
      [name]: type === 'checkbox' ? checked : value
    });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    // In a real app, this would save to the API
    alert('Settings saved successfully!');
  };

  return (
    <div>
      <h1>Settings</h1>
      
      <div className="card">
        <h2>Business Information</h2>
        <form onSubmit={handleSubmit}>
          <div className="form-group">
            <label className="form-label" htmlFor="businessName">Business Name</label>
            <input
              type="text"
              id="businessName"
              name="businessName"
              className="form-input"
              value={settings.businessName}
              onChange={handleChange}
            />
          </div>
          
          <div className="form-group">
            <label className="form-label" htmlFor="email">Email</label>
            <input
              type="email"
              id="email"
              name="email"
              className="form-input"
              value={settings.email}
              onChange={handleChange}
            />
          </div>
          
          <div className="form-group">
            <label className="form-label" htmlFor="phone">Phone</label>
            <input
              type="text"
              id="phone"
              name="phone"
              className="form-input"
              value={settings.phone}
              onChange={handleChange}
            />
          </div>
          
          <div className="form-group">
            <label className="form-label" htmlFor="address">Address</label>
            <input
              type="text"
              id="address"
              name="address"
              className="form-input"
              value={settings.address}
              onChange={handleChange}
            />
          </div>
          
          <div className="form-group">
            <label className="form-label" htmlFor="openingHours">Opening Hours</label>
            <input
              type="text"
              id="openingHours"
              name="openingHours"
              className="form-input"
              value={settings.openingHours}
              onChange={handleChange}
            />
          </div>
          
          <h2 className="mt-4">Integration Settings</h2>
          
          <div className="form-group">
            <label className="form-label" htmlFor="squareLocationId">Square Location ID</label>
            <input
              type="text"
              id="squareLocationId"
              name="squareLocationId"
              className="form-input"
              value={settings.squareLocationId}
              onChange={handleChange}
            />
          </div>
          
          <h2 className="mt-4">Notification Settings</h2>
          
          <div className="form-group">
            <label className="form-label">
              <input
                type="checkbox"
                name="enableNotifications"
                checked={settings.enableNotifications}
                onChange={handleChange}
              />
              {' '}Enable System Notifications
            </label>
          </div>
          
          <div className="form-group">
            <label className="form-label">
              <input
                type="checkbox"
                name="enableBookingReminders"
                checked={settings.enableBookingReminders}
                onChange={handleChange}
              />
              {' '}Send Booking Reminders
            </label>
          </div>
          
          <div className="form-group">
            <label className="form-label">
              <input
                type="checkbox"
                name="enableMarketingEmails"
                checked={settings.enableMarketingEmails}
                onChange={handleChange}
              />
              {' '}Send Marketing Emails
            </label>
          </div>
          
          <button type="submit" className="btn btn-primary mt-4">
            Save Settings
          </button>
        </form>
      </div>
    </div>
  );
};

export default Settings;
