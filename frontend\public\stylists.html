<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Our Stylists – GetTwisted Hair Studios</title>

  <!-- SEO & Social Meta -->
  <meta name="description" content="GetTwisted Hair Studios – Meet our professional stylists specializing in locs, braids, and natural hair care.">
  <meta property="og:title" content="Our Stylists – GetTwisted Hair Studios" />
  <meta property="og:description" content="Meet our team of natural hair specialists." />
  <meta property="og:image" content="https://www.gettwistedloc.com/assets/images/hero.jpg" />
  <meta property="og:url" content="https://www.gettwistedloc.com/stylists.html" />

  <!-- Tailwind CSS -->
  <script src="https://cdn.tailwindcss.com"></script>
  <link href="https://fonts.googleapis.com/css2?family=Fredoka+One&display=swap" rel="stylesheet">
  <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">

  <!-- Custom CSS -->
  <link href="css/main.css" rel="stylesheet">

  <!-- Google Analytics -->
  <script async src="https://www.googletagmanager.com/gtag/js?id=G-92MSJDQS52"></script>
  <script>
    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);}
    gtag('js', new Date());
    gtag('config', 'G-92MSJDQS52');
  </script>

  <!-- Security Headers -->
  <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline' https://cdn.tailwindcss.com https://unpkg.com https://www.googletagmanager.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://unpkg.com; font-src 'self' https://fonts.gstatic.com; object-src 'none';">
  <meta http-equiv="X-Content-Type-Options" content="nosniff">
  <meta http-equiv="Referrer-Policy" content="no-referrer-when-downgrade">
  <meta http-equiv="X-Frame-Options" content="DENY">
  <meta http-equiv="Permissions-Policy" content="geolocation=(), microphone=(), camera=()">
</head>

<header class="sticky top-0 z-50 p-6 shadow-md flex flex-wrap items-center bg-pink-600">
  <!-- Logo -->
  <a href="index.html" class="text-3xl font-extrabold tracking-wide flex-shrink-0 mr-auto text-yellow-300">
    GETTWISTED
    <span class="block text-sm md:text-lg text-cyan-300">HAIR STUDIOS</span>
  </a>

  <!-- Desktop Nav -->
  <nav id="navMenu" class="hidden md:flex space-x-4 text-sm md:text-base items-center">
    <a href="index.html" class="hover:underline">Home</a>
    <a href="stylists.html" class="hover:underline">Stylists</a>
    <a href="services.html" class="hover:underline">Services</a>
    <a href="careers.html" class="hover:underline">Careers</a>
    <a href="help.html" class="hover:underline">Help</a>
    <button onclick="toggleTheme()" id="themeToggleBtn" class="action-btn theme-btn" title="Change Theme">🎨</button>
    <button onclick="toggleChat()" class="action-btn chat-btn" title="Chat with Us">💬</button>
    <a href="sms:+16102880343" class="action-btn sms-btn" title="Text Us">📱</a>
  </nav>

  <!-- Mobile Toggle -->
  <button id="mobileMenuBtn" onclick="document.getElementById('mobileMenu').classList.toggle('hidden');" class="md:hidden bg-white text-black px-4 py-2 rounded-full shadow-lg font-bold ml-4 text-xl">
   ☰
  </button>

  <!-- Mobile Dropdown -->
  <div id="mobileMenu" class="w-full mt-4 hidden md:hidden bg-pink-600 py-3 px-2 rounded-lg shadow-lg">
    <nav class="flex flex-wrap justify-center items-center gap-4 text-sm text-white">
      <a href="index.html" class="hover:underline">Home</a>
      <a href="stylists.html" class="hover:underline">Stylists</a>
      <a href="services.html" class="hover:underline">Services</a>
      <a href="careers.html" class="hover:underline">Careers</a>
      <a href="help.html" class="hover:underline">Help</a>
      <div class="flex gap-3 mt-3 sm:mt-0">
        <button onclick="toggleTheme()" class="action-btn mobile-action-btn theme-btn" title="Change Theme">🎨</button>
        <button onclick="toggleChat()" class="action-btn mobile-action-btn chat-btn" title="Chat with Us">💬</button>
        <a href="sms:+16102880343" class="action-btn mobile-action-btn sms-btn" title="Text Us">📱</a>
      </div>
    </nav>
  </div>

</header>

  <!-- Hero Portion -->
  <section class="text-center py-12 bg-yellow-300 text-black">
    <h2 class="text-3xl md:text-5xl font-bold theme-text">Meet Our Stylists</h2>
    <p class="mt-2 text-lg theme-subtext">Natural Hair Experts • Braids • Locs • Love</p>
  </section>

  <!-- Stylist Profiles -->
  <section class="px-6 py-12 max-w-6xl mx-auto grid grid-cols-1 md:grid-cols-3 gap-10" data-aos="fade-up">

    <!-- Stylist 1 - Sheryl Williams -->
    <div class="card rounded-xl overflow-hidden shadow-lg">
      <img src="assets/images/sw.png" alt="Stylist Sheryl Williams" class="w-full h-64 object-cover">
      <div class="p-6">
        <h3 class="text-xl font-bold mb-2 stylist-name">Sheryl Williams</h3>
        <p class="text-sm mb-4">Master Loctician with 10+ years of experience specializing in starter locs, retwists, and loc styling.</p>
        <div class="flex flex-wrap gap-2 mb-4">
          <span class="specialty text-xs px-2 py-1 rounded-full">Locs</span>
          <span class="specialty text-xs px-2 py-1 rounded-full">Natural Hair</span>
          <span class="specialty text-xs px-2 py-1 rounded-full">Color</span>
        </div>
        <a href="https://get-twisted-hair-studio.square.site/" target="_blank" class="book-btn">Book with Sheryl</a>
      </div>
    </div>

    <!-- Stylist 2 - Michael Williams -->
    <div class="card rounded-xl overflow-hidden shadow-lg">
      <img src="assets/images/mw.png" alt="Stylist Michael Williams" class="w-full h-64 object-cover">
      <div class="p-6">
        <h3 class="text-xl font-bold mb-2 stylist-name">Michael Williams</h3>
        <p class="text-sm mb-4">Master Barber and Loctician specializing in fades, tapers, and precision loc maintenance.</p>
        <div class="flex flex-wrap gap-2 mb-4">
          <span class="specialty text-xs px-2 py-1 rounded-full">Barber</span>
          <span class="specialty text-xs px-2 py-1 rounded-full">Locs</span>
          <span class="specialty text-xs px-2 py-1 rounded-full">Fades</span>
        </div>
        <a href="https://get-twisted-hair-studio.square.site/" target="_blank" class="book-btn">Book with Michael</a>
      </div>
    </div>

    <!-- Stylist 3 - Shantell Mack -->
    <div class="card rounded-xl overflow-hidden shadow-lg">
      <img src="assets/images/sm.png" alt="Stylist Shantell Mack" class="w-full h-64 object-cover">
      <div class="p-6">
        <h3 class="text-xl font-bold mb-2 stylist-name">Shantell Mack</h3>
        <p class="text-sm mb-4">Braid Specialist with expertise in box braids, knotless braids, and protective styling.</p>
        <div class="flex flex-wrap gap-2 mb-4">
          <span class="specialty text-xs px-2 py-1 rounded-full">Braids</span>
          <span class="specialty text-xs px-2 py-1 rounded-full">Twists</span>
          <span class="specialty text-xs px-2 py-1 rounded-full">Styling</span>
        </div>
        <a href="https://get-twisted-hair-studio.square.site/" target="_blank" class="book-btn">Book with Shantell</a>
      </div>
    </div>

  </section>

  <!-- Join Our Team CTA -->
  <section class="bg-pink-600 text-white text-center py-16 px-6">
    <div class="max-w-3xl mx-auto">
      <h2 class="text-3xl font-bold mb-4">Join Our Team</h2>
      <p class="mb-6">Are you a talented stylist looking to grow your career? We're always looking for passionate professionals to join our team.</p>
      <a href="careers.html" class="bg-yellow-300 hover:bg-yellow-400 text-black font-bold py-3 px-6 rounded-full shadow-lg transition">View Careers</a>
    </div>
  </section>

<!-- Chatbot -->
<div id="chatbot" class="fixed bottom-28 right-5 bg-white w-80 rounded-xl shadow-lg overflow-hidden text-black z-50 hidden">
  <div class="bg-pink-600 text-white p-4 flex justify-between items-center">
    <h4 class="font-bold text-sm">💬 TwistyBot</h4>
    <button onclick="toggleChat()" class="text-white text-lg font-bold">&times;</button>
  </div>
  <div class="p-4 h-60 overflow-y-auto text-sm space-y-3" id="chatMessages">
    <div class="bg-pink-100 text-black p-3 rounded-lg w-fit max-w-[80%]">Hi! I'm TwistyBot 💖</div>
    <div class="bg-pink-100 text-black p-3 rounded-lg w-fit max-w-[80%]">How can I help you today?</div>
  </div>
  <div class="flex p-2 border-t">
    <input id="chatInput" type="text" placeholder="Type your question..." class="flex-1 text-sm p-2 outline-none" />
    <button id="sendBtn" class="bg-pink-600 text-white px-3 ml-2 rounded">Send</button>
  </div>
</div>

<!-- Footer -->
<footer class="px-4 sm:px-6 py-10 mt-20 text-center md:text-left">
  <div class="max-w-6xl mx-auto px-4 sm:px-6 grid grid-cols-1 md:grid-cols-3 gap-10 text-sm md:text-base text-center md:text-left">

    <!-- Brand Info -->
    <div>
      <h3 class="text-xl font-bold mb-2">GetTwisted Hair Studios</h3>
      <p>Where natural beauty meets expert care.</p>
      <p class="mt-2 italic">"Twist. Slay. Repeat."</p>
    </div>

    <!-- Contact Info with Multiple Locations -->
    <div>
      <h4 class="font-semibold mb-1">Contact Us</h4>
      <p>📍 Teaneck, NJ</p>
      <p>📍 Pottstown, PA</p>
      <p>📞 (*************</p>
      <p>📧 <EMAIL></p>
    </div>

    <!-- Quick Links -->
    <div class="space-y-2">
      <h4 class="font-semibold mb-1">Quick Links</h4>
      <a href="https://get-twisted-hair-studio.square.site/" target="_blank" class="block hover:underline">Book Appointment</a>
      <a href="stylists.html" class="block hover:underline">Meet Our Stylists</a>
      <a href="services.html" class="block hover:underline">Our Services</a>
    </div>
  </div>

  <p class="text-center text-xs footer-copy mt-8">&copy; 2024 GetTwisted Hair Studios. All rights reserved.</p>
</footer>

<!-- Scripts -->
<script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
<script src="js/main.js"></script>
<script>
  AOS.init();

  function toggleChat() {
    const chat = document.getElementById('chatbot');
    chat.classList.toggle('hidden');
    chat.classList.toggle('animate-slide-in');
  }

  function toggleTheme() {
    const themes = ['vibrant', 'black-white', 'matrix'];
    let currentTheme = themes.findIndex(t => document.body.classList.contains(`theme-${t}`));
    document.body.classList.remove(`theme-${themes[currentTheme]}`);
    currentTheme = (currentTheme + 1) % themes.length;
    document.body.classList.add(`theme-${themes[currentTheme]}`);
    localStorage.setItem('selectedTheme', themes[currentTheme]);
  }

  document.addEventListener("DOMContentLoaded", () => {
    const savedTheme = localStorage.getItem('selectedTheme') || 'vibrant';
    document.body.classList.add(`theme-${savedTheme}`);
  });

  // Mobile Menu Toggle is now handled by inline onclick attribute
</script>

<!-- Chatbot Script -->
<script>
  let chatbotKnowledge = [];

  fetch('assets/data/chatbot-knowledge.json')
    .then(response => response.json())
    .then(data => {
      chatbotKnowledge = data;
      console.log("Chatbot knowledge loaded:", chatbotKnowledge);
    })
    .catch(err => console.error("Error loading chatbot knowledge:", err));

  function sanitize(text) {
    return text.toLowerCase().replace(/[^\w\s]/gi, '').trim();
  }

  function handleChatInput(inputText) {
    const cleanInput = sanitize(inputText);

    let match = chatbotKnowledge.find(item => sanitize(item.question) === cleanInput);
    if (!match) {
      match = chatbotKnowledge.find(item => cleanInput.includes(sanitize(item.question)));
    }

    return match
      ? match.answer
      : "I'm here to help, but I don't have that answer yet. You can text us directly at (************* or visit our booking page to learn more.";
  }

  document.addEventListener("DOMContentLoaded", () => {
    const input = document.getElementById('chatInput');
    const sendBtn = document.getElementById('sendBtn');
    const chatBox = document.getElementById('chatMessages');

    function sendMessage() {
      const text = input.value.trim();
      if (!text) return;

      const userBubble = `<div class="bg-yellow-100 text-black p-3 rounded-lg w-fit max-w-[80%] ml-auto">${text}</div>`;
      const botReply = handleChatInput(text);
      const botBubble = `<div class="bg-pink-100 text-black p-3 rounded-lg w-fit max-w-[80%]">${botReply}</div>`;

      chatBox.innerHTML += userBubble + botBubble;
      input.value = '';
      chatBox.scrollTop = chatBox.scrollHeight;
    }

    sendBtn.addEventListener('click', sendMessage);
    input.addEventListener('keydown', e => { if (e.key === 'Enter') sendMessage(); });
  });
</script>

</body>
</html>
