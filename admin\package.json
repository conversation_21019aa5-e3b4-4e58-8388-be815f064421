{"name": "gettwistedlocs-admin", "version": "1.0.0", "description": "Admin portal for GetTwisted Hair Studios", "main": "index.js", "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "keywords": ["hair", "salon", "admin", "dashboard"], "author": "GetTwisted Hair Studios", "license": "ISC", "dependencies": {"axios": "^1.4.0", "chart.js": "^4.3.3", "react": "^18.2.0", "react-chartjs-2": "^5.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.14.2", "react-scripts": "5.0.1"}, "devDependencies": {"@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^14.0.0", "@testing-library/user-event": "^14.4.3", "tailwindcss": "^3.3.3"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}