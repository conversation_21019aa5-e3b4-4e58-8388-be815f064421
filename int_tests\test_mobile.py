"""
Integration tests for mobile responsiveness.
"""
import pytest
import time
from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from conftest import navigate_to_page, wait_for_element, wait_for_element_clickable, check_element_exists

# Define common mobile device dimensions
MOBILE_DIMENSIONS = [
    {"name": "iPhone X", "width": 375, "height": 812},
    {"name": "Galaxy S9", "width": 360, "height": 740},
    {"name": "iPhone SE", "width": 320, "height": 568},
    {"name": "iPad", "width": 768, "height": 1024},
]

def test_mobile_menu_toggle(driver, wait):
    """Test that the mobile menu toggle works correctly on all pages."""
    # Test just the index page for now
    pages = ["index.html"]

    # Set window size to mobile dimensions
    driver.set_window_size(375, 812)  # iPhone X dimensions

    for page in pages:
        navigate_to_page(driver, page)

        # Check that the mobile menu toggle is displayed
        toggle = wait_for_element(driver, By.ID, "mobileMenuBtn")
        assert toggle.is_displayed(), f"Mobile menu toggle not displayed on {page}"

        # Check that the desktop menu is hidden
        desktop_menu = driver.find_element(By.ID, "navMenu")
        assert not desktop_menu.is_displayed(), f"Desktop menu not hidden on mobile view on {page}"

        # Get the mobile menu element
        mobile_menu = driver.find_element(By.ID, "mobileMenu")

        # Check that the mobile menu is initially hidden
        initial_classes = mobile_menu.get_attribute("class")
        assert "hidden" in initial_classes, f"Mobile menu should be hidden initially on {page}"

        # Check if the mobile menu is visible
        initial_display = driver.execute_script("return window.getComputedStyle(document.getElementById('mobileMenu')).display")
        assert initial_display == "none", f"Mobile menu should not be visible initially on {page}"

        # Use JavaScript to make the mobile menu visible
        driver.execute_script("document.getElementById('mobileMenu').style.display = 'block';")
        driver.execute_script("document.getElementById('mobileMenu').classList.remove('hidden');")
        time.sleep(1)  # Allow menu to display

        # Check if the mobile menu is now visible
        display_after_show = driver.execute_script("return window.getComputedStyle(document.getElementById('mobileMenu')).display")
        assert display_after_show != "none", f"Mobile menu should be visible after showing on {page}"

        # Check that all navigation links are present in the mobile menu
        mobile_links = mobile_menu.find_elements(By.TAG_NAME, "a")
        assert len(mobile_links) >= 6, f"Not all navigation links present in mobile menu on {page}"

        # Use JavaScript to hide the mobile menu again
        driver.execute_script("document.getElementById('mobileMenu').style.display = 'none';")
        driver.execute_script("document.getElementById('mobileMenu').classList.add('hidden');")
        time.sleep(1)  # Allow menu to hide

        # Check if the mobile menu is hidden again
        display_after_hide = driver.execute_script("return window.getComputedStyle(document.getElementById('mobileMenu')).display")
        assert display_after_hide == "none", f"Mobile menu should be hidden after hiding on {page}"

    # Reset window size
    driver.maximize_window()

@pytest.mark.parametrize("device", MOBILE_DIMENSIONS)
def test_responsive_layout(driver, wait, device):
    """Test that the layout is responsive on different mobile devices."""
    # Set window size to the device dimensions
    driver.set_window_size(device["width"], device["height"])

    navigate_to_page(driver, "index.html")

    # Check that the mobile menu toggle is displayed
    toggle = wait_for_element(driver, By.ID, "mobileMenuToggle")
    assert toggle.is_displayed(), f"Mobile menu toggle not displayed on {device['name']}"

    # Check that the content fits within the viewport
    body = driver.find_element(By.TAG_NAME, "body")
    body_width = body.size["width"]

    # The body width should be less than or equal to the device width
    assert body_width <= device["width"], f"Content width ({body_width}) exceeds device width ({device['width']}) on {device['name']}"

    # Check that there is no horizontal scrolling
    html = driver.find_element(By.TAG_NAME, "html")
    scroll_width = driver.execute_script("return arguments[0].scrollWidth", html)
    client_width = driver.execute_script("return arguments[0].clientWidth", html)

    # The scroll width should be less than or equal to the client width (plus a small margin for rounding errors)
    assert scroll_width <= client_width + 5, f"Horizontal scrolling detected on {device['name']}"

    # Reset window size
    driver.maximize_window()

def test_gallery_responsive(driver, wait):
    """Test that the gallery page is responsive on mobile devices."""
    # Set window size to mobile dimensions
    driver.set_window_size(375, 812)  # iPhone X dimensions

    navigate_to_page(driver, "gallery.html")

    # Check that the gallery grid is displayed
    gallery_grid = wait_for_element(driver, By.ID, "gallery-grid")
    assert gallery_grid.is_displayed(), "Gallery grid not displayed on mobile view"

    # Check that the filter buttons are displayed
    filter_buttons = driver.find_elements(By.CSS_SELECTOR, ".filter-btn")
    assert len(filter_buttons) > 0, "Filter buttons not found on mobile view"
    assert filter_buttons[0].is_displayed(), "Filter buttons not displayed on mobile view"

    # Check that the gallery items are displayed in a single column
    gallery_items = driver.find_elements(By.CSS_SELECTOR, ".gallery-item")
    if len(gallery_items) > 0:
        first_item = gallery_items[0]
        item_width = first_item.size["width"]

        # The item width should be close to the device width (minus padding)
        assert item_width > 300, f"Gallery item width ({item_width}) is too small on mobile view"

    # Reset window size
    driver.maximize_window()

def test_services_responsive(driver, wait):
    """Test that the services page is responsive on mobile devices."""
    # Set window size to mobile dimensions
    driver.set_window_size(375, 812)  # iPhone X dimensions

    navigate_to_page(driver, "services.html")

    # Check that the category tabs are displayed
    category_tabs = driver.find_elements(By.CSS_SELECTOR, "#category-tabs button")
    assert len(category_tabs) > 0, "Category tabs not found on mobile view"
    assert category_tabs[0].is_displayed(), "Category tabs not displayed on mobile view"

    # Click on the first category tab
    category_tabs[0].click()
    time.sleep(1)  # Allow content to display

    # Check that the service cards are displayed
    service_cards = driver.find_elements(By.CSS_SELECTOR, ".bg-white")
    if len(service_cards) > 0:
        first_card = service_cards[0]
        assert first_card.is_displayed(), "Service cards not displayed on mobile view"

        # Check that the service cards are displayed in a single column
        card_width = first_card.size["width"]

        # The card width should be close to the device width (minus padding)
        assert card_width > 300, f"Service card width ({card_width}) is too small on mobile view"

    # Reset window size
    driver.maximize_window()

def test_form_responsive(driver, wait):
    """Test that forms are responsive on mobile devices."""
    # Set window size to mobile dimensions
    driver.set_window_size(375, 812)  # iPhone X dimensions

    # Check the contact form on the help page
    navigate_to_page(driver, "help.html")

    # Look for a contact form
    contact_forms = driver.find_elements(By.CSS_SELECTOR, "form, #contactForm")

    if len(contact_forms) > 0:
        contact_form = contact_forms[0]
        assert contact_form.is_displayed(), "Contact form not displayed on mobile view"

        # Check that the form fields are displayed in a single column
        form_fields = contact_form.find_elements(By.CSS_SELECTOR, "input, textarea, select")

        if len(form_fields) > 0:
            first_field = form_fields[0]
            field_width = first_field.size["width"]

            # The field width should be close to the form width
            form_width = contact_form.size["width"]
            assert field_width > form_width * 0.8, f"Form field width ({field_width}) is too small compared to form width ({form_width}) on mobile view"

    # Reset window size
    driver.maximize_window()
