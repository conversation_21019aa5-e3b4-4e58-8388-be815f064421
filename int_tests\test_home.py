"""
Integration tests for the home page.
"""
import pytest
import time
from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from conftest import navigate_to_page, wait_for_element, wait_for_element_clickable, check_element_exists

def test_home_page_loads(driver, wait):
    """Test that the home page loads successfully."""
    navigate_to_page(driver, "index.html")
    
    # Check that the page title is correct
    assert "GetTwisted Hair Studios" in driver.title
    
    # Check that the hero section is displayed
    hero = wait_for_element(driver, By.CSS_SELECTOR, ".hero-section")
    assert hero.is_displayed()
    
    # Check that the logo is displayed
    logo = wait_for_element(driver, By.CSS_SELECTOR, "header a.text-3xl")
    assert "GETTWISTED" in logo.text
    
    # Check that the navigation menu is present
    nav = wait_for_element(driver, By.ID, "navMenu")
    assert nav.is_displayed()

def test_navigation_links(driver, wait):
    """Test that the navigation links work correctly."""
    navigate_to_page(driver, "index.html")
    
    # Check that all navigation links are present
    nav_links = driver.find_elements(By.CSS_SELECTOR, "#navMenu a")
    assert len(nav_links) >= 6  # Home, Stylists, Services, Gallery, Careers, Help
    
    # Check specific links
    link_texts = [link.text for link in nav_links]
    assert "Home" in link_texts
    assert "Stylists" in link_texts
    assert "Services" in link_texts
    assert "Gallery" in link_texts
    assert "Careers" in link_texts
    assert "Help" in link_texts

def test_theme_toggle(driver, wait):
    """Test that the theme toggle button works correctly."""
    navigate_to_page(driver, "index.html")
    
    # Get the initial theme
    body = driver.find_element(By.TAG_NAME, "body")
    initial_theme = body.get_attribute("class")
    
    # Click the theme toggle button
    theme_button = wait_for_element_clickable(driver, By.ID, "themeToggleBtn")
    theme_button.click()
    time.sleep(1)  # Allow theme to change
    
    # Check that the theme has changed
    new_theme = body.get_attribute("class")
    assert initial_theme != new_theme
    
    # Click the theme toggle button again
    theme_button.click()
    time.sleep(1)  # Allow theme to change
    
    # Check that the theme has changed back
    final_theme = body.get_attribute("class")
    assert final_theme != new_theme

def test_mobile_menu(driver, wait):
    """Test that the mobile menu works correctly."""
    # Set window size to mobile dimensions
    driver.set_window_size(375, 812)  # iPhone X dimensions
    
    navigate_to_page(driver, "index.html")
    
    # Check that the mobile menu toggle is displayed
    toggle = wait_for_element(driver, By.ID, "mobileMenuToggle")
    assert toggle.is_displayed()
    
    # Check that the desktop menu is hidden
    desktop_menu = driver.find_element(By.ID, "navMenu")
    assert not desktop_menu.is_displayed()
    
    # Click the mobile menu toggle
    toggle.click()
    time.sleep(1)  # Allow menu to open
    
    # Check that the mobile menu is displayed
    mobile_menu = wait_for_element(driver, By.ID, "mobileMenu")
    assert "hidden" not in mobile_menu.get_attribute("class")
    
    # Check that all navigation links are present in the mobile menu
    mobile_links = mobile_menu.find_elements(By.TAG_NAME, "a")
    assert len(mobile_links) >= 6  # Home, Stylists, Services, Gallery, Careers, Help
    
    # Click the close button
    close_button = wait_for_element_clickable(driver, By.ID, "closeMenu")
    close_button.click()
    time.sleep(1)  # Allow menu to close
    
    # Check that the mobile menu is hidden
    assert "hidden" in mobile_menu.get_attribute("class")
    
    # Reset window size
    driver.maximize_window()

def test_book_appointment_button(driver, wait):
    """Test that the book appointment button is present and clickable."""
    navigate_to_page(driver, "index.html")
    
    # Find all book appointment buttons (there might be multiple)
    book_buttons = driver.find_elements(By.XPATH, "//a[contains(text(), 'Book Appointment')]")
    
    # Check that at least one book appointment button exists
    assert len(book_buttons) > 0
    
    # Check that the first button is displayed and has the correct href
    book_button = book_buttons[0]
    assert book_button.is_displayed()
    assert "square.site" in book_button.get_attribute("href")

def test_footer_content(driver, wait):
    """Test that the footer content is correct."""
    navigate_to_page(driver, "index.html")
    
    # Check that the footer is displayed
    footer = wait_for_element(driver, By.TAG_NAME, "footer")
    assert footer.is_displayed()
    
    # Check that the footer contains the studio name
    assert "GetTwisted Hair Studios" in footer.text
    
    # Check that the footer contains contact information
    assert "Contact Us" in footer.text
    assert "Teaneck, NJ" in footer.text
    assert "Pottstown, PA" in footer.text
    
    # Check that the footer contains quick links
    assert "Quick Links" in footer.text
    
    # Check that the footer contains the copyright notice
    assert "© 2024" in footer.text
