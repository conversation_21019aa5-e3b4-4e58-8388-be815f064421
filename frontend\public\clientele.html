<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Help & Support – GetTwisted</title>

  <!-- Tailwind + Font + AOS -->
  <script src="https://cdn.tailwindcss.com"></script>
  <link href="https://fonts.googleapis.com/css2?family=Fredoka+One&display=swap" rel="stylesheet">
  <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">

  <style>
    body { font-family: 'Fredoka One', cursive; }

    @keyframes slideIn {
      0% { transform: translateY(100%); opacity: 0; }
      100% { transform: translateY(0); opacity: 1; }
    }
    .animate-slide-in { animation: slideIn 0.3s ease-out forwards; }

    /* Theme Styles */
    body.theme-vibrant { background-color: #f472b6; color: white; }
    body.theme-black-white { background-color: #000; color: #fff; }
    body.theme-matrix { background-color: #000; color: #00ff00; }
    body.theme-black-white header, body.theme-black-white section {
      background-color: #000 !important; color: #fff !important;
    }
    body.theme-matrix header, body.theme-matrix section {
      background-color: #000 !important; color: #00ff00 !important;
    }
    body.theme-vibrant header, body.theme-vibrant section {
      background-color: #ec4899 !important; color: white !important;
    }
    body.theme-black-white .footer-copy,
    body.theme-matrix .footer-copy { color: #aaa; }
    body.theme-vibrant .footer-copy { color: #f9a8d4; }
  </style>

  <!-- Security Headers -->
  <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline' https://cdn.tailwindcss.com https://unpkg.com https://www.googletagmanager.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://unpkg.com; font-src 'self' https://fonts.gstatic.com; frame-src https://*.square.site/ https://*.squareup.com/; connect-src https://*.square.site/ https://*.squareup.com/; img-src 'self' https://*.square.site/ https://*.squareup.com/ data:; object-src 'none';">
  <meta http-equiv="X-Content-Type-Options" content="nosniff">
  <meta http-equiv="Referrer-Policy" content="no-referrer-when-downgrade">
  <!-- Removed X-Frame-Options to allow iframe embedding -->
  <meta http-equiv="Permissions-Policy" content="geolocation=(), microphone=(), camera=()">
</head>

<body class="theme-black-white text-white">

  <header class="sticky top-0 z-50 bg-pink-600 shadow-md p-6">
    <div class="flex flex-col md:flex-row md:justify-between md:items-center gap-4">

      <!-- Logo / Title -->
      <div class="text-center md:text-left w-full md:w-auto leading-tight">
        <h1 class="text-5xl md:text-6xl font-extrabold text-yellow-300 tracking-wide">GETTWISTED</h1>
        <span class="block text-xl md:text-2xl text-cyan-300 tracking-wider">HAIR STUDIOS</span>
      </div>

      <!-- Nav Menu -->
      <nav class="flex flex-wrap justify-center md:justify-end items-center space-x-2 md:space-x-6 text-sm md:text-lg">
        <a href="index.html" class="hover:underline hover:text-white">Home</a>
        <a href="stylists.html" class="hover:underline hover:text-white">Stylists</a>
        <a href="shop.html" class="hover:underline hover:text-white">Shop</a>
        <a href="help.html" class="hover:underline hover:text-white">Help</a>

        <!-- Theme Toggle -->
        <button onclick="toggleTheme()" id="themeToggleBtn" class="bg-white text-black px-3 py-1 rounded-full shadow hover:bg-gray-100 font-bold text-xs md:text-sm transition">
          🎨
        </button>

        <!-- Contact Buttons -->
        <button onclick="toggleChat()" class="bg-yellow-300 hover:bg-yellow-400 text-black font-bold py-2 px-4 rounded-full shadow-lg">
          💬
        </button>
        <a href="sms:+16102880343" class="bg-pink-500 hover:bg-pink-600 text-white font-bold py-2 px-4 rounded-full shadow-lg">
          📱
        </a>
      </nav>
    </div>
  </header>

  <!-- Help Hero -->
  <section class="text-center py-16 bg-yellow-300 text-black" data-aos="fade-down">
    <h2 class="text-4xl md:text-5xl font-bold mb-4">Help & Support</h2>
    <p class="text-lg max-w-xl mx-auto">Got questions? We’re here to help with appointments, products, and everything hair care!</p>
  </section>

  <!-- Help Info Cards -->
  <section class="p-6 grid grid-cols-1 md:grid-cols-2 gap-6 max-w-6xl mx-auto">
    <div class="bg-white text-black rounded-xl p-6 shadow" data-aos="fade-up">
      <h3 class="text-pink-600 text-xl mb-2">Booking Help</h3>
      <p>Click the 'Book Appointment' button on the homepage or reach out via chat for assistance.</p>
    </div>
    <div class="bg-white text-black rounded-xl p-6 shadow" data-aos="fade-up" data-aos-delay="100">
      <h3 class="text-pink-600 text-xl mb-2">Product Support</h3>
      <p>Need help choosing a product? Browse the Shop or email us for personalized recommendations.</p>
    </div>
    <div class="bg-white text-black rounded-xl p-6 shadow" data-aos="fade-up" data-aos-delay="200">
      <h3 class="text-pink-600 text-xl mb-2">Live Chat</h3>
      <p>Use the yellow floating chat bubble for real-time support (TwistyBot ready to help!).</p>
    </div>
    <div class="bg-white text-black rounded-xl p-6 shadow" data-aos="fade-up" data-aos-delay="300">
      <h3 class="text-pink-600 text-xl mb-2">Salon Locations</h3>
      <p>We’re currently available in Philadelphia, PA — more cities coming soon!</p>
    </div>
  </section>

  <!-- Chatbot -->
  <div id="chatbot" class="fixed bottom-28 right-5 bg-white w-80 rounded-xl shadow-lg overflow-hidden text-black z-50 hidden">
    <div class="bg-pink-600 text-white p-4 flex justify-between items-center">
      <h4 class="font-bold text-sm">💬 TwistyBot</h4>
      <button onclick="toggleChat()" class="text-white text-lg font-bold">&times;</button>
    </div>
    <div class="p-4 h-60 overflow-y-auto text-sm space-y-3">
      <div class="bg-pink-100 text-black p-3 rounded-lg w-fit max-w-[80%]">Hi! I’m TwistyBot 💖</div>
      <div class="bg-pink-100 text-black p-3 rounded-lg w-fit max-w-[80%]">How can I help you today?</div>
    </div>
    <div class="flex p-2 border-t">
      <input type="text" placeholder="Type your question..." class="flex-1 text-sm p-2 outline-none" />
      <button class="bg-pink-600 text-white px-3 ml-2 rounded">Send</button>
    </div>
  </div>

  <!-- Booking Modal -->
  <div id="bookingModal" class="fixed inset-0 z-50 hidden bg-black bg-opacity-60 flex items-center justify-center">
    <div class="bg-white rounded-xl max-w-3xl w-full mx-4 md:mx-0 shadow-lg relative">
      <div class="flex justify-between items-center px-6 py-4 bg-pink-600 text-white rounded-t-xl">
        <h3 class="text-lg font-bold">Book Your Appointment</h3>
        <button onclick="toggleBookingModal()" class="text-2xl leading-none hover:text-yellow-300">&times;</button>
      </div>
      <div class="p-4">
        <iframe src="https://get-twisted-hair-studio.square.site/" width="100%" height="600" frameborder="0" class="w-full rounded-md shadow-md"></iframe>
      </div>
    </div>
  </div>

  <!-- Footer -->
  <footer class="footer px-6 py-10 mt-20">
    <div class="max-w-6xl mx-auto grid grid-cols-1 md:grid-cols-3 gap-10 text-sm md:text-base">
      <div>
        <h3 class="text-xl font-bold mb-2">GetTwisted Hair Studios</h3>
        <p>Where natural beauty meets expert care.</p>
        <p class="mt-2 italic">"Twist. Slay. Repeat."</p>
      </div>
      <div>
        <h4 class="font-semibold mb-1">Contact Us</h4>
        <p>📍 Philadelphia, PA</p>
        <p>📞 (610) 288-0343</p>
        <p>📧 <EMAIL></p>
      </div>
      <div class="space-y-2">
        <h4 class="font-semibold mb-1">Quick Links</h4>
        <a href="https://get-twisted-hair-studio.square.site/" target="_blank" class="block hover:underline">Book Appointment</a>
        <a href="stylists.html" class="block hover:underline">Meet Our Stylists</a>
        <a href="shop.html" class="block hover:underline">Shop Products</a>
        <div class="mt-4 flex flex-wrap gap-2">
          <button onclick="toggleChat()" class="bg-yellow-300 hover:bg-yellow-400 text-black font-bold py-2 px-4 rounded-full shadow-lg">
            💬 Chat
          </button>
          <a href="sms:+16102880343" class="bg-pink-500 hover:bg-pink-600 text-white font-bold py-2 px-4 rounded-full shadow-lg">
            📱 Text Us
          </a>
        </div>
      </div>
    </div>
    <p class="text-center text-xs footer-copy mt-8">&copy; 2024 GetTwisted Hair Studios. All rights reserved.</p>
  </footer>

  <!-- Scripts -->
  <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
  <script>
    AOS.init();

    function toggleChat() {
      const chat = document.getElementById('chatbot');
      chat.classList.toggle('hidden');
      chat.classList.toggle('animate-slide-in');
    }

    function toggleBookingModal() {
      const modal = document.getElementById('bookingModal');
      modal.classList.toggle('hidden');
    }

    const themes = ['vibrant', 'black-white', 'matrix'];
    let currentTheme = 1;

    function toggleTheme() {
      const body = document.body;
      themes.forEach(t => body.classList.remove(`theme-${t}`));
      currentTheme = (currentTheme + 1) % themes.length;
      const selected = themes[currentTheme];
      body.classList.add(`theme-${selected}`);
      document.getElementById('themeToggleBtn').innerText = `🎨 Theme: ${selected.replace('-', ' ').toUpperCase()}`;
      localStorage.setItem('selectedTheme', selected);
    }

    document.addEventListener("DOMContentLoaded", () => {
      const savedTheme = localStorage.getItem('selectedTheme') || 'black-white';
      currentTheme = themes.indexOf(savedTheme);
      document.body.classList.add(`theme-${savedTheme}`);
      document.getElementById('themeToggleBtn').innerText = `🎨 Theme: ${savedTheme.replace('-', ' ').toUpperCase()}`;
    });
  </script>
</body>
</html>
