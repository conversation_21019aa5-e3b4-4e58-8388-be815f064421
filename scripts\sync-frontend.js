/**
 * Sync Frontend Script for GetTwisted Hair Studios
 * 
 * This script:
 * 1. Copies all files from frontend/public to the root directory
 * 2. Ensures all HTML files have security headers
 * 3. Logs all actions for verification
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Paths
const sourceDir = path.join(__dirname, '..', 'frontend', 'public');
const targetDir = path.join(__dirname, '..');

// Security headers that should be in every HTML file
const SECURITY_HEADERS = `
  <!-- Security Headers -->
  <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline' https://cdn.tailwindcss.com https://unpkg.com https://www.googletagmanager.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://unpkg.com; font-src 'self' https://fonts.gstatic.com; object-src 'none';">
  <meta http-equiv="X-Content-Type-Options" content="nosniff">
  <meta http-equiv="Referrer-Policy" content="no-referrer-when-downgrade">
  <meta http-equiv="X-Frame-Options" content="DENY">
  <meta http-equiv="Permissions-Policy" content="geolocation=(), microphone=(), camera=()">
`;

// Function to ensure HTML files have security headers
function ensureSecurityHeaders(filePath) {
  let content = fs.readFileSync(filePath, 'utf8');
  
  // Check if security headers already exist
  if (!content.includes('Content-Security-Policy')) {
    // Find the position after the opening head tag
    const headPos = content.indexOf('</head>');
    if (headPos !== -1) {
      // Insert security headers before closing head tag
      content = content.slice(0, headPos) + SECURITY_HEADERS + content.slice(headPos);
      fs.writeFileSync(filePath, content);
      console.log(`✅ Added security headers to ${filePath}`);
    } else {
      console.warn(`⚠️ Could not find </head> in ${filePath}`);
    }
  } else {
    console.log(`✓ Security headers already present in ${filePath}`);
  }
}

// Function to copy a file from source to target
function copyFile(source, target) {
  try {
    fs.copyFileSync(source, target);
    console.log(`✅ Copied: ${path.relative(sourceDir, source)} -> ${path.relative(targetDir, target)}`);
    
    // If it's an HTML file, ensure it has security headers
    if (path.extname(target).toLowerCase() === '.html') {
      ensureSecurityHeaders(target);
    }
    
    return true;
  } catch (error) {
    console.error(`❌ Error copying ${source} to ${target}:`, error);
    return false;
  }
}

// Function to copy a directory recursively
function copyDir(source, target) {
  // Create target directory if it doesn't exist
  if (!fs.existsSync(target)) {
    fs.mkdirSync(target, { recursive: true });
    console.log(`📁 Created directory: ${path.relative(targetDir, target)}`);
  }
  
  // Get all files and directories in the source directory
  const entries = fs.readdirSync(source, { withFileTypes: true });
  
  // Process each entry
  for (const entry of entries) {
    const sourcePath = path.join(source, entry.name);
    const targetPath = path.join(target, entry.name);
    
    if (entry.isDirectory()) {
      // Recursively copy directory
      copyDir(sourcePath, targetPath);
    } else {
      // Copy file
      copyFile(sourcePath, targetPath);
    }
  }
}

// Main function
function main() {
  console.log('🔄 Syncing frontend/public to root directory...');
  
  // Copy all files from frontend/public to root
  copyDir(sourceDir, targetDir);
  
  console.log('✅ Sync completed');
}

main();
