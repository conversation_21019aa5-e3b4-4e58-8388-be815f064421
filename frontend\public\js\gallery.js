/**
 * Gallery JavaScript for GetTwisted Hair Studios
 * This script handles the gallery functionality including:
 * - Loading gallery images
 * - Filtering by category
 * - Lightbox functionality
 */

// Gallery data - using existing images from the website
const galleryItems = [
  {
    id: 1,
    title: "Goddess Locs",
    description: "Long, flowing goddess locs with gold accessories",
    category: "locs",
    thumbnail: "assets/images/rs1.jpg",
    fullImage: "assets/images/rs1.jpg",
    stylist: "<PERSON><PERSON>"
  },
  {
    id: 2,
    title: "Box Braids",
    description: "Medium-sized box braids with beads",
    category: "braids",
    thumbnail: "assets/images/rs3.jpg",
    fullImage: "assets/images/rs3.jpg",
    stylist: "<PERSON>"
  },
  {
    id: 3,
    title: "Fade with Design",
    description: "Clean fade with custom line design",
    category: "cuts barber",
    thumbnail: "assets/images/rs4.jpg",
    fullImage: "assets/images/rs4.jpg",
    stylist: "<PERSON>"
  },
  {
    id: 4,
    title: "Tapered Cut & Loc",
    description: "Tapered cut with maintained locs",
    category: "locs cuts",
    thumbnail: "assets/images/rs5.jpg",
    fullImage: "assets/images/rs5.jpg",
    stylist: "<PERSON>"
  },
  {
    id: 5,
    title: "Flat Twists",
    description: "Elegant flat twists with natural hair",
    category: "natural braids",
    thumbnail: "assets/images/rs7.jpg",
    fullImage: "assets/images/rs7.jpg",
    stylist: "Sasha"
  },
  {
    id: 6,
    title: "Loc Retwist & Style",
    description: "Freshly retwisted and styled locs",
    category: "locs",
    thumbnail: "assets/images/rs9.jpg",
    fullImage: "assets/images/rs9.jpg",
    stylist: "Sheryl"
  },
  {
    id: 7,
    title: "Natural Curls",
    description: "Defined natural curls with shine",
    category: "natural",
    thumbnail: "assets/images/rs2.jpg",
    fullImage: "assets/images/rs2.jpg",
    stylist: "Sasha"
  },
  {
    id: 8,
    title: "Starter Locs",
    description: "Fresh starter locs with neat parts",
    category: "locs",
    thumbnail: "assets/images/rs6.jpg",
    fullImage: "assets/images/rs6.jpg",
    stylist: "Sheryl"
  },
  {
    id: 9,
    title: "Braided Updo",
    description: "Elegant braided updo for special occasions",
    category: "braids",
    thumbnail: "assets/images/rs8.jpg",
    fullImage: "assets/images/rs8.jpg",
    stylist: "Shantell"
  },
  {
    id: 10,
    title: "Precision Cut",
    description: "Clean precision cut with shape up",
    category: "cuts barber",
    thumbnail: "assets/images/rs10.jpg",
    fullImage: "assets/images/rs10.jpg",
    stylist: "Marcus"
  },
  {
    id: 11,
    title: "Curved Cornrows",
    description: "Fresh curved cornrows.",
    category: "braids",
    thumbnail: "assets/images/gallery/sm_corn_rows.jpg",
    fullImage: "assets/images/gallery/sm_corn_rows.jpg",
    stylist: "Sasha"
  },
  {
    id: 12,
    title: "Curved Cornrows",
    description: "Twists done right by Sasha.",
    category: "locs",
    thumbnail: "assets/images/gallery/sm_twists.jpg",
    fullImage: "assets/images/gallery/sm_twists.jpg",
    stylist: "Sasha"
  },
  {
    id: 13,
    title: "Curved Cornrows",
    description: "Elegant retwist with a style.",
    category: "locs",
    thumbnail: "assets/images/gallery/sm_retwist.jpg",
    fullImage: "assets/images/gallery/sm_retwist.jpg",
    stylist: "Sasha"
  },
  {
    id: 14,
    title: "Curved Cornrows",
    description: "Retwist with a style.",
    category: "locs",
    thumbnail: "assets/images/gallery/sm_retwist_01.jpg",
    fullImage: "assets/images/gallery/sm_retwist_01.jpg",
    stylist: "Sasha"
  }
];

// DOM elements
const galleryGrid = document.getElementById('gallery-grid');
const gallerySlider = document.getElementById('gallery-slider');
const sliderTrack = document.getElementById('slider-track');
const sliderPrev = document.getElementById('slider-prev');
const sliderNext = document.getElementById('slider-next');
const sliderIndicators = document.getElementById('slider-indicators');
const filterButtons = document.querySelectorAll('.filter-btn');
const stylistButtons = document.querySelectorAll('.stylist-btn');
const lightbox = document.getElementById('gallery-lightbox');
const lightboxImage = document.getElementById('lightbox-image');
const lightboxTitle = document.getElementById('lightbox-title');
const lightboxDescription = document.getElementById('lightbox-description');
const closeLightbox = document.getElementById('close-lightbox');
const prevImage = document.getElementById('prev-image');
const nextImage = document.getElementById('next-image');

// Current state - expose to global scope for direct access from HTML
window.currentStyleFilter = 'all';
window.currentStylistFilter = 'all';
window.currentImageIndex = 0;
window.currentSlide = 0;
window.slidesPerView = 4; // Default, will be updated based on screen size
window.filteredItems = [...galleryItems];

// Local references for convenience
let currentStyleFilter = window.currentStyleFilter;
let currentStylistFilter = window.currentStylistFilter;
let currentImageIndex = window.currentImageIndex;
let currentSlide = window.currentSlide;
let slidesPerView = window.slidesPerView;
let filteredItems = window.filteredItems;

// Initialize gallery
document.addEventListener('DOMContentLoaded', () => {
  // Ensure lightbox is completely closed on page load
  const lightboxElement = document.getElementById('gallery-lightbox');
  if (lightboxElement) {
    lightboxElement.classList.add('hidden');
    lightboxElement.style.display = 'none';
    lightboxElement.style.zIndex = '-1';
    lightboxElement.style.visibility = 'hidden';
    lightboxElement.style.opacity = '0';
    lightboxElement.style.pointerEvents = 'none';
  }

  // Make sure filter buttons are clickable
  const galleryFilters = document.getElementById('gallery-filters');
  const stylistFilters = document.getElementById('stylist-filters');

  if (galleryFilters) {
    galleryFilters.style.pointerEvents = 'auto';
    galleryFilters.style.zIndex = '30';
  }

  if (stylistFilters) {
    stylistFilters.style.pointerEvents = 'auto';
    stylistFilters.style.zIndex = '30';
  }

  // Update slides per view based on screen size
  updateSlidesPerView();

  // Load gallery items
  loadGalleryItems();

  // Initialize slider
  initSlider();

  // Set up filter buttons
  setupFilters();

  // Set up lightbox
  setupLightbox();

  // Handle window resize for responsive slider
  window.addEventListener('resize', () => {
    updateSlidesPerView();
    updateSlider();
  });
});

// Update slides per view based on screen width
function updateSlidesPerView() {
  if (window.innerWidth < 640) {
    slidesPerView = 1; // Mobile
  } else if (window.innerWidth < 768) {
    slidesPerView = 2; // Small tablets
  } else if (window.innerWidth < 1024) {
    slidesPerView = 3; // Tablets
  } else {
    slidesPerView = 4; // Desktop
  }
}

// Load gallery items based on current filters
function loadGalleryItems() {
  // Clear the gallery grid
  galleryGrid.innerHTML = '';

  // Filter items based on both style and stylist filters
  filteredItems = galleryItems.filter(item => {
    const matchesStyle = currentStyleFilter === 'all' || item.category.includes(currentStyleFilter);
    const matchesStylist = currentStylistFilter === 'all' || item.stylist === currentStylistFilter;
    return matchesStyle && matchesStylist;
  });

  // Create and append gallery items
  filteredItems.forEach((item, index) => {
    const galleryItem = document.createElement('div');
    galleryItem.className = 'gallery-item relative group overflow-hidden rounded-lg shadow-md cursor-pointer';
    galleryItem.setAttribute('data-category', item.category);
    galleryItem.setAttribute('data-stylist', item.stylist);
    galleryItem.setAttribute('data-index', index);

    galleryItem.innerHTML = `
      <img src="${item.thumbnail}" alt="${item.title}" class="w-full h-64 object-cover transition-transform duration-300 group-hover:scale-110">
      <div class="absolute inset-0 bg-black bg-opacity-60 opacity-0 group-hover:opacity-100 transition duration-300 flex flex-col justify-end p-4 text-white">
        <h3 class="font-bold text-lg">${item.title}</h3>
        <p class="text-sm">${item.description}</p>
        <p class="text-xs mt-1">Stylist: ${item.stylist}</p>
      </div>
    `;

    // Add click event to open lightbox
    galleryItem.addEventListener('click', () => {
      openLightbox(index);
    });

    galleryGrid.appendChild(galleryItem);
  });

  // Show "no results" message if no items match the filter
  if (filteredItems.length === 0) {
    const noResults = document.createElement('div');
    noResults.className = 'col-span-full text-center py-12';
    noResults.innerHTML = `
      <p class="text-xl">No styles found with the selected filters.</p>
      <button class="mt-4 bg-pink-600 text-white px-6 py-2 rounded-full hover:bg-pink-700 transition" onclick="resetFilters()">Reset All Filters</button>
    `;
    galleryGrid.appendChild(noResults);
  }
}

// Set up filter buttons
function setupFilters() {
  // Style filter buttons
  filterButtons.forEach(button => {
    // Remove any existing event listeners to prevent duplicates
    button.removeEventListener('click', handleFilterButtonClick);

    // Add new event listener
    button.addEventListener('click', handleFilterButtonClick);
  });

  // Stylist filter buttons
  stylistButtons.forEach(button => {
    // Remove any existing event listeners to prevent duplicates
    button.removeEventListener('click', handleStylistButtonClick);

    // Add new event listener
    button.addEventListener('click', handleStylistButtonClick);
  });
}

// Handler for filter button clicks
function handleFilterButtonClick(event) {
  // Get the button that was clicked
  const button = event.currentTarget;

  // Check if lightbox is open - don't process clicks if it is
  if (lightbox && !lightbox.classList.contains('hidden')) {
    return;
  }

  // Remove active class from all style filter buttons
  filterButtons.forEach(btn => {
    btn.classList.remove('active', 'gallery-filter-btn-active');
    btn.classList.add('gallery-filter-btn');
  });

  // Add active class to clicked button
  button.classList.add('active', 'gallery-filter-btn-active');
  button.classList.remove('gallery-filter-btn');

  // Update current style filter
  currentStyleFilter = button.getAttribute('data-filter');

  // Reload gallery items
  loadGalleryItems();
}

// Handler for stylist button clicks
function handleStylistButtonClick(event) {
  // Get the button that was clicked
  const button = event.currentTarget;

  // Check if lightbox is open - don't process clicks if it is
  if (lightbox && !lightbox.classList.contains('hidden')) {
    return;
  }

  // Remove active class from all stylist filter buttons
  stylistButtons.forEach(btn => {
    btn.classList.remove('active', 'gallery-stylist-btn-active');
    btn.classList.add('gallery-stylist-btn');
  });

  // Add active class to clicked button
  button.classList.add('active', 'gallery-stylist-btn-active');
  button.classList.remove('gallery-stylist-btn');

  // Update current stylist filter
  currentStylistFilter = button.getAttribute('data-stylist');

  // Reload gallery items
  loadGalleryItems();
}

// Direct filter functions that can be called from HTML
function filterByStyle(style) {
  console.log("Filter by style clicked:", style);

  // Force close lightbox if it's open
  if (lightbox && !lightbox.classList.contains('hidden')) {
    closeLightboxFunction();
    // Add a small delay to ensure lightbox is closed before proceeding
    setTimeout(() => {
      applyStyleFilter(style);
    }, 100);
    return;
  }

  applyStyleFilter(style);
}

// Helper function to apply style filter
function applyStyleFilter(style) {
  // Update current style filter
  currentStyleFilter = style;

  // Update button UI
  filterButtons.forEach(btn => {
    if (btn.getAttribute('data-filter') === style) {
      btn.classList.add('active', 'gallery-filter-btn-active');
      btn.classList.remove('gallery-filter-btn');
    } else {
      btn.classList.remove('active', 'gallery-filter-btn-active');
      btn.classList.add('gallery-filter-btn');
    }
  });

  // Reload gallery items
  loadGalleryItems();
}

function filterByStylist(stylist) {
  console.log("Filter by stylist clicked:", stylist);

  // Force close lightbox if it's open
  if (lightbox && !lightbox.classList.contains('hidden')) {
    closeLightboxFunction();
    // Add a small delay to ensure lightbox is closed before proceeding
    setTimeout(() => {
      applyStylistFilter(stylist);
    }, 100);
    return;
  }

  applyStylistFilter(stylist);
}

// Helper function to apply stylist filter
function applyStylistFilter(stylist) {
  // Update current stylist filter
  currentStylistFilter = stylist;

  // Update button UI
  stylistButtons.forEach(btn => {
    if (btn.getAttribute('data-stylist') === stylist) {
      btn.classList.add('active', 'gallery-stylist-btn-active');
      btn.classList.remove('gallery-stylist-btn');
    } else {
      btn.classList.remove('active', 'gallery-stylist-btn-active');
      btn.classList.add('gallery-stylist-btn');
    }
  });

  // Reload gallery items
  loadGalleryItems();
}

// Reset filters to show all items
function resetFilters() {
  // Reset style filter
  filterByStyle('all');

  // Reset stylist filter
  filterByStylist('all');
}

// Set up lightbox functionality
function setupLightbox() {
  if (!closeLightbox || !lightbox || !prevImage || !nextImage) {
    console.error("Lightbox elements not found. Make sure you're on the gallery page.");
    return;
  }

  // Close lightbox when clicking the close button
  closeLightbox.addEventListener('click', (e) => {
    e.preventDefault();
    e.stopPropagation();
    closeLightboxFunction();
  });

  // Close lightbox when clicking outside the image
  lightbox.addEventListener('click', (e) => {
    if (e.target === lightbox) {
      e.preventDefault();
      closeLightboxFunction();
    }
  });

  // Navigate to previous image - using multiple approaches for redundancy
  prevImage.addEventListener('click', (e) => {
    console.log("Previous image button clicked via addEventListener");
    e.preventDefault();
    e.stopPropagation();

    // Check if we have filtered items
    if (!filteredItems || filteredItems.length === 0) {
      console.error("No filtered items available for navigation");
      return;
    }

    // Calculate new index with safety check
    currentImageIndex = (currentImageIndex - 1 + filteredItems.length) % filteredItems.length;
    window.currentImageIndex = currentImageIndex; // Update global variable
    console.log("New image index:", currentImageIndex);

    // Update lightbox content
    updateLightboxContent();
  }, true); // Use capture phase

  // Navigate to next image - using multiple approaches for redundancy
  nextImage.addEventListener('click', (e) => {
    console.log("Next image button clicked via addEventListener");
    e.preventDefault();
    e.stopPropagation();

    // Check if we have filtered items
    if (!filteredItems || filteredItems.length === 0) {
      console.error("No filtered items available for navigation");
      return;
    }

    // Calculate new index with safety check
    currentImageIndex = (currentImageIndex + 1) % filteredItems.length;
    window.currentImageIndex = currentImageIndex; // Update global variable
    console.log("New image index:", currentImageIndex);

    // Update lightbox content
    updateLightboxContent();
  }, true); // Use capture phase

  // Add direct click handlers to the navigation buttons for redundancy
  prevImage.onclick = function(e) {
    console.log("Previous image direct onclick handler");
    e.preventDefault();
    e.stopPropagation();

    if (filteredItems && filteredItems.length > 0) {
      currentImageIndex = (currentImageIndex - 1 + filteredItems.length) % filteredItems.length;
      window.currentImageIndex = currentImageIndex; // Update global variable
      updateLightboxContent();
    }
  };

  nextImage.onclick = function(e) {
    console.log("Next image direct onclick handler");
    e.preventDefault();
    e.stopPropagation();

    if (filteredItems && filteredItems.length > 0) {
      currentImageIndex = (currentImageIndex + 1) % filteredItems.length;
      window.currentImageIndex = currentImageIndex; // Update global variable
      updateLightboxContent();
    }
  };

  // Add additional click handlers using mousedown/mouseup for maximum compatibility
  prevImage.addEventListener('mousedown', function(e) {
    console.log("Previous image mousedown event");
    e.preventDefault();
    e.stopPropagation();
  }, true);

  prevImage.addEventListener('mouseup', function(e) {
    console.log("Previous image mouseup event");
    e.preventDefault();
    e.stopPropagation();

    if (filteredItems && filteredItems.length > 0) {
      currentImageIndex = (currentImageIndex - 1 + filteredItems.length) % filteredItems.length;
      window.currentImageIndex = currentImageIndex; // Update global variable
      updateLightboxContent();
    }
  }, true);

  nextImage.addEventListener('mousedown', function(e) {
    console.log("Next image mousedown event");
    e.preventDefault();
    e.stopPropagation();
  }, true);

  nextImage.addEventListener('mouseup', function(e) {
    console.log("Next image mouseup event");
    e.preventDefault();
    e.stopPropagation();

    if (filteredItems && filteredItems.length > 0) {
      currentImageIndex = (currentImageIndex + 1) % filteredItems.length;
      window.currentImageIndex = currentImageIndex; // Update global variable
      updateLightboxContent();
    }
  }, true);

  // Keyboard navigation
  document.addEventListener('keydown', (e) => {
    if (lightbox.classList.contains('hidden')) return;

    if (e.key === 'Escape') {
      closeLightboxFunction();
    } else if (e.key === 'ArrowLeft') {
      e.preventDefault();
      prevImage.click();
    } else if (e.key === 'ArrowRight') {
      e.preventDefault();
      nextImage.click();
    }
  });
}

// Separate function to close the lightbox to ensure consistent behavior
function closeLightboxFunction() {
  console.log("Closing lightbox");

  // Clear the safety timeout if it exists
  if (window.lightboxSafetyTimeout) {
    clearTimeout(window.lightboxSafetyTimeout);
    window.lightboxSafetyTimeout = null;
  }

  if (!lightbox) {
    console.error("Lightbox element not found when trying to close");
    return;
  }

  try {
    // Add hidden class
    lightbox.classList.add('hidden');

    // Restore scrolling
    document.body.style.overflow = '';

    // Force the display property to be none
    lightbox.style.display = 'none';

    // Set z-index to a lower value
    lightbox.style.zIndex = '-1';

    // Set other properties to ensure it's hidden
    lightbox.style.visibility = 'hidden';
    lightbox.style.opacity = '0';
    lightbox.style.pointerEvents = 'none';

    // Clear the image source to prevent memory leaks
    if (lightboxImage) {
      lightboxImage.src = '';
    }

    // Re-enable filter buttons
    enableFilterButtons();

    console.log("Lightbox closed:",
      "display:", lightbox.style.display,
      "visibility:", lightbox.style.visibility,
      "opacity:", lightbox.style.opacity,
      "z-index:", lightbox.style.zIndex,
      "class:", lightbox.className
    );

    // Force a small delay to ensure DOM updates
    setTimeout(() => {
      // Double-check that lightbox is hidden
      if (lightbox) {
        lightbox.style.display = 'none';
        lightbox.style.visibility = 'hidden';
        lightbox.style.opacity = '0';
        lightbox.style.pointerEvents = 'none';

        // Force re-enable filter buttons again
        enableFilterButtons();

        console.log("Lightbox closed and filter buttons re-enabled");
      }
    }, 100);

    // Add a click handler to the document to ensure filter buttons work
    setTimeout(() => {
      document.addEventListener('click', function documentClickHandler() {
        // Re-enable filter buttons one more time
        enableFilterButtons();
        // Remove this event listener after it's fired once
        document.removeEventListener('click', documentClickHandler);
      }, { once: true });
    }, 200);

    // Force garbage collection to free up memory (in modern browsers)
    if (window.gc) {
      window.gc();
    }
  } catch (error) {
    console.error("Error closing lightbox:", error);

    // Emergency fallback - force reload the page if there's an error closing the lightbox
    if (confirm("There was an error closing the lightbox. Would you like to reload the page?")) {
      window.location.reload();
    }
  }
}

// Function to enable filter buttons
function enableFilterButtons() {
  // Re-enable all filter buttons
  filterButtons.forEach(btn => {
    btn.disabled = false;
    btn.style.pointerEvents = 'auto';
    btn.style.cursor = 'pointer';
    btn.style.opacity = '1';
  });

  // Re-enable all stylist buttons
  stylistButtons.forEach(btn => {
    btn.disabled = false;
    btn.style.pointerEvents = 'auto';
    btn.style.cursor = 'pointer';
    btn.style.opacity = '1';
  });

  // Also ensure the containers are clickable
  const galleryFilters = document.getElementById('gallery-filters');
  const stylistFilters = document.getElementById('stylist-filters');

  if (galleryFilters) {
    galleryFilters.style.pointerEvents = 'auto';
    galleryFilters.style.zIndex = '30';
  }

  if (stylistFilters) {
    stylistFilters.style.pointerEvents = 'auto';
    stylistFilters.style.zIndex = '30';
  }
}

// Function to disable filter buttons
function disableFilterButtons() {
  // Disable all filter buttons
  filterButtons.forEach(btn => {
    btn.disabled = true;
    btn.style.pointerEvents = 'none';
  });

  // Disable all stylist buttons
  stylistButtons.forEach(btn => {
    btn.disabled = true;
    btn.style.pointerEvents = 'none';
  });
}

// Open lightbox with specific image
function openLightbox(index) {
  console.log("Opening lightbox for image index:", index);

  if (!lightbox) {
    console.error("Lightbox element not found");
    return;
  }

  // Disable filter buttons when lightbox is open
  disableFilterButtons();

  // Set current image index
  currentImageIndex = index;

  // Make sure all styles are set to make the lightbox visible
  lightbox.style.display = 'flex';
  lightbox.style.visibility = 'visible';
  lightbox.style.opacity = '1';
  lightbox.style.pointerEvents = 'auto';
  lightbox.style.zIndex = '9999';

  // Remove hidden class
  lightbox.classList.remove('hidden');

  // Prevent scrolling when lightbox is open
  document.body.style.overflow = 'hidden';

  console.log("Lightbox opened:",
    "display:", lightbox.style.display,
    "visibility:", lightbox.style.visibility,
    "opacity:", lightbox.style.opacity,
    "z-index:", lightbox.style.zIndex,
    "class:", lightbox.className
  );

  // Make sure navigation buttons are visible and clickable
  const prevBtn = document.getElementById('prev-image');
  const nextBtn = document.getElementById('next-image');

  if (prevBtn) {
    prevBtn.style.display = 'block';
    prevBtn.style.visibility = 'visible';
    prevBtn.style.opacity = '0.9';
    prevBtn.style.pointerEvents = 'auto';
    prevBtn.style.zIndex = '10001';
    prevBtn.style.position = 'absolute';
    prevBtn.style.left = '4rem';
    prevBtn.style.top = '50%';
    prevBtn.style.transform = 'translateY(-50%)';
    prevBtn.style.width = '60px';
    prevBtn.style.height = '60px';

    // Reinitialize click handlers for redundancy
    prevBtn.onclick = function(e) {
      console.log("Previous image direct onclick handler (reinitialized)");
      e.preventDefault();
      e.stopPropagation();

      if (filteredItems && filteredItems.length > 0) {
        currentImageIndex = (currentImageIndex - 1 + filteredItems.length) % filteredItems.length;
        window.currentImageIndex = currentImageIndex; // Update global variable
        updateLightboxContent();
      }
    };
  }

  if (nextBtn) {
    nextBtn.style.display = 'block';
    nextBtn.style.visibility = 'visible';
    nextBtn.style.opacity = '0.9';
    nextBtn.style.pointerEvents = 'auto';
    nextBtn.style.zIndex = '10001';
    nextBtn.style.position = 'absolute';
    nextBtn.style.right = '4rem';
    nextBtn.style.top = '50%';
    nextBtn.style.transform = 'translateY(-50%)';
    nextBtn.style.width = '60px';
    nextBtn.style.height = '60px';

    // Reinitialize click handlers for redundancy
    nextBtn.onclick = function(e) {
      console.log("Next image direct onclick handler (reinitialized)");
      e.preventDefault();
      e.stopPropagation();

      if (filteredItems && filteredItems.length > 0) {
        currentImageIndex = (currentImageIndex + 1) % filteredItems.length;
        window.currentImageIndex = currentImageIndex; // Update global variable
        updateLightboxContent();
      }
    };
  }

  // Add debug click handlers to the entire lightbox
  lightbox.addEventListener('click', function(e) {
    console.log('Lightbox clicked at coordinates:', e.clientX, e.clientY);

    // Check if click is in the left third of the screen (for previous)
    if (e.clientX < window.innerWidth / 3) {
      console.log('Left third of lightbox clicked - should go to previous image');
      if (filteredItems && filteredItems.length > 0) {
        currentImageIndex = (currentImageIndex - 1 + filteredItems.length) % filteredItems.length;
        window.currentImageIndex = currentImageIndex; // Update global variable
        updateLightboxContent();
        e.preventDefault();
        e.stopPropagation();
      }
    }
    // Check if click is in the right third of the screen (for next)
    else if (e.clientX > (window.innerWidth * 2) / 3) {
      console.log('Right third of lightbox clicked - should go to next image');
      if (filteredItems && filteredItems.length > 0) {
        currentImageIndex = (currentImageIndex + 1) % filteredItems.length;
        window.currentImageIndex = currentImageIndex; // Update global variable
        updateLightboxContent();
        e.preventDefault();
        e.stopPropagation();
      }
    }
  }, false);

  // Add safety timeout to close lightbox if it freezes
  window.lightboxSafetyTimeout = setTimeout(() => {
    // Check if lightbox is still open after 30 seconds
    if (lightbox && !lightbox.classList.contains('hidden')) {
      console.log("Safety timeout triggered - closing lightbox");
      closeLightboxFunction();
    }
  }, 30000); // 30 seconds timeout

  // Now update the lightbox content
  updateLightboxContent();
}

// Update lightbox content based on current image index
// Expose to global scope for direct access from HTML
window.updateLightboxContent = function() {
  console.log("Updating lightbox content for index:", currentImageIndex);

  if (!lightboxImage || !lightboxTitle || !lightboxDescription) {
    console.error("Lightbox content elements not found");
    return;
  }

  if (!filteredItems || filteredItems.length === 0) {
    console.error("No filtered items available");
    return;
  }

  if (currentImageIndex < 0 || currentImageIndex >= filteredItems.length) {
    console.error("Invalid image index:", currentImageIndex);
    return;
  }

  const item = filteredItems[currentImageIndex];
  console.log("Loading image:", item.fullImage);

  // Show loading indicator
  lightboxTitle.textContent = "Loading...";
  lightboxDescription.textContent = "";

  // Create a new image object to preload the image
  const preloadImg = new Image();

  // Set up handlers for the preload image
  preloadImg.onload = function() {
    console.log("Image loaded successfully");

    // Update the actual lightbox image
    lightboxImage.src = item.fullImage;
    lightboxImage.alt = item.title;

    // Update text content
    lightboxTitle.textContent = item.title;
    lightboxDescription.textContent = `${item.description} • Stylist: ${item.stylist}`;

    // Make sure navigation buttons are visible and clickable
    const prevBtn = document.getElementById('prev-image');
    const nextBtn = document.getElementById('next-image');

    if (prevBtn) {
      prevBtn.style.display = 'block';
      prevBtn.style.visibility = 'visible';
      prevBtn.style.opacity = '0.9';
      prevBtn.style.pointerEvents = 'auto';
      prevBtn.style.zIndex = '10001';
      prevBtn.style.position = 'absolute';
      prevBtn.style.left = '4rem';
      prevBtn.style.top = '50%';
      prevBtn.style.transform = 'translateY(-50%)';
      prevBtn.style.width = '60px';
      prevBtn.style.height = '60px';

      // Ensure click handlers are working
      prevBtn.onclick = function(e) {
        console.log("Previous image direct onclick handler (from updateLightboxContent)");
        e.preventDefault();
        e.stopPropagation();

        if (filteredItems && filteredItems.length > 0) {
          currentImageIndex = (currentImageIndex - 1 + filteredItems.length) % filteredItems.length;
          window.currentImageIndex = currentImageIndex; // Update global variable
          updateLightboxContent();
        }
      };
    }

    if (nextBtn) {
      nextBtn.style.display = 'block';
      nextBtn.style.visibility = 'visible';
      nextBtn.style.opacity = '0.9';
      nextBtn.style.pointerEvents = 'auto';
      nextBtn.style.zIndex = '10001';
      nextBtn.style.position = 'absolute';
      nextBtn.style.right = '4rem';
      nextBtn.style.top = '50%';
      nextBtn.style.transform = 'translateY(-50%)';
      nextBtn.style.width = '60px';
      nextBtn.style.height = '60px';

      // Ensure click handlers are working
      nextBtn.onclick = function(e) {
        console.log("Next image direct onclick handler (from updateLightboxContent)");
        e.preventDefault();
        e.stopPropagation();

        if (filteredItems && filteredItems.length > 0) {
          currentImageIndex = (currentImageIndex + 1) % filteredItems.length;
          window.currentImageIndex = currentImageIndex; // Update global variable
          updateLightboxContent();
        }
      };
    }

    // Log that the image has been loaded and navigation should be working
    console.log("Image loaded successfully, navigation buttons should be working now");
  };

  preloadImg.onerror = function() {
    console.error("Failed to load image:", item.fullImage);

    // Update the actual lightbox image with a placeholder
    lightboxImage.src = "assets/images/placeholder.jpg";
    lightboxImage.alt = "Error loading image";

    // Update text content
    lightboxTitle.textContent = "Error loading image";
    lightboxDescription.textContent = "Please try again later";
  };

  // Start loading the image
  preloadImg.src = item.fullImage;
}

// Initialize the slider
function initSlider() {
  // Clear slider track and indicators
  sliderTrack.innerHTML = '';
  sliderIndicators.innerHTML = '';

  // Create slider items
  galleryItems.forEach((item) => {
    const sliderItem = document.createElement('div');
    sliderItem.className = 'slider-item flex-shrink-0 px-2';
    sliderItem.style.width = `calc(100% / ${slidesPerView})`;

    sliderItem.innerHTML = `
      <div class="relative group overflow-hidden rounded-lg shadow-md cursor-pointer h-64">
        <img src="${item.thumbnail}" alt="${item.title}" class="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110">
        <div class="absolute inset-0 bg-black bg-opacity-60 opacity-0 group-hover:opacity-100 transition duration-300 flex flex-col justify-end p-4 text-white">
          <h3 class="font-bold text-lg">${item.title}</h3>
          <p class="text-sm">${item.description}</p>
          <p class="text-xs mt-1">Stylist: ${item.stylist}</p>
        </div>
      </div>
    `;

    // Add click event to open lightbox
    sliderItem.addEventListener('click', () => {
      // Find the index in filteredItems that matches this gallery item
      const filteredIndex = filteredItems.findIndex(fi => fi.id === item.id);
      if (filteredIndex !== -1) {
        openLightbox(filteredIndex);
      } else {
        // If not found in filtered items, reset filters and then open
        resetFilters();
        const newIndex = filteredItems.findIndex(fi => fi.id === item.id);
        if (newIndex !== -1) {
          openLightbox(newIndex);
        }
      }
    });

    sliderTrack.appendChild(sliderItem);
  });

  // Create indicators
  const totalSlides = Math.ceil(galleryItems.length / slidesPerView);
  for (let i = 0; i < totalSlides; i++) {
    const indicator = document.createElement('button');
    indicator.className = i === 0 ? 'w-3 h-3 rounded-full bg-pink-600' : 'w-3 h-3 rounded-full bg-gray-300';
    indicator.setAttribute('data-slide', i);
    indicator.addEventListener('click', () => {
      goToSlide(i);
    });
    sliderIndicators.appendChild(indicator);
  }

  // Set up slider navigation
  sliderPrev.addEventListener('click', prevSlide);
  sliderNext.addEventListener('click', nextSlide);

  // Initial position
  updateSlider();
}

// Update slider position
function updateSlider() {
  // Calculate total number of slides
  const totalSlides = Math.ceil(galleryItems.length / slidesPerView);

  // Make sure current slide is valid
  if (currentSlide >= totalSlides) {
    currentSlide = totalSlides - 1;
  }

  // Update slider track position
  sliderTrack.style.transform = `translateX(-${currentSlide * 100}%)`;

  // Update indicators
  const indicators = sliderIndicators.querySelectorAll('button');
  indicators.forEach((indicator, index) => {
    if (index === currentSlide) {
      indicator.classList.remove('bg-gray-300');
      indicator.classList.add('bg-pink-600');
    } else {
      indicator.classList.remove('bg-pink-600');
      indicator.classList.add('bg-gray-300');
    }
  });

  // Update slider items width
  const sliderItems = sliderTrack.querySelectorAll('.slider-item');
  sliderItems.forEach(item => {
    item.style.width = `calc(100% / ${slidesPerView})`;
  });
}

// Go to previous slide
function prevSlide() {
  const totalSlides = Math.ceil(galleryItems.length / slidesPerView);
  currentSlide = (currentSlide - 1 + totalSlides) % totalSlides;
  updateSlider();
}

// Go to next slide
function nextSlide() {
  const totalSlides = Math.ceil(galleryItems.length / slidesPerView);
  currentSlide = (currentSlide + 1) % totalSlides;
  updateSlider();
}

// Go to specific slide
function goToSlide(slideIndex) {
  currentSlide = slideIndex;
  updateSlider();
}
