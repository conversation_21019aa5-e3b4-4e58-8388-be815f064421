<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>GetTwisted Hair Studios</title>

  <!-- SEO & Social Meta -->
  <meta name="description" content="GetTwisted Hair Studios – Locs, Braids & Natural Hair Specialists in Philly.">
  <meta property="og:title" content="GetTwisted Hair Studios" />
  <meta property="og:description" content="Book your natural hair appointment today." />
  <meta property="og:image" content="https://www.gettwistedloc.com/assets/images/hero.jpg" />
  <meta property="og:url" content="https://www.gettwistedloc.com" />

  <!-- Tailwind CSS -->
  <script src="https://cdn.tailwindcss.com"></script>
  <link href="https://fonts.googleapis.com/css2?family=Fredoka+One&display=swap" rel="stylesheet">
  <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">

  <!-- Google Analytics -->
  <script async src="https://www.googletagmanager.com/gtag/js?id=G-92MSJDQS52"></script>
  <script>
    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);}
    gtag('js', new Date());
    gtag('config', 'G-92MSJDQS52');
  </script>

  <!-- Custom CSS -->
  <link href="css/main.css" rel="stylesheet">

  <!-- Security Headers -->
  <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline' https://cdn.tailwindcss.com https://unpkg.com https://www.googletagmanager.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://unpkg.com; font-src 'self' https://fonts.gstatic.com; frame-src https://*.square.site/ https://*.squareup.com/; connect-src https://*.square.site/ https://*.squareup.com/; img-src 'self' https://*.square.site/ https://*.squareup.com/ data:; object-src 'none';">
  <meta http-equiv="X-Content-Type-Options" content="nosniff">
  <meta http-equiv="Referrer-Policy" content="no-referrer-when-downgrade">
  <!-- Removed X-Frame-Options to allow iframe embedding -->
  <meta http-equiv="Permissions-Policy" content="geolocation=(), microphone=(), camera=()">
</head>

<!-- Navbar -->
<header class="sticky top-0 z-50 p-6 shadow-md flex flex-wrap items-center bg-pink-600">
  <!-- Logo -->
  <a href="index.html" class="text-3xl font-extrabold tracking-wide flex-shrink-0 mr-auto text-yellow-300">
    GETTWISTED
    <span class="block text-sm md:text-lg text-cyan-300">HAIR STUDIOS</span>
  </a>

  <!-- Desktop Nav -->
  <nav id="navMenu" class="hidden md:flex space-x-4 text-sm md:text-base items-center">
    <a href="index.html" class="hover:underline">Home</a>
    <a href="stylists.html" class="hover:underline">Stylists</a>
    <a href="services.html" class="hover:underline">Services</a>
    <a href="gallery.html" class="hover:underline">Gallery</a>
    <a href="careers.html" class="hover:underline">Careers</a>
    <a href="help.html" class="hover:underline">Help</a>
    <button onclick="toggleTheme()" id="themeToggleBtn" class="action-btn theme-btn" title="Change Theme">🎨</button>
    <button onclick="toggleChat()" class="action-btn chat-btn" title="Chat with Us">💬</button>
    <a href="sms:+16102880343" class="action-btn sms-btn" title="Text Us">📱</a>
  </nav>

  <!-- Mobile Toggle -->
  <button id="mobileMenuBtn" class="md:hidden bg-white text-black px-4 py-2 rounded-full shadow-lg font-bold ml-4 text-xl">
   ☰
  </button>

  <!-- Mobile Dropdown -->
  <div id="mobileMenu" class="w-full mt-4 hidden md:hidden bg-pink-600 py-3 px-2 rounded-lg shadow-lg">
    <nav class="flex flex-wrap justify-center items-center gap-4 text-sm text-white">
      <a href="index.html" class="hover:underline">Home</a>
      <a href="stylists.html" class="hover:underline">Stylists</a>
      <a href="services.html" class="hover:underline">Services</a>
      <a href="gallery.html" class="hover:underline">Gallery</a>
      <a href="careers.html" class="hover:underline">Careers</a>
      <a href="help.html" class="hover:underline">Help</a>
      <div class="flex gap-3 mt-3 sm:mt-0">
        <button onclick="toggleTheme()" class="action-btn mobile-action-btn theme-btn" title="Change Theme">🎨</button>
        <button onclick="toggleChat()" class="action-btn mobile-action-btn chat-btn" title="Chat with Us">💬</button>
        <a href="sms:+16102880343" class="action-btn mobile-action-btn sms-btn" title="Text Us">📱</a>
      </div>
    </nav>
  </div>

</header>


  <!-- Help Banner -->
  <section class="text-center py-16 bg-yellow-300 text-black" data-aos="fade-down">
    <h2 class="text-4xl md:text-5xl font-bold mb-4">Help & Support</h2>
    <p class="text-lg max-w-xl mx-auto">Got questions? We’re here to help with appointments, products, and everything hair care!</p>
  </section>

  <!-- Help Sections -->
  <section class="p-6 grid grid-cols-1 md:grid-cols-2 gap-6">
    <div class="bg-white text-black rounded-xl p-6 shadow" data-aos="fade-up">
      <h3 class="text-pink-600 text-xl mb-2">Booking Help</h3>
      <p>Click the 'Book Appointment' button on the homepage or reach out via chat for assistance.</p>
    </div>
    <div class="bg-white text-black rounded-xl p-6 shadow" data-aos="fade-up" data-aos-delay="100">
      <h3 class="text-pink-600 text-xl mb-2">Product Support</h3>
      <p>Need help choosing a product? Browse the Shop or email us for personalized recommendations.</p>
    </div>
    <div class="bg-white text-black rounded-xl p-6 shadow" data-aos="fade-up" data-aos-delay="200">
      <h3 class="text-pink-600 text-xl mb-2">Live Chat</h3>
      <p>Use the yellow floating chat bubble for real-time support (TwistyBot ready to help!).</p>
    </div>
    <div class="bg-white text-black rounded-xl p-6 shadow" data-aos="fade-up" data-aos-delay="300">
      <h3 class="text-pink-600 text-xl mb-2">Salon Locations</h3>
      <p>We’re currently available in Philadelphia, PA — more cities coming soon!</p>
    </div>
  </section>

  <!-- Contact Form Section -->
  <section class="p-6 max-w-2xl mx-auto mt-8" data-aos="fade-up">
    <div class="bg-white text-black rounded-xl p-6 shadow">
      <h3 class="text-pink-600 text-xl mb-4 text-center">Contact Us</h3>
      <p class="mb-6 text-center">Have a question or need assistance? Fill out the form below and we'll get back to you as soon as possible.</p>

      <form id="contactForm" action="https://formspree.io/f/xanonpnv" method="POST" class="space-y-4">
        <div class="mb-4">
          <label for="name" class="block text-sm font-medium text-gray-700">Full Name</label>
          <input type="text" id="name" name="name" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-pink-500 focus:ring-pink-500 p-2 border">
        </div>

        <div class="mb-4">
          <label for="email" class="block text-sm font-medium text-gray-700">Email</label>
          <input type="email" id="email" name="email" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-pink-500 focus:ring-pink-500 p-2 border">
        </div>

        <div class="mb-4">
          <label for="subject" class="block text-sm font-medium text-gray-700">Subject</label>
          <input type="text" id="subject" name="subject" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-pink-500 focus:ring-pink-500 p-2 border">
        </div>

        <div class="mb-4">
          <label for="message" class="block text-sm font-medium text-gray-700">Message</label>
          <textarea id="message" name="message" rows="4" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-pink-500 focus:ring-pink-500 p-2 border"></textarea>
        </div>

        <div class="text-center">
          <button type="submit" class="bg-pink-600 hover:bg-pink-700 text-white font-bold py-2 px-6 rounded-full transition-colors">Send Message</button>
        </div>
      </form>
    </div>
  </section>

  <!-- Chatbot -->
  <div id="chatbot" class="fixed bottom-28 right-5 bg-white w-80 rounded-xl shadow-lg overflow-hidden text-black z-50 hidden">
    <div class="bg-pink-600 text-white p-4 flex justify-between items-center">
      <h4 class="font-bold text-sm">💬 TwistyBot</h4>
      <button onclick="toggleChat()" class="text-white text-lg font-bold">&times;</button>
    </div>
    <div class="p-4 h-60 overflow-y-auto text-sm space-y-3" id="chatMessages">
      <div class="bg-pink-100 text-black p-3 rounded-lg w-fit max-w-[80%]">Hi! I’m TwistyBot 💖</div>
      <div class="bg-pink-100 text-black p-3 rounded-lg w-fit max-w-[80%]">How can I help you today?</div>
    </div>
    <div class="flex p-2 border-t">
      <input id="chatInput" type="text" placeholder="Type your question..." class="flex-1 text-sm p-2 outline-none" />
      <button id="sendBtn" class="bg-pink-600 text-white px-3 ml-2 rounded">Send</button>
    </div>
  </div>

<!-- Footer -->
<footer class="px-4 sm:px-6 py-10 mt-20 text-center md:text-left">
  <div class="max-w-6xl mx-auto px-4 sm:px-6 grid grid-cols-1 md:grid-cols-3 gap-10 text-sm md:text-base text-center md:text-left">

    <!-- Brand Info -->
    <div>
      <h3 class="text-xl font-bold mb-2">GetTwisted Hair Studios</h3>
      <p>Where natural beauty meets expert care.</p>
      <p class="mt-2 italic">"Twist. Slay. Repeat."</p>
    </div>

    <!-- Contact Info with Multiple Locations -->
    <div>
      <h4 class="font-semibold mb-1">Contact Us</h4>
      <p>📍 Teaneck, NJ</p>
      <p>📍 Pottstown, PA</p>
      <p>📞 (610) 288-0343</p>
      <p>📧 <EMAIL></p>
    </div>

    <!-- Quick Links -->
    <div class="space-y-2">
      <h4 class="font-semibold mb-1">Quick Links</h4>
      <a href="https://get-twisted-hair-studio.square.site/" target="_blank" class="block hover:underline">Book Appointment</a>
      <a href="stylists.html" class="block hover:underline">Meet Our Stylists</a>
      <a href="services.html" class="block hover:underline">Our Services</a>
      <a href="gallery.html" class="block hover:underline">Our Gallery</a>
    </div>
  </div>

  <p class="text-center text-xs footer-copy mt-8">&copy; 2024 GetTwisted Hair Studios. All rights reserved.</p>
</footer>

  <!-- Scripts -->
  <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
  <script src="js/main.js"></script>
  <script>
    AOS.init();

    function toggleChat() {
      const chat = document.getElementById('chatbot');
      chat.classList.toggle('hidden');
      chat.classList.toggle('animate-slide-in');
    }

    function toggleTheme() {
      const themes = ['vibrant', 'black-white', 'matrix'];
      let currentTheme = themes.findIndex(t => document.body.classList.contains(`theme-${t}`));
      document.body.classList.remove(`theme-${themes[currentTheme]}`);
      currentTheme = (currentTheme + 1) % themes.length;
      document.body.classList.add(`theme-${themes[currentTheme]}`);
      localStorage.setItem('selectedTheme', themes[currentTheme]);
    }

    document.addEventListener("DOMContentLoaded", () => {
      const savedTheme = localStorage.getItem('selectedTheme') || 'vibrant';
      document.body.classList.add(`theme-${savedTheme}`);
    });
  </script>

  <script>
    let chatbotKnowledge = [];

    fetch('assets/data/chatbot-knowledge.json')
      .then(response => response.json())
      .then(data => {
        chatbotKnowledge = data;
        console.log("Chatbot knowledge loaded:", chatbotKnowledge);
      })
      .catch(err => console.error("Error loading chatbot knowledge:", err));

    function sanitize(text) {
      return text.toLowerCase().replace(/[^\w\s]/gi, '').trim();
    }

    function handleChatInput(inputText) {
      const cleanInput = sanitize(inputText);

      let match = chatbotKnowledge.find(item => sanitize(item.question) === cleanInput);
      if (!match) {
        match = chatbotKnowledge.find(item => cleanInput.includes(sanitize(item.question)));
      }

      return match
        ? match.answer
        : "I'm here to help, but I don’t have that answer yet. You can text us directly at (************* or visit our booking page to learn more.";
    }

    document.addEventListener("DOMContentLoaded", () => {
      const input = document.getElementById('chatInput');
      const sendBtn = document.getElementById('sendBtn');
      const chatBox = document.getElementById('chatMessages');

      function sendMessage() {
        const text = input.value.trim();
        if (!text) return;

        const userBubble = `<div class="bg-yellow-100 text-black p-3 rounded-lg w-fit max-w-[80%] ml-auto">${text}</div>`;
        const botReply = handleChatInput(text);
        const botBubble = `<div class="bg-pink-100 text-black p-3 rounded-lg w-fit max-w-[80%]">${botReply}</div>`;

        chatBox.innerHTML += userBubble + botBubble;
        input.value = '';
        chatBox.scrollTop = chatBox.scrollHeight;
      }

      sendBtn.addEventListener('click', sendMessage);
      input.addEventListener('keydown', e => { if (e.key === 'Enter') sendMessage(); });
    });
  </script>
  <!-- Mobile Menu Toggle is now handled by inline onclick attribute -->

</body>
</html>
