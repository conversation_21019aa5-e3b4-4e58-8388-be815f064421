/* Main Styles for GetTwisted Hair Studios */

body {
  font-family: 'Fredoka One', cursive;
}

.animate-slide-in {
  animation: slideIn 0.3s ease-out forwards;
}

@keyframes slideIn {
  0% { transform: translateY(100%); opacity: 0; }
  100% { transform: translateY(0); opacity: 1; }
}

/* Theme Styling */
body.theme-vibrant { background-color: #f472b6; color: white; }
body.theme-black-white { background-color: #000; color: #fff; }
body.theme-matrix { background-color: #000; color: #00ff00; }

/* Header and section backgrounds */
body.theme-vibrant header { background-color: #ec4899 !important; }
body.theme-vibrant section { background-color: #f472b6 !important; }
body.theme-black-white header, body.theme-black-white section { background-color: #000 !important; color: #fff !important; }
body.theme-matrix header, body.theme-matrix section { background-color: #000 !important; color: #00ff00 !important; }

/* Footer styling */
body.theme-black-white .footer-copy, body.theme-matrix .footer-copy { color: #aaa; }
body.theme-vibrant .footer-copy { color: #f9a8d4; }

/* Action Buttons Styling */
.action-btn {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.08);
  border: 2px solid transparent;
}

.action-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 7px 14px rgba(0, 0, 0, 0.1), 0 3px 6px rgba(0, 0, 0, 0.08);
}

.action-btn:active {
  transform: translateY(1px);
}

.action-btn::after {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(255,255,255,0.8) 0%, rgba(255,255,255,0) 70%);
  opacity: 0;
  top: 0;
  left: 0;
  transform: scale(0);
  transition: all 0.3s;
}

.action-btn:hover::after {
  opacity: 1;
  transform: scale(2);
}

/* Theme Button */
.theme-btn {
  background-color: white;
  color: black;
}

body.theme-black-white .theme-btn {
  background-color: #333;
  color: white;
  border-color: white;
}

body.theme-matrix .theme-btn {
  background-color: #001800;
  color: #00ff00;
  border-color: #00ff00;
}

/* Chat Button */
.chat-btn {
  background-color: #fbbf24;
  color: black;
}

body.theme-black-white .chat-btn {
  background-color: #333;
  color: white;
  border-color: white;
}

body.theme-matrix .chat-btn {
  background-color: #001800;
  color: #00ff00;
  border-color: #00ff00;
}

/* SMS Button */
.sms-btn {
  background-color: #ec4899;
  color: white;
}

body.theme-black-white .sms-btn {
  background-color: #333;
  color: white;
  border-color: white;
}

body.theme-matrix .sms-btn {
  background-color: #001800;
  color: #00ff00;
  border-color: #00ff00;
}

/* Mobile version */
.mobile-action-btn {
  width: 36px;
  height: 36px;
  font-size: 1rem;
}

/* Button animations */
@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

@keyframes float {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-3px); }
  100% { transform: translateY(0px); }
}

.theme-btn {
  animation: pulse 2s infinite;
}

.chat-btn {
  animation: float 3s ease-in-out infinite;
}

.sms-btn {
  transition: all 0.3s;
}

.sms-btn:hover {
  transform: rotate(10deg) translateY(-3px);
}

/* Form elements */
body.theme-black-white input, body.theme-black-white select, body.theme-black-white textarea {
  background-color: #222;
  color: white;
  border-color: #444;
}
body.theme-matrix input, body.theme-matrix select, body.theme-matrix textarea {
  background-color: #001100;
  color: #00ff00;
  border-color: #00aa00;
}

.hero-section {
  transition: background 0.3s ease;
}
body.theme-vibrant .hero-section {
  background: linear-gradient(to bottom right, #fb923c, #fde047);
}
body.theme-black-white .hero-section,
body.theme-matrix .hero-section {
  background: #000;
}
/* Headings + titles */
.theme-text {
  transition: color 0.3s ease;
}

body.theme-vibrant .theme-text {
  color: white;
}

/* Exception for form title in vibrant theme */
body.theme-vibrant #applicationForm .theme-text {
  color: #ec4899;
}

body.theme-black-white .theme-text {
  color: #fff;
}

body.theme-matrix .theme-text {
  color: #00ff00;
}

/* Optional for description/subheadings */
.theme-subtext {
  transition: color 0.3s ease;
}

body.theme-vibrant .theme-subtext {
  color: #fde68a;
}

body.theme-black-white .theme-subtext {
  color: #aaa;
}

body.theme-matrix .theme-subtext {
  color: #66ff66;
}

/* Careers page specific styling */
/* Job cards */
body.theme-black-white .job-card {
  background-color: #222 !important;
  color: white !important;
  border: 1px solid #444;
}

body.theme-matrix .job-card {
  background-color: #001100 !important;
  color: #00ff00 !important;
  border: 1px solid #00aa00;
}

/* Form styling */
body.theme-black-white .form-input,
body.theme-black-white .form-select {
  background-color: #222;
  color: white;
  border-color: #444;
}

body.theme-matrix .form-input,
body.theme-matrix .form-select {
  background-color: #001100;
  color: #00ff00;
  border-color: #00aa00;
}

/* Application form modal styling */
body.theme-vibrant #applicationForm .bg-white {
  background-color: white;
  color: black;
  border: 1px solid #f472b6;
  box-shadow: 0 4px 12px rgba(244, 114, 182, 0.3);
}

body.theme-black-white #applicationForm .bg-white {
  background-color: #111;
  color: white;
  border: 1px solid #444;
}

body.theme-matrix #applicationForm .bg-white {
  background-color: #000;
  color: #00ff00;
  border: 1px solid #00aa00;
}

/* Button styling */
body.theme-black-white .book-btn,
body.theme-black-white .submit-btn {
  background-color: #444;
  color: white;
}

body.theme-matrix .book-btn,
body.theme-matrix .submit-btn {
  background-color: #00aa00;
  color: #000;
}

/* Ensure white backgrounds in vibrant theme */
body.theme-vibrant .bg-white {
  background-color: white !important;
  color: black !important;
}

/* Hero/Banner sections */
body.theme-black-white .bg-yellow-300 {
  background-color: #222 !important;
  color: white !important;
}

body.theme-matrix .bg-yellow-300 {
  background-color: #001100 !important;
  color: #00ff00 !important;
}

body.theme-black-white .bg-yellow-300 .text-black,
body.theme-black-white .bg-yellow-300 h2,
body.theme-black-white .bg-yellow-300 p {
  color: white !important;
}

body.theme-matrix .bg-yellow-300 .text-black,
body.theme-matrix .bg-yellow-300 h2,
body.theme-matrix .bg-yellow-300 p {
  color: #00ff00 !important;
}

/* Book appointment button in hero sections */
body.theme-black-white .bg-yellow-300 .bg-pink-600 {
  background-color: #444 !important;
  color: white !important;
}

body.theme-black-white .bg-yellow-300 .bg-pink-600:hover {
  background-color: #555 !important;
}

body.theme-matrix .bg-yellow-300 .bg-pink-600 {
  background-color: #00aa00 !important;
  color: #000 !important;
}

body.theme-matrix .bg-yellow-300 .bg-pink-600:hover {
  background-color: #00cc00 !important;
}

/* Careers page specific styles */
.book-btn {
  display: inline-block;
  margin-top: 1rem;
  background-color: #f472b6;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 9999px;
  font-weight: bold;
  box-shadow: 0 1px 3px rgba(0,0,0,0.2);
  transition: all 0.2s;
}

.book-btn:hover {
  background-color: #ec4899;
  transform: translateY(-1px);
}

.job-card {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.job-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.form-input {
  width: 100%;
  padding: 0.75rem;
  border-radius: 0.5rem;
  border: 1px solid #e5e7eb;
  margin-bottom: 1rem;
}

.form-input:focus {
  outline: none;
  border-color: #f472b6;
  box-shadow: 0 0 0 3px rgba(244, 114, 182, 0.2);
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

body.theme-vibrant .form-label {
  color: black;
  font-weight: 600;
}

body.theme-black-white .form-label {
  color: white;
}

body.theme-matrix .form-label {
  color: #00ff00;
}

.form-select {
  width: 100%;
  padding: 0.75rem;
  border-radius: 0.5rem;
  border: 1px solid #e5e7eb;
  margin-bottom: 1rem;
  background-color: white;
}

.form-select:focus {
  outline: none;
  border-color: #f472b6;
  box-shadow: 0 0 0 3px rgba(244, 114, 182, 0.2);
}

.submit-btn {
  display: inline-block;
  background-color: #ec4899;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 9999px;
  font-weight: bold;
  transition: background-color 0.3s ease;
  cursor: pointer;
}

.submit-btn:hover {
  background-color: #db2777;
}

/* Google Reviews Slideshow */
.review-slide {
  transition: opacity 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.animate-fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.fallback-avatar {
  object-fit: cover;
  background-color: #f3f4f6;
  border: 1px solid #e5e7eb;
}

/* Theme-specific styles for reviews */
body.theme-black-white #reviewsContainer {
  background-color: #222;
  color: white;
  border: 1px solid #444;
}

body.theme-matrix #reviewsContainer {
  background-color: #001100;
  color: #00ff00;
  border: 1px solid #00aa00;
}

body.theme-black-white .review-navigation button {
  background-color: #333;
  color: white;
}

body.theme-matrix .review-navigation button {
  background-color: #003300;
  color: #00ff00;
}

/* Booking Form Styles */
.service-category {
  transition: all 0.2s ease-in-out;
}

.service-category:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.service-category.selected {
  border-color: #db2777;
  background-color: #fce7f3;
}

/* Step indicators animation */
#step1Indicator, #step2Indicator, #step3Indicator {
  transition: all 0.3s ease;
}

#line1to2, #line2to3 {
  transition: background-color 0.3s ease;
}

/* Theme-specific booking form styles */
body.theme-black-white .service-category {
  background-color: #222;
  color: white;
  border: 1px solid #444;
}

body.theme-black-white .service-category.selected {
  background-color: #444;
  border-color: #888;
}

body.theme-matrix .service-category {
  background-color: #001100;
  color: #00ff00;
  border: 1px solid #003300;
}

body.theme-matrix .service-category.selected {
  background-color: #002200;
  border-color: #00ff00;
}

/* Mobile menu animations */
@keyframes slideDown {
  0% { transform: translateY(-10px); opacity: 0; }
  100% { transform: translateY(0); opacity: 1; }
}

#mobileMenu:not(.hidden) {
  animation: slideDown 0.3s ease-out forwards;
}

/* Make mobile menu button more noticeable */
#mobileMenuBtn {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

#mobileMenuBtn:hover, #mobileMenuBtn:focus {
  transform: scale(1.05);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Add a subtle pulse animation to the mobile menu button */
@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

@media (max-width: 768px) {
  #mobileMenuBtn {
    animation: pulse 2s infinite;
  }
}

/* Services page tab styling */
body.theme-black-white #category-tabs button {
  background-color: #333;
  color: white;
  border: 1px solid #444;
}

body.theme-black-white #category-tabs button.active {
  background-color: #555;
  color: white;
}

body.theme-matrix #category-tabs button {
  background-color: #001100;
  color: #00ff00;
  border: 1px solid #00aa00;
}

body.theme-matrix #category-tabs button.active {
  background-color: #003300;
  color: #00ff00;
}

/* Gallery filter buttons styling */
.gallery-filter-btn-active {
  background-color: #ec4899;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  z-index: 1;
}

.gallery-filter-btn {
  background-color: white;
  color: #ec4899;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  z-index: 1;
}

.gallery-filter-btn:hover {
  background-color: #fce7f3;
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

body.theme-black-white .gallery-filter-btn-active {
  background-color: #555;
  color: white;
}

body.theme-black-white .gallery-filter-btn {
  background-color: #333;
  color: white;
  border: 1px solid #444;
}

body.theme-black-white .gallery-filter-btn:hover {
  background-color: #444;
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
}

body.theme-matrix .gallery-filter-btn-active {
  background-color: #003300;
  color: #00ff00;
}

body.theme-matrix .gallery-filter-btn {
  background-color: #001100;
  color: #00ff00;
  border: 1px solid #00aa00;
}

body.theme-matrix .gallery-filter-btn:hover {
  background-color: #002200;
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 100, 0, 0.3);
}

/* Gallery stylist buttons styling */
.gallery-stylist-btn-active {
  background-color: #fde047;
  color: black;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  z-index: 1;
}

.gallery-stylist-btn {
  background-color: white;
  color: black;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  z-index: 1;
}

.gallery-stylist-btn:hover {
  background-color: #fef9c3;
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

body.theme-black-white .gallery-stylist-btn-active {
  background-color: #555;
  color: white;
}

body.theme-black-white .gallery-stylist-btn {
  background-color: #333;
  color: white;
  border: 1px solid #444;
}

body.theme-black-white .gallery-stylist-btn:hover {
  background-color: #444;
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
}

body.theme-matrix .gallery-stylist-btn-active {
  background-color: #003300;
  color: #00ff00;
}

body.theme-matrix .gallery-stylist-btn {
  background-color: #001100;
  color: #00ff00;
  border: 1px solid #00aa00;
}

body.theme-matrix .gallery-stylist-btn:hover {
  background-color: #002200;
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 100, 0, 0.3);
}

/* Ensure filter buttons are always clickable */
.filter-btn, .stylist-btn {
  position: relative;
  z-index: 50;
  pointer-events: auto !important;
  cursor: pointer !important;
  user-select: none;
}

/* Fix for filter button containers */
#gallery-filters, #stylist-filters {
  position: relative;
  z-index: 50;
  pointer-events: auto !important;
}

/* Ensure the gallery section doesn't have any overlays */
.gallery-item {
  position: relative;
  z-index: 1;
}

/* Ensure the lightbox is completely hidden when not active */
#gallery-lightbox.hidden {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
  pointer-events: none !important;
  z-index: -1 !important;
}

/* Service cards */
body.theme-black-white .category-content .bg-white {
  background-color: #222 !important;
  color: white !important;
  border: 1px solid #444;
}

body.theme-matrix .category-content .bg-white {
  background-color: #001100 !important;
  color: #00ff00 !important;
  border: 1px solid #00aa00;
}

body.theme-black-white .category-content .bg-white .bg-pink-600 {
  background-color: #333 !important;
}

body.theme-matrix .category-content .bg-white .bg-pink-600 {
  background-color: #003300 !important;
}

body.theme-black-white .category-content .bg-white .text-pink-600 {
  color: #aaa !important;
}

body.theme-matrix .category-content .bg-white .text-pink-600 {
  color: #00ff00 !important;
}

body.theme-black-white .category-content .bg-white .text-gray-600,
body.theme-black-white .category-content .bg-white .text-gray-500 {
  color: #aaa !important;
}

body.theme-matrix .category-content .bg-white .text-gray-600,
body.theme-matrix .category-content .bg-white .text-gray-500 {
  color: #00aa00 !important;
}

/* Book now button */
body.theme-black-white .category-content .bg-yellow-300 {
  background-color: #444 !important;
  color: white !important;
}

body.theme-black-white .category-content .bg-yellow-300:hover {
  background-color: #555 !important;
}

body.theme-matrix .category-content .bg-yellow-300 {
  background-color: #00aa00 !important;
  color: #000 !important;
}

body.theme-matrix .category-content .bg-yellow-300:hover {
  background-color: #00cc00 !important;
}

/* TikTok Videos Section */
/* Make TikTok section heading visible in all themes */
body.theme-vibrant section:has(.tiktok-embed) h2 {
  color: #ec4899 !important;
  text-shadow: 1px 1px 3px rgba(0,0,0,0.2);
}

body.theme-black-white section:has(.tiktok-embed) h2 {
  color: white !important;
}

body.theme-matrix section:has(.tiktok-embed) h2 {
  color: #00ff00 !important;
}

/* Style TikTok embeds for better visibility */
.tiktok-embed {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  position: relative;
}

/* Add a play button overlay to make it clear videos are clickable */
.tiktok-embed::before {
  content: "▶️";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 48px;
  z-index: 10;
  opacity: 0.8;
  transition: opacity 0.3s ease, transform 0.3s ease;
  pointer-events: none;
}

.tiktok-embed:hover::before {
  opacity: 1;
  transform: translate(-50%, -50%) scale(1.1);
}

/* Make TikTok text visible in all themes */
.tiktok-embed section {
  padding: 8px;
  background-color: white;
  color: black !important;
  font-weight: bold;
}
