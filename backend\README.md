# GetTwisted Hair Studios - Backend API

This is the backend API for the GetTwisted Hair Studios website.

## Directory Structure

- `config/` - Configuration files
- `src/` - Source code
  - `controllers/` - Request handlers
  - `middleware/` - Express middleware
  - `models/` - MongoDB models
  - `routes/` - API routes
  - `server.js` - Main server file

## Development

To start the development server:

```bash
npm run dev:backend
```

This will start the API server at http://localhost:5000.

## API Endpoints

### Authentication
- `POST /api/auth/register` - Register a new user
- `POST /api/auth/login` - Login a user
- `GET /api/auth/me` - Get current user
- `GET /api/auth/logout` - Logout user

### Stylists
- `GET /api/stylists` - Get all stylists
- `GET /api/stylists/:id` - Get a single stylist
- `POST /api/stylists` - Create a new stylist (admin only)
- `PUT /api/stylists/:id` - Update a stylist (admin only)
- `DELETE /api/stylists/:id` - Delete a stylist (admin only)

### Services
- `GET /api/services` - Get all services
- `GET /api/services/category/:categoryName` - Get services by category
- `GET /api/services/:id` - Get a single service
- `POST /api/services` - Create a new service (admin only)
- `PUT /api/services/:id` - Update a service (admin only)
- `DELETE /api/services/:id` - Delete a service (admin only)

## Database

The API uses MongoDB. To connect to a database, set the `MONGO_URI` environment variable in the `.env` file.
