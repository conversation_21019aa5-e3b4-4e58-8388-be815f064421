const fs = require('fs');
const path = require('path');

// Paths
const srcDir = path.join(__dirname);
const publicDir = path.join(__dirname, '..', 'public');

// Copy CSS files
const cssDir = path.join(srcDir, 'styles');
const publicCssDir = path.join(publicDir, 'css');

if (!fs.existsSync(publicCssDir)) {
  fs.mkdirSync(publicCssDir, { recursive: true });
}

fs.readdirSync(cssDir).forEach(file => {
  if (file.endsWith('.css')) {
    const srcFile = path.join(cssDir, file);
    const destFile = path.join(publicCssDir, file);
    fs.copyFileSync(srcFile, destFile);
    console.log(`Copied ${srcFile} to ${destFile}`);
  }
});

// Copy JS files
const jsDir = path.join(srcDir, 'js');
const publicJsDir = path.join(publicDir, 'js');

if (!fs.existsSync(publicJsDir)) {
  fs.mkdirSync(publicJsDir, { recursive: true });
}

fs.readdirSync(jsDir).forEach(file => {
  if (file.endsWith('.js')) {
    const srcFile = path.join(jsDir, file);
    const destFile = path.join(publicJsDir, file);
    fs.copyFileSync(srcFile, destFile);
    console.log(`Copied ${srcFile} to ${destFile}`);
  }
});

console.log('Build completed successfully!');
