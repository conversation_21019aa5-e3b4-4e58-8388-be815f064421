const express = require('express');
const cors = require('cors');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Database connection would go here
// Commented out for testing
// if (process.env.MONGO_URI) {
//   connectDB();
// }

// Initialize Express app
const app = express();

// Middleware
app.use(cors());
app.use(express.json());

// Routes
app.get('/api/health', (req, res) => {
  res.status(200).json({ status: 'ok', message: 'API is running' });
});

// Import route modules
// const authRoutes = require('./routes/auth');
const stylistRoutes = require('./routes/stylists');
const serviceRoutes = require('./routes/services');
const applicantRoutes = require('./routes/applicants');
const contactRoutes = require('./routes/contact');

// Use routes
// app.use('/api/auth', authRoutes);
app.use('/api/stylists', stylistRoutes);
app.use('/api/services', serviceRoutes);
app.use('/api/applicants', applicantRoutes);
app.use('/api/contact', contactRoutes);

// Error handling middleware - commented out for testing
// app.use(errorHandler);

// Start server
const PORT = process.env.PORT || 5000;
const server = app.listen(PORT, () => {
  console.log(`Server running in ${process.env.NODE_ENV || 'development'} mode on port ${PORT}`);
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (err, promise) => {
  console.log(`Error: ${err.message}`);
  // Close server & exit process
  server.close(() => process.exit(1));
});

module.exports = app;
