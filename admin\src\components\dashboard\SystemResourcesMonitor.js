import React from 'react';

const SystemResourcesMonitor = ({ cpuUsage, memoryUsage }) => {
  // Calculate percentage for progress bars
  const cpuPercentage = Math.min(100, Math.max(0, cpuUsage));
  const memoryPercentage = Math.min(100, Math.max(0, memoryUsage.percentage));
  
  // Determine color based on usage
  const getCpuBarColor = (usage) => {
    if (usage < 50) return 'low';
    if (usage < 80) return 'medium';
    return 'high';
  };
  
  const getMemoryBarColor = (usage) => {
    if (usage < 50) return 'low';
    if (usage < 80) return 'medium';
    return 'high';
  };
  
  return (
    <div className="system-resources-monitor">
      <h3 className="monitor-title">System Resources</h3>
      
      <div className="resource-item">
        <div className="resource-label">
          CPU Usage: {cpuPercentage}%
        </div>
        <div className="resource-bar-container">
          <div 
            className={`resource-bar cpu ${getCpuBarColor(cpuPercentage)}`}
            style={{ width: `${cpuPercentage}%` }}
          ></div>
        </div>
      </div>
      
      <div className="resource-item">
        <div className="resource-label">
          Memory Usage: {memoryUsage.used}MB / {memoryUsage.total}MB ({memoryPercentage}%)
        </div>
        <div className="resource-bar-container">
          <div 
            className={`resource-bar memory ${getMemoryBarColor(memoryPercentage)}`}
            style={{ width: `${memoryPercentage}%` }}
          ></div>
        </div>
      </div>
    </div>
  );
};

export default SystemResourcesMonitor;
