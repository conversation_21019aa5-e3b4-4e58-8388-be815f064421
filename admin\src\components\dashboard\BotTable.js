import React from 'react';

const BotTable = ({ bots, type }) => {
  // Define status badge colors
  const getStatusBadgeClass = (status) => {
    switch (status) {
      case 'running':
        return 'status-badge running';
      case 'paused':
        return 'status-badge paused';
      case 'stopped':
        return 'status-badge stopped';
      default:
        return 'status-badge';
    }
  };

  // Define status icons
  const getStatusIcon = (status) => {
    switch (status) {
      case 'running':
        return '▶️';
      case 'paused':
        return '⏸️';
      case 'stopped':
        return '⏹️';
      default:
        return '';
    }
  };

  return (
    <div className={`bot-table-container ${type.toLowerCase()}`}>
      <h3 className="table-title">
        {type === 'LIVE' ? 'Live Trading Bots' : 'Simulation Bots'}
      </h3>
      <table className="bot-table">
        <thead>
          <tr>
            <th>Status</th>
            <th>Bot Name</th>
            <th>Symbol</th>
            <th>Win Rate</th>
            <th>PnL</th>
            <th>Trades</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          {bots.map((bot) => (
            <tr key={bot.id}>
              <td>
                <span className={getStatusBadgeClass(bot.status)}>
                  {getStatusIcon(bot.status)} {bot.status}
                </span>
              </td>
              <td>
                <div className="bot-name">
                  {bot.name}
                  <span className={`bot-type-badge ${type.toLowerCase()}`}>
                    {type}
                  </span>
                </div>
              </td>
              <td>{bot.symbol}</td>
              <td>{bot.winRate}%</td>
              <td className={bot.pnl >= 0 ? 'positive' : 'negative'}>
                {bot.pnl > 0 ? '+' : ''}{bot.pnl}%
              </td>
              <td>{bot.trades}</td>
              <td>
                <div className="bot-actions">
                  {bot.status !== 'running' && (
                    <button className="action-btn start">Start</button>
                  )}
                  {bot.status === 'running' && (
                    <button className="action-btn pause">Pause</button>
                  )}
                  {bot.status !== 'stopped' && (
                    <button className="action-btn stop">Stop</button>
                  )}
                </div>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default BotTable;
