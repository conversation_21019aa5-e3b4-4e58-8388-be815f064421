# GetTwistedLocs Website

Welcome to the official repository for **GetTwisted Hair Studios**, a vibrant, natural hair-focused salon located in Philadelphia, PA. This site showcases our stylists, services, recent styles, and allows users to book appointments, ask questions through our chatbot, and shop our curated products.

## 📂 Repository Structure

This repository is organized into three main components:

- **Frontend**: Client-facing website with all public-facing content
- **Backend**: Server-side API and business logic for appointment management and data storage
- **Admin**: Administrative portal for managing stylists, services, and monitoring system performance

## 🚀 Setup Instructions

### Prerequisites

- Node.js (v14 or higher)
- npm (v6 or higher)

### Installation

1. Clone the repository:
   ```
   git clone https://github.com/Geo222222/gettwistedlocs.git
   cd gettwistedlocs
   ```

2. Install dependencies for all components:
   ```
   npm run install:all
   ```

3. Set up environment variables:
   - Create a `.env` file in the root directory
   - Add necessary environment variables (see `.env.example`)

4. Start the development servers:
   - Backend: `npm run dev:backend`
   - Frontend: `npm run dev:frontend`
   - Admin: `npm run dev:admin`

## ✨ Live Website
Visit the live site: [https://gettwistedloc.com](https://gettwistedloc.com)

Hosted via GitHub Pages: [https://geo222222.github.io/gettwistedlocs](https://geo222222.github.io/gettwistedlocs)

---

## 🌈 Features
- Dynamic **theme toggle** (Vibrant, Black & White, Matrix)
- Responsive, mobile-first design powered by **Tailwind CSS**
- Profiles for each stylist with bios, photos, and booking links
- Integrated **chatbot** and **text contact** buttons
- Interactive booking modal with embedded Square scheduling
- Gallery for flyers and recent hair styles
- Sticky, themed navigation bar with clickable site title
- Custom domain: `gettwistedloc.com`

---

## 🔎 Pages
| Page         | Description                          |
|--------------|--------------------------------------|
| `/`          | Home page with hero, gallery, tips   |
| `/stylists/` | Profiles of all stylists & barbers   |
| `/shop/`     | Featured hair products               |
| `/help/`     | FAQ and chatbot support              |

Note: URLs are folder-structured for clean navigation (e.g., `/stylists/` instead of `stylists.html`).

---

## ⚖️ Technologies Used
- **HTML5**
- **Tailwind CSS** (via CDN)
- **JavaScript** (for interactivity & theme persistence)
- **AOS** (Animate on Scroll library)
- **Express.js** (Backend API)
- **MongoDB** (Database)
- **React.js** (Admin Portal)
- **GitHub Pages** (for hosting frontend)

---

## 🌍 Deployment
The site is continuously deployed via GitHub Pages.

To deploy updates:
1. Make changes to the HTML, CSS, or JS files.
2. Commit and push to the `main` branch.
3. GitHub Pages will auto-deploy.

---

## ✉️ Contact
**GetTwisted Hair Studios**
Philadelphia, PA
Phone: (*************
Email: <EMAIL>

---

## 🚀 Future Plans
- Add booking confirmation animations
- Full accessibility (WCAG compliance)
- Structured SEO for search visibility
- Portfolio of client styles with tags

---

> "Twist. Slay. Repeat." — The GetTwisted Way
