"""
Integration tests for the help page.
"""
import pytest
import time
from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from conftest import navigate_to_page, wait_for_element, wait_for_element_clickable, check_element_exists

def test_help_page_loads(driver, wait):
    """Test that the help page loads successfully."""
    navigate_to_page(driver, "help.html")
    
    # Check that the page title is correct
    assert "Help - GetTwisted Hair Studios" in driver.title
    
    # Check that the help banner is displayed
    banner = wait_for_element(driver, By.CSS_SELECTOR, "section h2")
    assert "Help" in banner.text or "FAQ" in banner.text or "Support" in banner.text
    
    # Check that the help section is present
    help_section = wait_for_element(driver, By.CSS_SELECTOR, "section.py-16")
    assert help_section.is_displayed()

def test_faq_section(driver, wait):
    """Test that the FAQ section is displayed correctly."""
    navigate_to_page(driver, "help.html")
    
    # Check for FAQ items
    faq_items = driver.find_elements(By.CSS_SELECTOR, ".faq-item, .accordion-item, details")
    
    # If no specific FAQ items are found, look for questions and answers
    if len(faq_items) == 0:
        questions = driver.find_elements(By.CSS_SELECTOR, "h3, h4, .question, strong")
        answers = driver.find_elements(By.CSS_SELECTOR, "p, .answer")
        
        # Check that there are questions and answers
        assert len(questions) > 0, "No FAQ questions found"
        assert len(answers) > 0, "No FAQ answers found"
    else:
        # Check the first FAQ item
        first_item = faq_items[0]
        assert first_item.is_displayed()
        
        # Try to find the question and answer in the FAQ item
        question = first_item.find_elements(By.CSS_SELECTOR, "h3, h4, .question, summary, button")
        answer = first_item.find_elements(By.CSS_SELECTOR, "p, .answer, .accordion-body")
        
        # Check that the question and answer are present
        assert len(question) > 0 or len(answer) > 0, "FAQ item does not contain question or answer"

def test_contact_form(driver, wait):
    """Test that the contact form is displayed correctly."""
    navigate_to_page(driver, "help.html")
    
    # Look for a contact form
    contact_forms = driver.find_elements(By.CSS_SELECTOR, "form, #contactForm")
    
    # If no contact form is found, skip the test
    if len(contact_forms) == 0:
        pytest.skip("No contact form found on the help page")
    
    # Check the contact form
    contact_form = contact_forms[0]
    assert contact_form.is_displayed()
    
    # Check for common form fields
    name_field = contact_form.find_elements(By.XPATH, ".//input[@name='name' or @id='name' or @placeholder='Name']")
    email_field = contact_form.find_elements(By.XPATH, ".//input[@name='email' or @id='email' or @placeholder='Email']")
    message_field = contact_form.find_elements(By.XPATH, ".//textarea[@name='message' or @id='message' or @placeholder='Message']")
    
    # Check that at least some of the expected fields are present
    assert len(name_field) > 0 or len(email_field) > 0 or len(message_field) > 0, "Contact form does not contain expected fields"
    
    # Check for a submit button
    submit_buttons = contact_form.find_elements(By.XPATH, ".//button[@type='submit'] | .//input[@type='submit'] | .//button[contains(text(), 'Submit')] | .//button[contains(text(), 'Send')]")
    assert len(submit_buttons) > 0, "Contact form does not contain a submit button"

def test_contact_information(driver, wait):
    """Test that contact information is displayed correctly."""
    navigate_to_page(driver, "help.html")
    
    # Look for contact information
    contact_info = driver.find_elements(By.XPATH, "//*[contains(text(), 'Contact') or contains(text(), 'contact')]")
    
    # Check that contact information is present
    assert len(contact_info) > 0, "No contact information found on the help page"
    
    # Look for specific contact details
    phone_numbers = driver.find_elements(By.XPATH, "//*[contains(text(), '(610)') or contains(text(), '+1')]")
    email_addresses = driver.find_elements(By.XPATH, "//*[contains(text(), '@') and contains(text(), '.com')]")
    locations = driver.find_elements(By.XPATH, "//*[contains(text(), 'Teaneck') or contains(text(), 'Pottstown')]")
    
    # Check that at least some contact details are present
    assert len(phone_numbers) > 0 or len(email_addresses) > 0 or len(locations) > 0, "No specific contact details found"

def test_accordion_functionality(driver, wait):
    """Test that accordion or collapsible elements work correctly."""
    navigate_to_page(driver, "help.html")
    
    # Look for accordion or collapsible elements
    accordions = driver.find_elements(By.CSS_SELECTOR, ".accordion-item, details, .faq-item")
    
    # If no accordion elements are found, skip the test
    if len(accordions) == 0:
        # Look for elements that might be clickable to reveal answers
        clickable_elements = driver.find_elements(By.CSS_SELECTOR, ".question, summary, .accordion-header button")
        
        if len(clickable_elements) == 0:
            pytest.skip("No accordion or collapsible elements found on the help page")
        else:
            accordions = clickable_elements
    
    # Try to click the first accordion element
    first_accordion = accordions[0]
    driver.execute_script("arguments[0].scrollIntoView(true);", first_accordion)
    time.sleep(1)  # Allow scroll to complete
    
    # Try to find a clickable element within the accordion
    clickable = first_accordion.find_elements(By.CSS_SELECTOR, "button, summary, .accordion-header")
    
    if len(clickable) > 0:
        # Click the clickable element
        clickable[0].click()
    else:
        # Click the accordion itself
        first_accordion.click()
    
    time.sleep(1)  # Allow accordion to expand
    
    # Check if the accordion expanded
    # This is difficult to test generically, so we'll just check if any class changed
    # or if any new elements became visible
    
    # We'll consider the test passed if we didn't get any exceptions

def test_theme_compatibility(driver, wait):
    """Test that the help page works correctly with different themes."""
    navigate_to_page(driver, "help.html")
    
    # Get the initial theme
    body = driver.find_element(By.TAG_NAME, "body")
    initial_theme = body.get_attribute("class")
    
    # Click the theme toggle button
    theme_button = wait_for_element_clickable(driver, By.ID, "themeToggleBtn")
    theme_button.click()
    time.sleep(1)  # Allow theme to change
    
    # Check that the theme has changed
    new_theme = body.get_attribute("class")
    assert initial_theme != new_theme
    
    # Check that the help content is still displayed
    help_content = driver.find_elements(By.CSS_SELECTOR, ".faq-item, .accordion-item, details, h3, h4")
    assert len(help_content) > 0
    
    # Reset the theme
    theme_button.click()
    time.sleep(1)  # Allow theme to change
