<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Gallery - GetTwisted Hair Studios</title>

  <!-- SEO & Social Meta -->
  <meta name="description" content="GetTwisted Hair Studios – View our gallery of natural hairstyles, locs, braids, and more.">
  <meta property="og:title" content="Gallery - GetTwisted Hair Studios" />
  <meta property="og:description" content="Check out our latest styles and transformations." />
  <meta property="og:image" content="https://www.gettwistedloc.com/assets/images/hero.jpg" />
  <meta property="og:url" content="https://www.gettwistedloc.com/gallery.html" />

  <!-- Security Headers -->
  <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline' https://cdn.tailwindcss.com https://unpkg.com https://www.googletagmanager.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://unpkg.com; font-src 'self' https://fonts.gstatic.com; frame-src https://get-twisted-hair-studio.square.site/ https://*.square.site/ https://*.squareup.com/; connect-src https://*.square.site/ https://*.squareup.com/; img-src 'self' https://*.square.site/ https://*.squareup.com/ data:; object-src 'none';">
  <meta http-equiv="X-Content-Type-Options" content="nosniff">
  <meta http-equiv="Referrer-Policy" content="no-referrer-when-downgrade">
  <meta http-equiv="Permissions-Policy" content="geolocation=(), microphone=(), camera=()">

  <!-- Tailwind CSS -->
  <script src="https://cdn.tailwindcss.com"></script>
  <!-- Fonts + AOS -->
  <link href="https://fonts.googleapis.com/css2?family=Fredoka+One&display=swap" rel="stylesheet">
  <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">

  <!-- Google tag (gtag.js) -->
  <script async src="https://www.googletagmanager.com/gtag/js?id=G-QMXCEDX3LX"></script>
  <script>
    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);}
    gtag('js', new Date());
    gtag('config', 'G-QMXCEDX3LX');
  </script>

  <!-- Custom CSS -->
  <link href="css/main.css" rel="stylesheet">


</head>

<body class="theme-vibrant">
  <!-- Navbar -->
  <header class="sticky top-0 z-50 p-6 shadow-md flex flex-wrap items-center bg-pink-600">
    <!-- Logo -->
    <a href="index.html" class="text-3xl font-extrabold tracking-wide flex-shrink-0 mr-auto text-yellow-300">
      GETTWISTED
      <span class="block text-sm md:text-lg text-cyan-300">HAIR STUDIOS</span>
    </a>

    <!-- Desktop Nav -->
    <nav id="navMenu" class="hidden md:flex space-x-4 text-sm md:text-base items-center">
      <a href="index.html" class="hover:underline">Home</a>
      <a href="stylists.html" class="hover:underline">Stylists</a>
      <a href="services.html" class="hover:underline">Services</a>
      <a href="gallery.html" class="hover:underline">Gallery</a>
      <a href="careers.html" class="hover:underline">Careers</a>
      <a href="help.html" class="hover:underline">Help</a>
      <button onclick="toggleTheme()" id="themeToggleBtn" class="action-btn theme-btn" title="Change Theme">🎨</button>
      <button onclick="toggleChat()" class="action-btn chat-btn" title="Chat with Us">💬</button>
      <a href="sms:+16102880343" class="action-btn sms-btn" title="Text Us">📱</a>
    </nav>

    <!-- Mobile Toggle -->
    <button id="mobileMenuToggle" class="md:hidden bg-white text-black px-4 py-2 rounded-full shadow-lg font-bold ml-4 text-xl">
     ☰
    </button>

    <!-- Mobile Nav -->
    <div id="mobileMenu" class="hidden fixed inset-0 bg-pink-600 bg-opacity-95 z-40 flex flex-col items-center justify-center text-white text-xl space-y-6 w-full h-full">
      <button id="closeMenu" class="absolute top-4 right-4 text-white">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
        </svg>
      </button>
      <nav class="flex flex-col items-center space-y-4">
        <a href="index.html" class="hover:underline">Home</a>
        <a href="stylists.html" class="hover:underline">Stylists</a>
        <a href="services.html" class="hover:underline">Services</a>
        <a href="gallery.html" class="hover:underline">Gallery</a>
        <a href="careers.html" class="hover:underline">Careers</a>
        <a href="help.html" class="hover:underline">Help</a>
        <div class="flex space-x-4 mt-6">
          <button onclick="toggleTheme()" class="action-btn mobile-action-btn theme-btn" title="Change Theme">🎨</button>
          <button onclick="toggleChat()" class="action-btn mobile-action-btn chat-btn" title="Chat with Us">💬</button>
          <a href="sms:+16102880343" class="action-btn mobile-action-btn sms-btn" title="Text Us">📱</a>
        </div>
      </nav>
    </div>
  </header>

  <!-- Gallery Banner -->
  <section class="text-center py-16 bg-yellow-300 text-black" data-aos="fade-down">
    <h2 class="text-4xl md:text-5xl font-bold mb-4 theme-text">Our Gallery</h2>
    <p class="text-lg max-w-xl mx-auto theme-subtext">Check out our latest styles and transformations</p>
  </section>

  <!-- Gallery Filter -->
  <section class="p-6 max-w-6xl mx-auto">
    <div class="mb-8 text-center">
      <h3 class="text-xl font-bold mb-4 theme-text">Filter by Style</h3>
      <div id="gallery-filters" class="flex flex-wrap justify-center gap-3 mb-8 px-4 relative z-50">
        <button type="button" class="filter-btn active gallery-filter-btn-active px-4 py-2 rounded-full transition shadow-md hover:shadow-lg" data-filter="all" onclick="filterByStyle('all')">All Styles</button>
        <button type="button" class="filter-btn gallery-filter-btn px-4 py-2 rounded-full transition shadow-md hover:shadow-lg" data-filter="locs" onclick="filterByStyle('locs')">Locs</button>
        <button type="button" class="filter-btn gallery-filter-btn px-4 py-2 rounded-full transition shadow-md hover:shadow-lg" data-filter="braids" onclick="filterByStyle('braids')">Braids</button>
        <button type="button" class="filter-btn gallery-filter-btn px-4 py-2 rounded-full transition shadow-md hover:shadow-lg" data-filter="cuts" onclick="filterByStyle('cuts')">Cuts</button>
        <button type="button" class="filter-btn gallery-filter-btn px-4 py-2 rounded-full transition shadow-md hover:shadow-lg" data-filter="barber" onclick="filterByStyle('barber')">Barber</button>
        <button type="button" class="filter-btn gallery-filter-btn px-4 py-2 rounded-full transition shadow-md hover:shadow-lg" data-filter="natural" onclick="filterByStyle('natural')">Natural</button>
      </div>

      <h3 class="text-xl font-bold mb-4 mt-6 theme-text">Filter by Stylist</h3>
      <div id="stylist-filters" class="flex flex-wrap justify-center gap-3 mb-8 px-4 relative z-50">
        <button type="button" class="stylist-btn active gallery-stylist-btn-active px-4 py-2 rounded-full transition shadow-md hover:shadow-lg" data-stylist="all" onclick="filterByStylist('all')">All Stylists</button>
        <button type="button" class="stylist-btn gallery-stylist-btn px-4 py-2 rounded-full transition shadow-md hover:shadow-lg" data-stylist="Sheryl" onclick="filterByStylist('Sheryl')">Sheryl</button>
        <button type="button" class="stylist-btn gallery-stylist-btn px-4 py-2 rounded-full transition shadow-md hover:shadow-lg" data-stylist="Michael" onclick="filterByStylist('Michael')">Michael</button>
        <!-- <button type="button" class="stylist-btn gallery-stylist-btn px-4 py-2 rounded-full transition shadow-md hover:shadow-lg" data-stylist="Tasha" onclick="filterByStylist('Tasha')">Tasha</button> -->
        <button type="button" class="stylist-btn gallery-stylist-btn px-4 py-2 rounded-full transition shadow-md hover:shadow-lg" data-stylist="Shantell" onclick="filterByStylist('Shantell')">Shantell</button>
        <button type="button" class="stylist-btn gallery-stylist-btn px-4 py-2 rounded-full transition shadow-md hover:shadow-lg" data-stylist="Marcus" onclick="filterByStylist('Marcus')">Marcus</button>
        <button type="button" class="stylist-btn gallery-stylist-btn px-4 py-2 rounded-full transition shadow-md hover:shadow-lg" data-stylist="Sasha" onclick="filterByStylist('Sasha')">Sasha</button>
      </div>
    </div>



    <!-- Gallery Grid -->
    <div id="gallery-grid" class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
      <!-- Gallery items will be loaded here via JavaScript -->
    </div>
  </section>

  <!-- Lightbox Modal -->
  <div id="gallery-lightbox" class="fixed inset-0 bg-black bg-opacity-90 z-[9999] hidden" style="display: none; visibility: hidden; opacity: 0; pointer-events: none;">
    <button id="close-lightbox" class="absolute top-4 right-4 text-white hover:text-pink-400 transition-colors cursor-pointer z-[10001] bg-pink-600 bg-opacity-80 rounded-full p-2 shadow-lg" style="pointer-events: auto !important;">
      <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
      </svg>
    </button>
    <!-- Emergency close button -->
    <button id="emergency-close" class="absolute top-4 left-4 text-white hover:text-red-400 transition-colors cursor-pointer z-[10001] bg-red-600 bg-opacity-80 rounded-full p-2 shadow-lg" style="pointer-events: auto !important;" onclick="window.location.reload();" title="Emergency reload - click if page freezes">
      <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
      </svg>
    </button>
    <button id="prev-image" class="absolute left-4 top-1/2 transform -translate-y-1/2 text-white hover:text-pink-400 transition-colors cursor-pointer z-[10001] bg-pink-600 bg-opacity-80 rounded-full p-3 shadow-lg" style="pointer-events: auto !important; width: 60px; height: 60px; display: block; visibility: visible; opacity: 0.9;" onclick="navigatePrevImage(event)">
      <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="3" d="M15 19l-7-7 7-7" />
      </svg>
    </button>
    <button id="next-image" class="absolute right-4 top-1/2 transform -translate-y-1/2 text-white hover:text-pink-400 transition-colors cursor-pointer z-[10001] bg-pink-600 bg-opacity-80 rounded-full p-3 shadow-lg" style="pointer-events: auto !important; width: 60px; height: 60px; display: block; visibility: visible; opacity: 0.9;" onclick="navigateNextImage(event)">
      <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="3" d="M9 5l7 7-7 7" />
      </svg>
    </button>

    <script>
      // Direct navigation functions that can be called from HTML
      function navigatePrevImage(e) {
        console.log("Previous image button clicked via inline onclick");
        e.preventDefault();
        e.stopPropagation();

        // Get the current filtered items and image index from the global scope
        if (window.filteredItems && window.filteredItems.length > 0) {
          window.currentImageIndex = (window.currentImageIndex - 1 + window.filteredItems.length) % window.filteredItems.length;
          window.updateLightboxContent();
        }
      }

      function navigateNextImage(e) {
        console.log("Next image button clicked via inline onclick");
        e.preventDefault();
        e.stopPropagation();

        // Get the current filtered items and image index from the global scope
        if (window.filteredItems && window.filteredItems.length > 0) {
          window.currentImageIndex = (window.currentImageIndex + 1) % window.filteredItems.length;
          window.updateLightboxContent();
        }
      }
    </script>
    <div class="flex items-center justify-center h-full p-4">
      <div class="max-w-4xl max-h-[80vh] relative">
        <img id="lightbox-image" src="" alt="Gallery Image" class="max-w-full max-h-[70vh] object-contain rounded-lg shadow-2xl">
        <div class="text-white text-center mt-4 bg-black bg-opacity-70 p-4 rounded-lg">
          <h3 id="lightbox-title" class="text-xl font-bold"></h3>
          <p id="lightbox-description" class="text-sm mt-2"></p>
        </div>
      </div>
    </div>
  </div>

  <script>
    // Initialize lightbox state
    document.addEventListener('DOMContentLoaded', function() {
      const lightbox = document.getElementById('gallery-lightbox');
      if (lightbox) {
        // Ensure lightbox is completely hidden on page load
        lightbox.classList.add('hidden');
        lightbox.style.display = 'none';
        lightbox.style.visibility = 'hidden';
        lightbox.style.opacity = '0';
        lightbox.style.pointerEvents = 'none';
        lightbox.style.zIndex = '-1';

        // Add direct click handler to close button for redundancy
        const closeBtn = document.getElementById('close-lightbox');
        if (closeBtn) {
          closeBtn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            // Force close the lightbox
            lightbox.classList.add('hidden');
            lightbox.style.display = 'none';
            lightbox.style.visibility = 'hidden';
            lightbox.style.opacity = '0';
            lightbox.style.pointerEvents = 'none';
            lightbox.style.zIndex = '-1';
            document.body.style.overflow = '';

            console.log("Lightbox closed via direct close button handler");
          });
        }
      }
    });
  </script>

  <style>
    /* Additional Lightbox Styles */
    #gallery-lightbox {
      display: none;
      position: fixed;
      inset: 0;
      z-index: 9999;
      background-color: rgba(0, 0, 0, 0.9);
      align-items: center;
      justify-content: center;
    }

    #gallery-lightbox.hidden {
      display: none !important;
    }

    #gallery-lightbox:not(.hidden) {
      display: flex !important;
    }

    #prev-image, #next-image, #close-lightbox {
      opacity: 0.9;
      transition: opacity 0.3s ease, transform 0.3s ease, background-color 0.3s ease;
      cursor: pointer !important;
      z-index: 10001 !important;
      pointer-events: auto !important;
      background-color: rgba(219, 39, 119, 0.8); /* Pink-600 with opacity */
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
      position: absolute !important;
      display: block !important;
    }

    #prev-image:hover, #next-image:hover, #close-lightbox:hover {
      opacity: 1;
      transform: scale(1.1);
      background-color: rgba(219, 39, 119, 1); /* Full opacity on hover */
    }

    #prev-image:active, #next-image:active, #close-lightbox:active {
      transform: scale(0.95);
      background-color: rgba(190, 24, 93, 1); /* Pink-700 */
    }

    /* Make sure navigation buttons are always visible */
    #gallery-lightbox:not(.hidden) #prev-image,
    #gallery-lightbox:not(.hidden) #next-image,
    #gallery-lightbox:not(.hidden) #close-lightbox {
      display: block !important;
      visibility: visible !important;
      opacity: 0.9 !important;
      pointer-events: auto !important;
    }

    /* Specific styles for navigation buttons to ensure they're clickable */
    #prev-image {
      left: 4rem !important;
      top: 50% !important;
      transform: translateY(-50%) !important;
      width: 60px !important;
      height: 60px !important;
    }

    #next-image {
      right: 4rem !important;
      top: 50% !important;
      transform: translateY(-50%) !important;
      width: 60px !important;
      height: 60px !important;
    }

    /* Ensure filter buttons are visible and clickable */
    .filter-btn, .stylist-btn {
      position: relative;
      z-index: 1;
      cursor: pointer;
    }

    /* Ensure lightbox controls are always on top */
    #close-lightbox {
      position: absolute;
      top: 20px;
      right: 20px;
      background-color: rgba(0, 0, 0, 0.5);
      border-radius: 50%;
      padding: 5px;
    }
  </style>

<!-- Footer -->
<footer class="px-4 sm:px-6 py-10 mt-20 text-center md:text-left">
  <div class="max-w-6xl mx-auto px-4 sm:px-6 grid grid-cols-1 md:grid-cols-3 gap-10 text-sm md:text-base text-center md:text-left">

    <!-- Brand Info -->
    <div>
      <h3 class="text-xl font-bold mb-2">GetTwisted Hair Studios</h3>
      <p>Where natural beauty meets expert care.</p>
      <p class="mt-2 italic">"Twist. Slay. Repeat."</p>
    </div>

    <!-- Contact Info with Multiple Locations -->
    <div>
      <h4 class="font-semibold mb-1">Contact Us</h4>
      <p>📍 Teaneck, NJ</p>
      <p>📍 Pottstown, PA</p>
      <p>📞 (610) 288-0343</p>
      <p>📧 <EMAIL></p>
    </div>

    <!-- Quick Links -->
    <div class="space-y-2">
      <h4 class="font-semibold mb-1">Quick Links</h4>
      <a href="https://get-twisted-hair-studio.square.site/" target="_blank" class="block hover:underline">Book Appointment</a>
      <a href="stylists.html" class="block hover:underline">Meet Our Stylists</a>
      <a href="services.html" class="block hover:underline">Our Services</a>
      <a href="gallery.html" class="block hover:underline">Our Gallery</a>
    </div>
  </div>

  <p class="text-center text-xs footer-copy mt-8">&copy; 2024 GetTwisted Hair Studios. All rights reserved.</p>
</footer>

  <!-- Chatbot -->
  <div id="chatbot" class="fixed bottom-0 right-0 w-full md:w-96 bg-white rounded-t-xl shadow-xl z-40 hidden">
    <!-- Chatbot content will be loaded via JavaScript -->
  </div>

  <!-- Scripts -->
  <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
  <script src="js/main.js"></script>
  <script src="js/gallery.js"></script>
  <script>
    // Mobile menu toggle - now handled in main.js

    // Additional lightbox close handler for redundancy
    document.addEventListener('DOMContentLoaded', function() {
      const closeLightboxBtn = document.getElementById('close-lightbox');
      const lightbox = document.getElementById('gallery-lightbox');

      if (closeLightboxBtn && lightbox) {
        closeLightboxBtn.addEventListener('click', function(e) {
          e.preventDefault();
          e.stopPropagation();

          // Force close the lightbox
          lightbox.classList.add('hidden');
          lightbox.style.display = 'none';
          document.body.style.overflow = '';

          // Re-enable buttons if the enableFilterButtons function exists
          if (typeof enableFilterButtons === 'function') {
            enableFilterButtons();
          }
        });
      }
    });
  </script>
</body>
</html>
