"""
Integration tests for the services page.
"""
import pytest
import time
from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from conftest import navigate_to_page, wait_for_element, wait_for_element_clickable, check_element_exists

def test_services_page_loads(driver, wait):
    """Test that the services page loads successfully."""
    navigate_to_page(driver, "services.html")
    
    # Check that the page title is correct
    assert "Services - GetTwisted Hair Studios" in driver.title
    
    # Check that the services banner is displayed
    banner = wait_for_element(driver, By.CSS_SELECTOR, "section h2")
    assert "Our Services" in banner.text
    
    # Check that the services categories are present
    categories = wait_for_element(driver, By.ID, "category-tabs")
    assert categories.is_displayed()

def test_service_categories(driver, wait):
    """Test that the service categories work correctly."""
    navigate_to_page(driver, "services.html")
    
    # Check that all category tabs are present
    category_tabs = driver.find_elements(By.CSS_SELECTOR, "#category-tabs button")
    assert len(category_tabs) >= 3  # At least 3 categories (e.g., <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>)
    
    # Click on each category tab and check that the corresponding content is displayed
    for tab in category_tabs:
        category_id = tab.get_attribute("data-category")
        
        # Skip if the tab doesn't have a data-category attribute
        if not category_id:
            continue
        
        # Click on the tab
        driver.execute_script("arguments[0].scrollIntoView(true);", tab)
        time.sleep(0.5)  # Allow scroll to complete
        tab.click()
        time.sleep(0.5)  # Allow content to display
        
        # Check that the tab is active
        assert "active" in tab.get_attribute("class")
        
        # Check that the corresponding content is displayed
        content = driver.find_element(By.ID, f"category-{category_id}")
        assert content.is_displayed()
        
        # Check that other contents are hidden
        other_contents = driver.find_elements(By.CSS_SELECTOR, ".category-content")
        for other in other_contents:
            if other.get_attribute("id") != f"category-{category_id}":
                assert not other.is_displayed()

def test_service_cards(driver, wait):
    """Test that the service cards are displayed correctly."""
    navigate_to_page(driver, "services.html")
    
    # Click on the first category tab
    first_tab = wait_for_element_clickable(driver, By.CSS_SELECTOR, "#category-tabs button")
    first_tab.click()
    time.sleep(0.5)  # Allow content to display
    
    # Get the category ID
    category_id = first_tab.get_attribute("data-category")
    
    # Check that the service cards are displayed
    service_cards = driver.find_elements(By.CSS_SELECTOR, f"#category-{category_id} .bg-white")
    assert len(service_cards) > 0
    
    # Check the first service card
    first_card = service_cards[0]
    assert first_card.is_displayed()
    
    # Check that the card contains a service name
    service_name = first_card.find_element(By.CSS_SELECTOR, "h3")
    assert service_name.text.strip() != ""
    
    # Check that the card contains a price
    price = first_card.find_element(By.CSS_SELECTOR, ".text-pink-600")
    assert "$" in price.text
    
    # Check that the card contains a description
    description = first_card.find_element(By.CSS_SELECTOR, "p")
    assert description.text.strip() != ""

def test_book_appointment_links(driver, wait):
    """Test that the book appointment links work correctly."""
    navigate_to_page(driver, "services.html")
    
    # Find all book appointment links
    book_links = driver.find_elements(By.XPATH, "//a[contains(text(), 'Book Now')]")
    
    # Check that at least one book appointment link exists
    assert len(book_links) > 0
    
    # Check that the first link is displayed and has the correct href
    book_link = book_links[0]
    assert book_link.is_displayed()
    assert "square.site" in book_link.get_attribute("href")

def test_theme_compatibility(driver, wait):
    """Test that the services page works correctly with different themes."""
    navigate_to_page(driver, "services.html")
    
    # Get the initial theme
    body = driver.find_element(By.TAG_NAME, "body")
    initial_theme = body.get_attribute("class")
    
    # Click the theme toggle button
    theme_button = wait_for_element_clickable(driver, By.ID, "themeToggleBtn")
    theme_button.click()
    time.sleep(1)  # Allow theme to change
    
    # Check that the theme has changed
    new_theme = body.get_attribute("class")
    assert initial_theme != new_theme
    
    # Check that the service categories are still displayed
    categories = wait_for_element(driver, By.ID, "category-tabs")
    assert categories.is_displayed()
    
    # Click on the first category tab
    first_tab = wait_for_element_clickable(driver, By.CSS_SELECTOR, "#category-tabs button")
    first_tab.click()
    time.sleep(0.5)  # Allow content to display
    
    # Check that the service cards are still displayed
    service_cards = driver.find_elements(By.CSS_SELECTOR, ".bg-white")
    assert len(service_cards) > 0
    
    # Reset the theme
    theme_button.click()
    time.sleep(1)  # Allow theme to change
