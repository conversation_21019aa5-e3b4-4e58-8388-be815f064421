import React, { useState } from 'react';

const PerformanceChart = ({ liveData, simData }) => {
  const [displayMode, setDisplayMode] = useState('both'); // 'live', 'sim', or 'both'
  
  // In a real implementation, this would use a charting library like Chart.js or Recharts
  // For now, we'll create a simple mock chart
  
  return (
    <div className="performance-chart-container">
      <div className="chart-header">
        <h3 className="chart-title">Performance</h3>
        <div className="chart-controls">
          <label>Show: </label>
          <select 
            value={displayMode} 
            onChange={(e) => setDisplayMode(e.target.value)}
            className="chart-display-mode"
          >
            <option value="both">Both</option>
            <option value="live">Live Only</option>
            <option value="sim">Simulation Only</option>
          </select>
        </div>
      </div>
      
      <div className="chart-area">
        {/* This would be replaced with an actual chart component */}
        <div className="mock-chart">
          {/* Display a simple mock chart with colored lines */}
          <div className="chart-y-axis">
            <div>+10%</div>
            <div>+5%</div>
            <div>0%</div>
            <div>-5%</div>
          </div>
          
          <div className="chart-content">
            {(displayMode === 'live' || displayMode === 'both') && (
              <div className="chart-line live">
                {/* Mock line for live data */}
                <div className="chart-line-label">Live: +{liveData.currentPnl}%</div>
              </div>
            )}
            
            {(displayMode === 'sim' || displayMode === 'both') && (
              <div className="chart-line sim">
                {/* Mock line for simulation data */}
                <div className="chart-line-label">Sim: +{simData.currentPnl}%</div>
              </div>
            )}
          </div>
          
          <div className="chart-x-axis">
            <div>1h</div>
            <div>6h</div>
            <div>12h</div>
            <div>24h</div>
          </div>
        </div>
      </div>
      
      <div className="chart-legend">
        {displayMode === 'both' || displayMode === 'live' ? (
          <div className="legend-item">
            <div className="legend-color live"></div>
            <div className="legend-label">Live Trading</div>
          </div>
        ) : null}
        
        {displayMode === 'both' || displayMode === 'sim' ? (
          <div className="legend-item">
            <div className="legend-color sim"></div>
            <div className="legend-label">Simulation</div>
          </div>
        ) : null}
      </div>
    </div>
  );
};

export default PerformanceChart;
