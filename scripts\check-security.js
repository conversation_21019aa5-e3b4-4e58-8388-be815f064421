/**
 * Security Check Script for GetTwisted Hair Studios
 * 
 * This script:
 * 1. Checks for hardcoded secrets or API keys
 * 2. Looks for insecure patterns in JavaScript code
 * 3. Validates API endpoints for proper security measures
 */

const fs = require('fs');
const path = require('path');

// Patterns to check for
const SECURITY_PATTERNS = [
  {
    pattern: /(api|access|secret|key|password|token|auth).*['"][a-zA-Z0-9_\-]{16,}['"]/, 
    message: 'Possible hardcoded API key or secret'
  },
  {
    pattern: /eval\s*\(/,
    message: 'Dangerous eval() usage detected'
  },
  {
    pattern: /document\.write\s*\(/,
    message: 'Insecure document.write() usage detected'
  },
  {
    pattern: /localStorage\.setItem\s*\(\s*['"]token['"]|localStorage\.setItem\s*\(\s*['"]auth/,
    message: 'Storing sensitive data in localStorage'
  },
  {
    pattern: /http:\/\/(?!localhost)/,
    message: 'Non-secure HTTP URL detected'
  }
];

// Function to check a file for security issues
function checkFile(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  const issues = [];
  
  SECURITY_PATTERNS.forEach(({ pattern, message }) => {
    const matches = content.match(pattern);
    if (matches) {
      issues.push({
        message,
        matches: matches.map(m => m.substring(0, 50) + (m.length > 50 ? '...' : ''))
      });
    }
  });
  
  return issues;
}

// Main function
function main() {
  const filePath = process.argv[2];
  
  if (!filePath) {
    console.error('Please provide a file path to check');
    process.exit(1);
  }
  
  try {
    const issues = checkFile(filePath);
    
    if (issues.length > 0) {
      console.warn(`⚠️ Security issues found in ${filePath}:`);
      issues.forEach(issue => {
        console.warn(`   - ${issue.message}`);
        issue.matches.forEach(match => console.warn(`     ${match}`));
      });
      
      // Exit with error code if issues found
      process.exit(1);
    } else {
      console.log(`✅ No security issues found in ${filePath}`);
    }
  } catch (error) {
    console.error(`Error checking ${filePath}:`, error);
    process.exit(1);
  }
}

main();
