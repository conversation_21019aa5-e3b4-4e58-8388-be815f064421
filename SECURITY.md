# Security Policy for GetTwisted Hair Studios Website

## Reporting a Vulnerability

If you discover a security vulnerability within our website, please send an <NAME_EMAIL>. All security vulnerabilities will be promptly addressed.

## Security Measures

### Frontend Security

1. **Content Security Policy (CSP)**
   - All HTML pages include a strict CSP to prevent XSS attacks
   - Inline scripts are limited and controlled
   - External resources are restricted to trusted domains

2. **Form Security**
   - All user inputs are sanitized before processing
   - API endpoints use relative URLs to prevent hardcoding
   - CSRF protection is implemented for form submissions

3. **Secure Deployment**
   - Automated security checks run before deployment
   - GitHub Actions workflow ensures security headers are present
   - Branch protection prevents direct pushes to main

### Backend Security

1. **Environment Variables**
   - Sensitive data is stored in environment variables
   - `.env` files are excluded from version control
   - Example files do not contain real credentials

2. **API Security**
   - CORS is properly configured
   - Rate limiting prevents abuse
   - Authentication is required for sensitive operations

3. **Data Protection**
   - User data is validated and sanitized
   - Passwords are hashed using bcrypt
   - JWT tokens are used for secure authentication

## Development Guidelines

### Working with Sensitive Data

1. Never commit `.env` files to the repository
2. Use environment-specific variables for different environments
3. Rotate secrets regularly and after team member departures

### Secure Coding Practices

1. Always sanitize user inputs
2. Use parameterized queries for database operations
3. Implement proper error handling without exposing sensitive details
4. Keep dependencies updated and run security audits regularly

### Deployment Security

1. Run `npm run security:check` before deployment
2. Use the secure build process with `npm run build`
3. Review GitHub Actions logs for security warnings

## Security Tools

The repository includes several security tools:

- `scripts/secure-build.js`: Ensures all HTML files have security headers
- `scripts/check-security.js`: Scans code for potential security issues
- GitHub Actions workflow: Automates security checks during deployment
- Husky pre-commit hooks: Prevent committing insecure code

## Compliance

This website aims to comply with:

- OWASP Top 10 web application security risks
- GDPR requirements for user data protection
- Web Content Accessibility Guidelines (WCAG) 2.1
