# How to Run Integration Tests

This document provides instructions on how to run the integration tests for the GetTwisted Hair Studios website.

## Prerequisites

1. Python 3.7 or higher
2. Chrome browser installed
3. Internet connection (to download ChromeDriver)

## Setup

1. Install the required dependencies:
```bash
cd int_tests
pip install -r requirements.txt
```

2. The tests will automatically download and use ChromeDriver, so no manual setup is required.

## Running Tests

### Using the run_tests.py Script

The simplest way to run the tests is using the provided script:

```bash
# Run all tests
python run_tests.py

# Run all tests in verbose mode
python run_tests.py --verbose

# Run a specific test file
python run_tests.py --file test_gallery.py
```

### Using pytest Directly

You can also use pytest directly:

```bash
# Run all tests
python -m pytest

# Run all tests in verbose mode
python -m pytest -v

# Run a specific test file
python -m pytest test_gallery.py

# Generate HTML report
python -m pytest --html=report.html --self-contained-html
```

## Test Reports

When you run the tests using the `run_tests.py` script, an HTML report is automatically generated in the `int_tests` directory as `report.html`. You can open this file in a web browser to view detailed test results.

## Troubleshooting

1. **WebDriver Issues**: If you encounter issues with ChromeDriver, you can manually download the appropriate version from [ChromeDriver Downloads](https://sites.google.com/a/chromium.org/chromedriver/downloads) and specify its path in `conftest.py`.

2. **Test Failures**: If tests fail, check the HTML report for detailed error messages and screenshots.

3. **Path Issues**: Make sure you're running the tests from the `int_tests` directory.

4. **Browser Compatibility**: The tests are designed to work with Chrome. If you want to use a different browser, modify the `driver` fixture in `conftest.py`.

## Adding New Tests

To add new tests:

1. Create a new test file following the naming convention `test_*.py`
2. Import the necessary utilities from `conftest.py`
3. Write your test functions using the `driver` and `wait` fixtures

Example:
```python
def test_new_feature(driver, wait):
    navigate_to_page(driver, "page.html")
    element = wait_for_element(driver, By.ID, "element-id")
    assert element.is_displayed()
```
