const express = require('express');
const router = express.Router();
const {
  submitApplication,
  getApplications,
  getApplication,
  updateApplicationStatus
} = require('../controllers/applicantController');

// Public route for submitting applications
router.post('/', submitApplication);

// Admin-only routes (would normally have auth middleware)
// For now, these are open for testing purposes
router.get('/', getApplications);
router.get('/:id', getApplication);
router.put('/:id', updateApplicationStatus);

module.exports = router;
