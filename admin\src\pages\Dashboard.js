import React from 'react';
import '../styles/dashboard.css';

// Import dashboard components
import BotStatusPanel from '../components/dashboard/BotStatusPanel';
import BotTable from '../components/dashboard/BotTable';
import PerformanceChart from '../components/dashboard/PerformanceChart';
import SystemResourcesMonitor from '../components/dashboard/SystemResourcesMonitor';
import AIMarketAnalysis from '../components/dashboard/AIMarketAnalysis';
import BotActivityFeed from '../components/dashboard/BotActivityFeed';

// Import mock data
import {
  liveBots,
  simBots,
  performanceData,
  systemResources,
  marketAnalysis,
  botActivities
} from '../data/mockTradingData';

const Dashboard = () => {
  return (
    <div className="trading-dashboard">
      <div className="dashboard-header">
        <h1 className="dashboard-title">Trading Dashboard</h1>
      </div>

      {/* Metrics Section */}
      <div className="metrics-section">
        <div className="metric-card live">
          <div className="metric-title">Live Trading Profit</div>
          <div className={`metric-value ${performanceData.live.currentPnl >= 0 ? 'positive' : 'negative'}`}>
            {performanceData.live.currentPnl > 0 ? '+' : ''}{performanceData.live.currentPnl}%
          </div>
          <div className="metric-badge live">LIVE</div>
        </div>

        <div className="metric-card live">
          <div className="metric-title">Live Trading Win Rate</div>
          <div className="metric-value">{performanceData.live.winRate}%</div>
          <div className="metric-badge live">LIVE</div>
        </div>

        <div className="metric-card sim">
          <div className="metric-title">Simulation Trading Profit</div>
          <div className={`metric-value ${performanceData.sim.currentPnl >= 0 ? 'positive' : 'negative'}`}>
            {performanceData.sim.currentPnl > 0 ? '+' : ''}{performanceData.sim.currentPnl}%
          </div>
          <div className="metric-badge sim">SIM</div>
        </div>

        <div className="metric-card sim">
          <div className="metric-title">Simulation Trading Win Rate</div>
          <div className="metric-value">{performanceData.sim.winRate}%</div>
          <div className="metric-badge sim">SIM</div>
        </div>
      </div>

      {/* Section Divider */}
      <div className="section-divider">
        <div className="divider-line"></div>
        <div className="divider-text">Running Bots Overview</div>
        <div className="divider-line"></div>
      </div>

      {/* Bot Status Panel */}
      <BotStatusPanel liveBots={liveBots} simBots={simBots} />

      {/* System Resources Monitor */}
      <SystemResourcesMonitor
        cpuUsage={systemResources.cpu}
        memoryUsage={systemResources.memory}
      />

      {/* Bot Tables */}
      <div className="bot-tables">
        <BotTable bots={liveBots} type="LIVE" />
        <BotTable bots={simBots} type="SIM" />
      </div>

      {/* Performance Chart */}
      <PerformanceChart
        liveData={performanceData.live}
        simData={performanceData.sim}
      />

      {/* AI Market Analysis */}
      <AIMarketAnalysis
        liveAnalysis={marketAnalysis.live}
        simAnalysis={marketAnalysis.sim}
      />

      {/* Bot Activity Feed */}
      <BotActivityFeed activities={botActivities} />
    </div>
  );
};

export default Dashboard;
