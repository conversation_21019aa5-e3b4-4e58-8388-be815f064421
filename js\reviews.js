/**
 * Google Reviews Slideshow
 * This script fetches and displays Google reviews from multiple locations
 */

// Configuration
const LOCATIONS = [
  {
    name: "Teaneck, NJ",
    placeId: "ChIJLxCUEQX0wokRFYdH_YjCmtQ" // Replace with actual Google Place ID for Teaneck location
  },
  {
    name: "Pottstown, PA",
    placeId: "ChIJvVrxV9X9xokRIFvGWjSr1GQ" // Replace with actual Google Place ID for Pottstown location
  }
];

// Reviews container
let reviewsData = [];
let currentReviewIndex = 0;
let reviewsContainer;
let reviewInterval;

// Initialize reviews on page load
document.addEventListener('DOMContentLoaded', () => {
  reviewsContainer = document.getElementById('reviewsContainer');

  if (reviewsContainer) {
    initReviews();
  }

  // Make navigation functions globally available
  window.prevReview = prevReview;
  window.nextReview = nextReview;
});

// Initialize reviews
async function initReviews() {
  try {
    // Show loading state
    reviewsContainer.innerHTML = '<div class="text-center py-8"><div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-pink-600"></div><p class="mt-2">Loading reviews...</p></div>';

    // Fetch reviews from both locations
    await fetchAllReviews();

    // Start the slideshow if we have reviews
    if (reviewsData.length > 0) {
      startReviewsSlideshow();
    } else {
      reviewsContainer.innerHTML = '<p class="text-center py-8">No reviews available at this time.</p>';
    }
  } catch (error) {
    console.error('Error initializing reviews:', error);
    reviewsContainer.innerHTML = '<p class="text-center py-8">Unable to load reviews. Please try again later.</p>';
  }
}

// Fetch reviews from all locations
async function fetchAllReviews() {
  // For demo purposes, we'll use sample data
  // In a real implementation, you would use the Google Places API to fetch actual reviews

  // Sample reviews data (replace with actual API calls in production)
  const sampleReviews = [
    {
      author_name: "Sarah Johnson",
      rating: 5,
      text: "Absolutely love this salon! My locs have never looked better. The stylists are so knowledgeable and take their time to make sure everything is perfect.",
      relative_time_description: "2 weeks ago",
      profile_photo_url: "https://www.gravatar.com/avatar/00000000000000000000000000000000?d=mp&f=y",
      location: "Teaneck, NJ"
    },
    {
      author_name: "Michael Thompson",
      rating: 5,
      text: "First time getting my hair done here and I'm definitely coming back! The atmosphere is great and my barber was amazing.",
      relative_time_description: "1 month ago",
      profile_photo_url: "https://www.gravatar.com/avatar/11111111111111111111111111111111?d=mp&f=y",
      location: "Pottstown, PA"
    },
    {
      author_name: "Jasmine Williams",
      rating: 4,
      text: "Great service and friendly staff. My stylist was very attentive and listened to exactly what I wanted. Will be back for my next retwist!",
      relative_time_description: "3 weeks ago",
      profile_photo_url: "https://www.gravatar.com/avatar/22222222222222222222222222222222?d=mp&f=y",
      location: "Teaneck, NJ"
    },
    {
      author_name: "David Rodriguez",
      rating: 5,
      text: "Best loc maintenance I've ever had! The detox treatment made my scalp feel amazing and my locs look so clean and fresh.",
      relative_time_description: "2 months ago",
      profile_photo_url: "https://www.gravatar.com/avatar/33333333333333333333333333333333?d=mp&f=y",
      location: "Pottstown, PA"
    },
    {
      author_name: "Tiffany Brooks",
      rating: 5,
      text: "I've been coming here for over a year now and I'm always satisfied with my results. The stylists are professional and the salon is always clean.",
      relative_time_description: "1 week ago",
      profile_photo_url: "https://www.gravatar.com/avatar/44444444444444444444444444444444?d=mp&f=y",
      location: "Teaneck, NJ"
    }
  ];

  // In a real implementation, you would fetch reviews from the Google Places API
  // For each location using their Place IDs

  // For now, we'll use the sample data
  reviewsData = sampleReviews;

  // Sort reviews by most recent
  reviewsData.sort((a, b) => {
    // This is a simple sort for the sample data
    // In a real implementation, you would sort by actual dates
    return 0.5 - Math.random(); // Random sort for demo purposes
  });

  return reviewsData;
}

// Start the reviews slideshow
function startReviewsSlideshow() {
  // Show the first review
  showReview(currentReviewIndex);

  // Set up automatic rotation
  reviewInterval = setInterval(() => {
    currentReviewIndex = (currentReviewIndex + 1) % reviewsData.length;
    showReview(currentReviewIndex);
  }, 5000); // Change review every 5 seconds
}

// Show a specific review
function showReview(index) {
  const review = reviewsData[index];

  // Generate stars based on rating
  let stars = '';
  for (let i = 0; i < 5; i++) {
    if (i < review.rating) {
      stars += '★'; // Filled star
    } else {
      stars += '☆'; // Empty star
    }
  }

  // Create the review HTML
  const reviewHTML = `
    <div class="review-slide animate-fade-in">
      <div class="flex items-center mb-4">
        <img src="${review.profile_photo_url}" alt="${review.author_name}" class="w-12 h-12 rounded-full mr-4 bg-gray-200"
             onerror="this.onerror=null; this.src='assets/images/default-avatar.svg'; this.classList.add('fallback-avatar');">
        <div>
          <h4 class="font-bold">${review.author_name}</h4>
          <div class="text-yellow-400 text-lg">${stars}</div>
        </div>
      </div>
      <p class="mb-4">"${review.text}"</p>
      <div class="flex justify-between text-sm text-gray-500">
        <span>${review.relative_time_description}</span>
        <span>${review.location}</span>
      </div>
    </div>
  `;

  // Update the container with fade effect
  reviewsContainer.style.opacity = 0;
  setTimeout(() => {
    reviewsContainer.innerHTML = reviewHTML;
    reviewsContainer.style.opacity = 1;
  }, 300);

  // Update the dots/indicators if they exist
  updateReviewIndicators(index);
}

// Update the review indicators/dots
function updateReviewIndicators(activeIndex) {
  const indicatorsContainer = document.getElementById('reviewIndicators');

  if (!indicatorsContainer) return;

  // Create indicators if they don't exist
  if (indicatorsContainer.children.length === 0) {
    for (let i = 0; i < reviewsData.length; i++) {
      const dot = document.createElement('button');
      dot.className = 'w-3 h-3 rounded-full mx-1 focus:outline-none transition-colors duration-300';
      dot.onclick = () => {
        currentReviewIndex = i;
        showReview(i);

        // Reset the interval to prevent quick transitions
        clearInterval(reviewInterval);
        reviewInterval = setInterval(() => {
          currentReviewIndex = (currentReviewIndex + 1) % reviewsData.length;
          showReview(currentReviewIndex);
        }, 5000);
      };
      indicatorsContainer.appendChild(dot);
    }
  }

  // Update active state
  for (let i = 0; i < indicatorsContainer.children.length; i++) {
    const dot = indicatorsContainer.children[i];
    if (i === activeIndex) {
      dot.classList.add('bg-pink-600');
      dot.classList.remove('bg-gray-300');
    } else {
      dot.classList.add('bg-gray-300');
      dot.classList.remove('bg-pink-600');
    }
  }
}

// Manual navigation functions
function prevReview() {
  currentReviewIndex = (currentReviewIndex - 1 + reviewsData.length) % reviewsData.length;
  showReview(currentReviewIndex);

  // Reset the interval to prevent quick transitions
  clearInterval(reviewInterval);
  reviewInterval = setInterval(() => {
    currentReviewIndex = (currentReviewIndex + 1) % reviewsData.length;
    showReview(currentReviewIndex);
  }, 5000);
}

function nextReview() {
  currentReviewIndex = (currentReviewIndex + 1) % reviewsData.length;
  showReview(currentReviewIndex);

  // Reset the interval to prevent quick transitions
  clearInterval(reviewInterval);
  reviewInterval = setInterval(() => {
    currentReviewIndex = (currentReviewIndex + 1) % reviewsData.length;
    showReview(currentReviewIndex);
  }, 5000);
}
