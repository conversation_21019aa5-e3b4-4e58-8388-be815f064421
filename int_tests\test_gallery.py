"""
Integration tests for the gallery page.
"""
import pytest
import time
from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from conftest import navigate_to_page, wait_for_element, wait_for_element_clickable, check_element_exists

def test_gallery_page_loads(driver, wait):
    """Test that the gallery page loads successfully."""
    navigate_to_page(driver, "gallery.html")

    # Check that the page title is correct
    assert "Gallery - GetTwisted Hair Studios" in driver.title

    # Check that the gallery banner is displayed
    banner = wait_for_element(driver, By.CSS_SELECTOR, "section.bg-yellow-300 h2")
    assert "Our Gallery" in banner.text

    # Check that the gallery grid is present
    gallery_grid = wait_for_element(driver, By.ID, "gallery-grid")
    assert gallery_grid.is_displayed()

def test_style_filter_buttons(driver, wait):
    """Test that the style filter buttons work correctly."""
    navigate_to_page(driver, "gallery.html")

    # Wait for gallery items to load
    wait_for_element(driver, By.CSS_SELECTOR, ".gallery-item")

    # Get initial count of gallery items
    initial_items = driver.find_elements(By.CSS_SELECTOR, ".gallery-item")
    initial_count = len(initial_items)

    # Click on the "Locs" filter button
    locs_button = wait_for_element_clickable(driver, By.CSS_SELECTOR, "button[data-filter='locs']")
    driver.execute_script("arguments[0].scrollIntoView(true);", locs_button)
    time.sleep(1)  # Allow scroll to complete
    locs_button.click()
    time.sleep(1)  # Allow filter to apply

    # Check that the button is now active
    assert "gallery-filter-btn-active" in locs_button.get_attribute("class")

    # Check that the gallery items have been filtered
    locs_items = driver.find_elements(By.CSS_SELECTOR, ".gallery-item")

    # The count may be different after filtering
    if len(locs_items) > 0:
        # Check at least one item has the locs category
        item_categories = [item.get_attribute("data-category") for item in locs_items]
        assert any("locs" in category for category in item_categories)

    # Click on the "All Styles" filter button to reset
    all_button = wait_for_element_clickable(driver, By.CSS_SELECTOR, "button[data-filter='all']")
    driver.execute_script("arguments[0].scrollIntoView(true);", all_button)
    time.sleep(1)  # Allow scroll to complete
    all_button.click()
    time.sleep(1)  # Allow filter to apply

    # Check that the gallery items have been reset
    all_items = driver.find_elements(By.CSS_SELECTOR, ".gallery-item")
    assert len(all_items) == initial_count

def test_stylist_filter_buttons(driver, wait):
    """Test that the stylist filter buttons work correctly."""
    navigate_to_page(driver, "gallery.html")

    # Wait for gallery items to load
    wait_for_element(driver, By.CSS_SELECTOR, ".gallery-item")

    # Get initial count of gallery items
    initial_items = driver.find_elements(By.CSS_SELECTOR, ".gallery-item")
    initial_count = len(initial_items)

    # Click on the "Sheryl" filter button
    sheryl_button = wait_for_element_clickable(driver, By.CSS_SELECTOR, "button[data-stylist='Sheryl']")
    driver.execute_script("arguments[0].scrollIntoView(true);", sheryl_button)
    time.sleep(1)  # Allow scroll to complete
    sheryl_button.click()
    time.sleep(1)  # Allow filter to apply

    # Check that the button is now active
    assert "gallery-stylist-btn-active" in sheryl_button.get_attribute("class")

    # Check that the gallery items have been filtered
    sheryl_items = driver.find_elements(By.CSS_SELECTOR, ".gallery-item")

    # The count may be different after filtering
    if len(sheryl_items) > 0:
        # Check at least one item has Sheryl as the stylist
        item_stylists = [item.get_attribute("data-stylist") for item in sheryl_items]
        assert "Sheryl" in item_stylists

    # Click on the "All Stylists" filter button to reset
    all_button = wait_for_element_clickable(driver, By.CSS_SELECTOR, "button[data-stylist='all']")
    driver.execute_script("arguments[0].scrollIntoView(true);", all_button)
    time.sleep(1)  # Allow scroll to complete
    all_button.click()
    time.sleep(1)  # Allow filter to apply

    # Check that the gallery items have been reset
    all_items = driver.find_elements(By.CSS_SELECTOR, ".gallery-item")
    assert len(all_items) == initial_count

def test_lightbox_functionality(driver, wait):
    """Test that the lightbox functionality works correctly."""
    navigate_to_page(driver, "gallery.html")

    # Wait for gallery items to load
    gallery_items = wait_for_element(driver, By.CSS_SELECTOR, ".gallery-item")

    # Print the number of gallery items found
    items = driver.find_elements(By.CSS_SELECTOR, ".gallery-item")
    print(f"Found {len(items)} gallery items")

    if len(items) == 0:
        pytest.skip("No gallery items found to test lightbox")

    # Click on the first gallery item to open the lightbox
    first_item = items[0]
    driver.execute_script("arguments[0].scrollIntoView(true);", first_item)
    time.sleep(1)  # Allow scroll to complete

    # Print information about the first item
    print(f"First item tag: {first_item.tag_name}")
    print(f"First item classes: {first_item.get_attribute('class')}")
    print(f"First item is displayed: {first_item.is_displayed()}")

    # Click the item
    first_item.click()
    print("Clicked on first gallery item")
    time.sleep(2)  # Allow lightbox to open

    # Check that the lightbox is displayed
    lightboxes = driver.find_elements(By.ID, "gallery-lightbox")
    if len(lightboxes) == 0:
        pytest.fail("Lightbox element not found")

    lightbox = lightboxes[0]
    print(f"Lightbox classes: {lightbox.get_attribute('class')}")
    print(f"Lightbox style: {lightbox.get_attribute('style')}")
    print(f"Lightbox is displayed: {lightbox.is_displayed()}")

    # Check if the lightbox is hidden
    if "hidden" in lightbox.get_attribute("class"):
        pytest.fail("Lightbox has 'hidden' class after clicking gallery item")

    # Check if the lightbox has display:none style
    if "display: none" in (lightbox.get_attribute("style") or ""):
        pytest.fail("Lightbox has 'display: none' style after clicking gallery item")

    # Try to find the lightbox image
    lightbox_images = driver.find_elements(By.ID, "lightbox-image")
    if len(lightbox_images) == 0:
        pytest.fail("Lightbox image element not found")

    lightbox_image = lightbox_images[0]
    print(f"Lightbox image src: {lightbox_image.get_attribute('src')}")
    print(f"Lightbox image is displayed: {lightbox_image.is_displayed()}")

    # Check for lightbox title and description
    lightbox_titles = driver.find_elements(By.ID, "lightbox-title")
    if len(lightbox_titles) > 0:
        print(f"Lightbox title text: {lightbox_titles[0].text}")
        print(f"Lightbox title is displayed: {lightbox_titles[0].is_displayed()}")

    # Try to find the close button
    close_buttons = driver.find_elements(By.ID, "close-lightbox")
    if len(close_buttons) == 0:
        pytest.fail("Close button not found")

    close_button = close_buttons[0]
    print(f"Close button is displayed: {close_button.is_displayed()}")

    # Click the close button
    close_button.click()
    print("Clicked close button")
    time.sleep(2)  # Allow lightbox to close

    # Check if the lightbox is now hidden
    print(f"Lightbox classes after closing: {lightbox.get_attribute('class')}")
    print(f"Lightbox style after closing: {lightbox.get_attribute('style')}")

    # Check that we can click on filter buttons after closing the lightbox
    filter_buttons = driver.find_elements(By.CSS_SELECTOR, "button[data-filter='locs']")
    if len(filter_buttons) == 0:
        pytest.fail("Filter button not found")

    locs_button = filter_buttons[0]
    driver.execute_script("arguments[0].scrollIntoView(true);", locs_button)
    time.sleep(1)  # Allow scroll to complete

    print(f"Filter button is displayed: {locs_button.is_displayed()}")
    print(f"Filter button classes before click: {locs_button.get_attribute('class')}")

    # Click the filter button
    locs_button.click()
    print("Clicked filter button")
    time.sleep(1)  # Allow filter to apply

    # Check that the button is now active
    print(f"Filter button classes after click: {locs_button.get_attribute('class')}")
    assert "gallery-filter-btn-active" in locs_button.get_attribute("class")

def test_combined_filters(driver, wait):
    """Test that combining style and stylist filters works correctly."""
    navigate_to_page(driver, "gallery.html")

    # Wait for gallery items to load
    wait_for_element(driver, By.CSS_SELECTOR, ".gallery-item")

    # Click on the "Locs" filter button
    locs_button = wait_for_element_clickable(driver, By.CSS_SELECTOR, "button[data-filter='locs']")
    driver.execute_script("arguments[0].scrollIntoView(true);", locs_button)
    time.sleep(1)  # Allow scroll to complete
    locs_button.click()
    time.sleep(1)  # Allow filter to apply

    # Click on the "Sheryl" filter button
    sheryl_button = wait_for_element_clickable(driver, By.CSS_SELECTOR, "button[data-stylist='Sheryl']")
    driver.execute_script("arguments[0].scrollIntoView(true);", sheryl_button)
    time.sleep(1)  # Allow scroll to complete
    sheryl_button.click()
    time.sleep(1)  # Allow filter to apply

    # Check that both buttons are active
    assert "gallery-filter-btn-active" in locs_button.get_attribute("class")
    assert "gallery-stylist-btn-active" in sheryl_button.get_attribute("class")

    # Check that the gallery items have been filtered
    filtered_items = driver.find_elements(By.CSS_SELECTOR, ".gallery-item")

    # If there are items, check they match both filters
    if len(filtered_items) > 0:
        for item in filtered_items:
            assert "locs" in item.get_attribute("data-category")
            assert item.get_attribute("data-stylist") == "Sheryl"

    # Reset filters
    all_style_button = wait_for_element_clickable(driver, By.CSS_SELECTOR, "button[data-filter='all']")
    all_stylist_button = wait_for_element_clickable(driver, By.CSS_SELECTOR, "button[data-stylist='all']")

    driver.execute_script("arguments[0].scrollIntoView(true);", all_style_button)
    time.sleep(1)
    all_style_button.click()

    driver.execute_script("arguments[0].scrollIntoView(true);", all_stylist_button)
    time.sleep(1)
    all_stylist_button.click()

    # Check that the filters have been reset
    assert "gallery-filter-btn-active" in all_style_button.get_attribute("class")
    assert "gallery-stylist-btn-active" in all_stylist_button.get_attribute("class")
