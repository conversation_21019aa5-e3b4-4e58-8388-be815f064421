/**
 * TikTok Videos Slider for GetTwisted Hair Studios
 * This script handles the horizontal slider functionality for the TikTok videos section
 */

// TikTok videos data
const tiktokVideos = [
  {
    id: 1,
    title: "Instant Locs Magic ✨",
    username: "@therealsashafearless",
    videoId: "7140483994671467819",
    description: "Watch how we create beautiful instant locs",
    category: "locs"
  },
  {
    id: 2,
    title: "Box Braids Transformation",
    username: "@therealsashafearless",
    videoId: "7140483994671467819", // Using same video ID for now (would be different in production)
    description: "From natural to box braids in one session",
    category: "braids"
  },
  {
    id: 3,
    title: "Loc Maintenance Tips",
    username: "@therealsashafearless",
    videoId: "7140483994671467819", // Using same video ID for now (would be different in production)
    description: "How to keep your locs looking fresh between appointments",
    category: "locs"
  },
  {
    id: 4,
    title: "Knot<PERSON> Braids Tutorial",
    username: "@therealsashafearless",
    videoId: "7140483994671467819", // Using same video ID for now (would be different in production)
    description: "Learn the technique behind our popular knotless braids",
    category: "braids"
  }
];

// DOM elements
let tiktokSliderTrack;
let tiktokSliderPrev;
let tiktokSliderNext;
let tiktokSliderIndicators;
let tiktokVideoModal;
let tiktokVideoContainer;
let tiktokVideoTitle;
let tiktokVideoUsername;
let closeTiktokModal;

// Current state
let currentTiktokSlide = 0;
let tiktokSlidesPerView = 2; // Default, will be updated based on screen size

// Initialize slider
document.addEventListener('DOMContentLoaded', () => {
  // Get DOM elements
  tiktokSliderTrack = document.getElementById('tiktok-slider-track');
  tiktokSliderPrev = document.getElementById('tiktok-slider-prev');
  tiktokSliderNext = document.getElementById('tiktok-slider-next');
  tiktokSliderIndicators = document.getElementById('tiktok-slider-indicators');
  tiktokVideoModal = document.getElementById('tiktok-video-modal');
  tiktokVideoContainer = document.getElementById('tiktok-video-container');
  tiktokVideoTitle = document.getElementById('tiktok-video-title');
  tiktokVideoUsername = document.getElementById('tiktok-video-username');
  closeTiktokModal = document.getElementById('close-tiktok-modal');

  if (tiktokSliderTrack) {
    // Update slides per view based on screen size
    updateTiktokSlidesPerView();

    // Initialize slider
    initTiktokSlider();

    // Handle window resize for responsive slider
    window.addEventListener('resize', () => {
      updateTiktokSlidesPerView();
      updateTiktokSlider();
    });

    // Set up modal close button
    if (closeTiktokModal) {
      closeTiktokModal.addEventListener('click', closeTiktokVideoModal);
    }

    // Close modal when clicking outside the video
    if (tiktokVideoModal) {
      tiktokVideoModal.addEventListener('click', (e) => {
        if (e.target === tiktokVideoModal) {
          closeTiktokVideoModal();
        }
      });
    }

    // Keyboard navigation for modal
    document.addEventListener('keydown', (e) => {
      if (tiktokVideoModal && !tiktokVideoModal.classList.contains('hidden')) {
        if (e.key === 'Escape') {
          closeTiktokVideoModal();
        }
      }
    });
  }
});

// Update slides per view based on screen width
function updateTiktokSlidesPerView() {
  if (window.innerWidth < 640) {
    tiktokSlidesPerView = 1; // Mobile
  } else if (window.innerWidth < 1024) {
    tiktokSlidesPerView = 2; // Tablets
  } else {
    tiktokSlidesPerView = 2; // Desktop (keeping at 2 for larger video previews)
  }
}

// Initialize the slider
function initTiktokSlider() {
  // Clear slider track and indicators
  tiktokSliderTrack.innerHTML = '';
  tiktokSliderIndicators.innerHTML = '';

  // Create slider items
  tiktokVideos.forEach((video, index) => {
    const sliderItem = document.createElement('div');
    sliderItem.className = 'tiktok-slider-item flex-shrink-0 px-3';
    sliderItem.style.width = `calc(100% / ${tiktokSlidesPerView})`;

    // Create a category badge based on video category
    const categoryBadge = video.category === 'locs'
      ? '<span class="absolute top-3 right-3 bg-purple-500 text-white text-xs px-2 py-1 rounded-full">Locs</span>'
      : '<span class="absolute top-3 right-3 bg-blue-500 text-white text-xs px-2 py-1 rounded-full">Braids</span>';

    sliderItem.innerHTML = `
      <div class="relative group overflow-hidden rounded-xl shadow-lg cursor-pointer bg-white p-4">
        <div class="tiktok-video-preview relative overflow-hidden rounded-lg h-64 mb-3 bg-gray-100">
          ${categoryBadge}
          <!-- TikTok-style preview with logo overlay -->
          <div class="absolute inset-0 flex items-center justify-center bg-gradient-to-br from-pink-100 to-pink-50">
            <div class="absolute inset-0 flex items-center justify-center">
              <div class="w-16 h-16 relative">
                <!-- TikTok-inspired logo animation -->
                <div class="absolute inset-0 bg-blue-500 rounded-full opacity-70 animate-ping"></div>
                <div class="absolute inset-0 bg-red-500 rounded-full opacity-70 animate-ping" style="animation-delay: 0.5s"></div>
                <div class="absolute inset-0 bg-teal-500 rounded-full opacity-70 animate-ping" style="animation-delay: 1s"></div>
                <!-- Play button -->
                <button class="absolute inset-0 play-button bg-black text-white rounded-full w-16 h-16 flex items-center justify-center transition-transform duration-300 transform group-hover:scale-110 z-10">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                  </svg>
                </button>
              </div>
            </div>
            <!-- Video title overlay at bottom -->
            <div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-3">
              <h3 class="font-bold text-lg text-white text-left">${video.title}</h3>
            </div>
          </div>
        </div>
        <div class="text-left">
          <div class="flex items-center mb-1">
            <span class="text-pink-500 font-bold text-sm">${video.username}</span>
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1 text-blue-500" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
            </svg>
          </div>
          <p class="text-sm text-gray-600">${video.description}</p>
        </div>
      </div>
    `;

    // Add click event to open TikTok video modal
    sliderItem.addEventListener('click', () => {
      openTiktokVideo(video);
    });

    tiktokSliderTrack.appendChild(sliderItem);
  });

  // Create indicators
  const totalSlides = Math.ceil(tiktokVideos.length / tiktokSlidesPerView);
  for (let i = 0; i < totalSlides; i++) {
    const indicator = document.createElement('button');
    indicator.className = i === 0 ? 'w-3 h-3 rounded-full bg-pink-600' : 'w-3 h-3 rounded-full bg-gray-300';
    indicator.setAttribute('data-slide', i);
    indicator.addEventListener('click', () => {
      goToTiktokSlide(i);
    });
    tiktokSliderIndicators.appendChild(indicator);
  }

  // Set up slider navigation
  tiktokSliderPrev.addEventListener('click', prevTiktokSlide);
  tiktokSliderNext.addEventListener('click', nextTiktokSlide);

  // Initial position
  updateTiktokSlider();
}

// Update slider position
function updateTiktokSlider() {
  // Calculate total number of slides
  const totalSlides = Math.ceil(tiktokVideos.length / tiktokSlidesPerView);

  // Make sure current slide is valid
  if (currentTiktokSlide >= totalSlides) {
    currentTiktokSlide = totalSlides - 1;
  }

  // Update slider track position
  tiktokSliderTrack.style.transform = `translateX(-${currentTiktokSlide * 100}%)`;

  // Update indicators
  const indicators = tiktokSliderIndicators.querySelectorAll('button');
  indicators.forEach((indicator, index) => {
    if (index === currentTiktokSlide) {
      indicator.classList.remove('bg-gray-300');
      indicator.classList.add('bg-pink-600');
    } else {
      indicator.classList.remove('bg-pink-600');
      indicator.classList.add('bg-gray-300');
    }
  });

  // Update slider items width
  const sliderItems = tiktokSliderTrack.querySelectorAll('.tiktok-slider-item');
  sliderItems.forEach(item => {
    item.style.width = `calc(100% / ${tiktokSlidesPerView})`;
  });
}

// Go to previous slide
function prevTiktokSlide() {
  const totalSlides = Math.ceil(tiktokVideos.length / tiktokSlidesPerView);
  currentTiktokSlide = (currentTiktokSlide - 1 + totalSlides) % totalSlides;
  updateTiktokSlider();
}

// Go to next slide
function nextTiktokSlide() {
  const totalSlides = Math.ceil(tiktokVideos.length / tiktokSlidesPerView);
  currentTiktokSlide = (currentTiktokSlide + 1) % totalSlides;
  updateTiktokSlider();
}

// Go to specific slide
function goToTiktokSlide(slideIndex) {
  currentTiktokSlide = slideIndex;
  updateTiktokSlider();
}

// Open TikTok video modal
function openTiktokVideo(video) {
  // Set video title and username
  tiktokVideoTitle.textContent = video.title;
  tiktokVideoUsername.textContent = video.username;

  // Create TikTok embed with loading indicator
  tiktokVideoContainer.innerHTML = `
    <div class="w-full">
      <!-- Loading indicator -->
      <div class="flex items-center justify-center py-8 mb-4 text-pink-600">
        <svg class="animate-spin h-8 w-8 mr-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        <span>Loading video...</span>
      </div>

      <!-- TikTok embed -->
      <blockquote class="tiktok-embed" cite="https://www.tiktok.com/${video.username}/video/${video.videoId}"
        data-video-id="${video.videoId}" style="max-width: 605px; min-width: 325px;">
        <section></section>
      </blockquote>

      <!-- Video info -->
      <div class="mt-4 p-4 bg-gray-50 rounded-lg">
        <div class="flex items-center mb-2">
          <span class="text-pink-500 font-bold">${video.username}</span>
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1 text-blue-500" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
          </svg>
        </div>
        <p class="text-gray-700">${video.description}</p>
        <div class="mt-3 flex items-center">
          <span class="bg-${video.category === 'locs' ? 'purple' : 'blue'}-500 text-white text-xs px-2 py-1 rounded-full">${video.category.charAt(0).toUpperCase() + video.category.slice(1)}</span>
          <a href="https://www.tiktok.com/${video.username}" target="_blank" class="ml-auto text-sm text-pink-600 hover:underline">View more on TikTok</a>
        </div>
      </div>
    </div>
  `;

  // Load TikTok embed script
  const script = document.createElement('script');
  script.src = 'https://www.tiktok.com/embed.js';
  script.async = true;
  document.body.appendChild(script);

  // Show modal
  tiktokVideoModal.classList.remove('hidden');
  document.body.style.overflow = 'hidden'; // Prevent scrolling
}

// Close TikTok video modal
function closeTiktokVideoModal() {
  tiktokVideoModal.classList.add('hidden');
  document.body.style.overflow = ''; // Restore scrolling

  // Clear video container after a short delay to allow for transition
  setTimeout(() => {
    tiktokVideoContainer.innerHTML = '';
  }, 300);
}
