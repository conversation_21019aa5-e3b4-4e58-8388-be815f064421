# GetTwisted Hair Studios - Admin Portal

This is the admin portal for the GetTwisted Hair Studios website.

## Directory Structure

- `public/` - Static files
- `src/` - Source code
  - `components/` - React components
  - `pages/` - Page components
  - `services/` - API services
  - `styles/` - CSS files

## Development

To start the development server:

```bash
npm run dev:admin
```

This will start the admin portal at http://localhost:3001.

## Building

To build the admin portal:

```bash
npm run build
```

This will create a production build in the `build` directory.

## Features

- Dashboard with key metrics
- Stylist management
- Service management
- Appointment tracking
- Settings configuration

## Authentication

The admin portal uses JWT authentication. To log in, use the credentials provided by the administrator.
