/* Main Styles for GetTwisted Hair Studios */

body { 
  font-family: 'Fredoka One', cursive; 
}

.animate-slide-in {
  animation: slideIn 0.3s ease-out forwards;
}

@keyframes slideIn {
  0% { transform: translateY(100%); opacity: 0; }
  100% { transform: translateY(0); opacity: 1; }
}

/* Theme Styling */
body.theme-vibrant { background-color: #f472b6; color: white; }
body.theme-black-white { background-color: #000; color: #fff; }
body.theme-matrix { background-color: #000; color: #00ff00; }
body.theme-black-white header, body.theme-black-white section,
body.theme-matrix header, body.theme-matrix section {
  background-color: #000 !important;
}
body.theme-black-white .footer-copy,
body.theme-matrix .footer-copy { color: #aaa; }
body.theme-vibrant .footer-copy { color: #f9a8d4; }

.hero-section {
  transition: background 0.3s ease;
}
body.theme-vibrant .hero-section {
  background: linear-gradient(to bottom right, #fb923c, #fde047);
}
body.theme-black-white .hero-section,
body.theme-matrix .hero-section {
  background: #000;
}
/* Headings + titles */
.theme-text {
  transition: color 0.3s ease;
}

body.theme-vibrant .theme-text {
  color: white;
}

body.theme-black-white .theme-text {
  color: #fff;
}

body.theme-matrix .theme-text {
  color: #00ff00;
}

/* Optional for description/subheadings */
.theme-subtext {
  transition: color 0.3s ease;
}

body.theme-vibrant .theme-subtext {
  color: #fde68a;
}

body.theme-black-white .theme-subtext {
  color: #aaa;
}

body.theme-matrix .theme-subtext {
  color: #66ff66;
}
