# GetTwisted Hair Studios - Frontend

This is the frontend component of the GetTwisted Hair Studios website.

## Directory Structure

- `public/` - Static files served directly to clients
  - `assets/` - Images, data files, and other assets
  - `css/` - Compiled CSS files
  - `js/` - JavaScript files
  - HTML files for each page
- `src/` - Source code
  - `styles/` - CSS source files
  - `js/` - JavaScript source files

## Development

To start the development server:

```bash
npm run dev
```

This will start a local server at http://localhost:3000.

## Building

To build the frontend:

```bash
node src/build.js
```

This will copy all CSS and JS files from the `src` directory to the `public` directory.

## Deployment

The frontend is deployed to GitHub Pages. To deploy:

1. Build the frontend
2. Commit and push changes to the `main` branch
3. GitHub Actions will automatically deploy the site
