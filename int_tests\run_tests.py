"""
<PERSON><PERSON><PERSON> to run all integration tests.
"""
import os
import sys
import subprocess
import argparse

def run_tests(test_file=None, verbose=False):
    """
    Run the integration tests.
    
    Args:
        test_file: Specific test file to run (optional)
        verbose: Whether to run tests in verbose mode
    """
    # Build the command
    cmd = ["python", "-m", "pytest"]
    
    # Add verbose flag if requested
    if verbose:
        cmd.append("-v")
    
    # Add HTML report generation
    cmd.extend(["--html=report.html", "--self-contained-html"])
    
    # Add specific test file if provided
    if test_file:
        cmd.append(test_file)
    
    # Run the tests
    subprocess.run(cmd)

def main():
    """Main function to parse arguments and run tests."""
    parser = argparse.ArgumentParser(description="Run integration tests for GetTwisted Hair Studios website.")
    parser.add_argument("--file", help="Specific test file to run (e.g., test_gallery.py)")
    parser.add_argument("--verbose", "-v", action="store_true", help="Run tests in verbose mode")
    
    args = parser.parse_args()
    
    # Run the tests
    run_tests(args.file, args.verbose)

if __name__ == "__main__":
    main()
